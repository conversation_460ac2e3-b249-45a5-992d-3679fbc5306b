//
//  SharedDataManager.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/1.
//

import Foundation
import SQLite

/// Shared data manager for accessing journal data from the widget extension
class SharedDataManager {
    static let shared = SharedDataManager()
    
    private let appGroupIdentifier = "group.cn.seungyu.journal"
    private var connection: Connection?
    
    private init() {
        setupDatabase()
    }
    
    // MARK: - Database Setup
    
    private func setupDatabase() {
        guard let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier),
              let documentsURL = containerURL.appendingPathComponent("Documents") as URL? else {
            print("Failed to get shared container URL")
            return
        }
        
        let databaseURL = documentsURL.appendingPathComponent("db.sqlite3")
        
        do {
            connection = try Connection(databaseURL.path)
        } catch {
            print("Failed to connect to shared database: \(error)")
        }
    }
    
    // MARK: - Data Access
    
    /// Get recent journal entries for widget display
    func getRecentJournalEntries(limit: Int = 5) -> [WidgetJournalEntry] {
        guard let connection = connection else { return [] }
        
        let journals = Table("journals")
        let journalId = Expression<String>("journal_id")
        let journalDate = Expression<Date>("date")
        let journalMoodEmoji = Expression<String>("mood_emoji")
        let journalContentSnippet = Expression<String>("content_snippet")
        let journalTitle = Expression<String>("title")
        let journalCreated = Expression<Date>("created")
        
        do {
            let query = journals
                .select(journalId, journalDate, journalMoodEmoji, journalContentSnippet, journalTitle, journalCreated)
                .order(journalCreated.desc)
                .limit(limit)
            
            var entries: [WidgetJournalEntry] = []
            
            for row in try connection.prepare(query) {
                let entry = WidgetJournalEntry(
                    id: row[journalId],
                    date: row[journalDate],
                    moodEmoji: row[journalMoodEmoji],
                    contentSnippet: row[journalContentSnippet],
                    title: parseTitle(from: row[journalTitle]),
                    createdAt: row[journalCreated]
                )
                entries.append(entry)
            }
            
            return entries
        } catch {
            print("Failed to fetch journal entries: \(error)")
            return []
        }
    }
    
    /// Get journal entry count
    func getJournalEntryCount() -> Int {
        guard let connection = connection else { return 0 }
        
        let journals = Table("journals")
        
        do {
            return try connection.scalar(journals.count)
        } catch {
            print("Failed to get journal entry count: \(error)")
            return 0
        }
    }
    
    /// Get most recent journal entry
    func getMostRecentEntry() -> WidgetJournalEntry? {
        let entries = getRecentJournalEntries(limit: 1)
        return entries.first
    }
    
    /// Get entries for a specific date
    func getEntriesForDate(_ date: Date) -> [WidgetJournalEntry] {
        guard let connection = connection else { return [] }
        
        let journals = Table("journals")
        let journalId = Expression<String>("journal_id")
        let journalDate = Expression<Date>("date")
        let journalMoodEmoji = Expression<String>("mood_emoji")
        let journalContentSnippet = Expression<String>("content_snippet")
        let journalTitle = Expression<String>("title")
        let journalCreated = Expression<Date>("created")
        
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        do {
            let query = journals
                .select(journalId, journalDate, journalMoodEmoji, journalContentSnippet, journalTitle, journalCreated)
                .filter(journalDate >= startOfDay && journalDate < endOfDay)
                .order(journalCreated.desc)
            
            var entries: [WidgetJournalEntry] = []
            
            for row in try connection.prepare(query) {
                let entry = WidgetJournalEntry(
                    id: row[journalId],
                    date: row[journalDate],
                    moodEmoji: row[journalMoodEmoji],
                    contentSnippet: row[journalContentSnippet],
                    title: parseTitle(from: row[journalTitle]),
                    createdAt: row[journalCreated]
                )
                entries.append(entry)
            }
            
            return entries
        } catch {
            print("Failed to fetch entries for date: \(error)")
            return []
        }
    }
    
    // MARK: - Helper Methods
    
    private func parseTitle(from titleJSON: String) -> String {
        guard let data = titleJSON.data(using: .utf8),
              let titleItem = try? JSONDecoder().decode(ContentItem.self, from: data) else {
            return "Untitled"
        }
        
        // Extract plain text from HTML content
        return titleItem.content.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
    }
}

// MARK: - Widget Data Models

struct WidgetJournalEntry: Identifiable, Codable {
    let id: String
    let date: Date
    let moodEmoji: String
    let contentSnippet: String
    let title: String
    let createdAt: Date
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
    
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
}
