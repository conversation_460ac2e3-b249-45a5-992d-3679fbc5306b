//
//  XYZWidget.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/1.
//

import WidgetKit
import SwiftUI

struct Provider: AppIntentTimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        let placeholderPrompt = DailyPrompt(
            emoji: "✨",
            prompt: "What made you smile today?",
            category: .gratitude
        )
        let placeholderData = WidgetDisplayData(
            streak: 5,
            streakMessage: "5 days strong! 💪",
            streakEmoji: "💪",
            motivationalMessage: "Your thoughts matter! 💭",
            hasWrittenToday: false,
            totalEntries: 12
        )
        return SimpleEntry(date: Date(), configuration: ConfigurationAppIntent(), dailyPrompt: placeholderPrompt, widgetData: placeholderData)
    }

    func snapshot(for configuration: ConfigurationAppIntent, in context: Context) async -> SimpleEntry {
        let prompt = DailyPromptManager.shared.getTodaysPrompt(category: configuration.promptCategory)
        let widgetData = WidgetDataManager.shared.getWidgetDisplayData()
        return SimpleEntry(date: Date(), configuration: configuration, dailyPrompt: prompt, widgetData: widgetData)
    }

    func timeline(for configuration: ConfigurationAppIntent, in context: Context) async -> Timeline<SimpleEntry> {
        let currentDate = Date()
        let prompt = DailyPromptManager.shared.getTodaysPrompt(category: configuration.promptCategory)
        let widgetData = WidgetDataManager.shared.getWidgetDisplayData()

        // Create entry for today
        let entry = SimpleEntry(date: currentDate, configuration: configuration, dailyPrompt: prompt, widgetData: widgetData)

        // Update at midnight for new daily prompt
        let calendar = Calendar.current
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        let nextMidnight = calendar.startOfDay(for: tomorrow)

        return Timeline(entries: [entry], policy: .after(nextMidnight))
    }

//    func relevances() async -> WidgetRelevances<ConfigurationAppIntent> {
//        // Generate a list containing the contexts this widget is relevant in.
//    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let configuration: ConfigurationAppIntent
    let dailyPrompt: DailyPrompt
    let widgetData: WidgetDisplayData
}

struct XYZWidgetEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            SmallWidgetView(entry: entry)
        case .systemMedium:
            MediumWidgetView(entry: entry)
        default:
            SmallWidgetView(entry: entry)
        }
    }
}

struct SmallWidgetView: View {
    let entry: Provider.Entry

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Clean background with subtle accent
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)

                // Accent border
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color(hex: entry.dailyPrompt.category.color).opacity(0.3), lineWidth: 2)

                VStack(spacing: 8) {
                    // Header with emoji and streak
                    HStack {
                        Text(entry.dailyPrompt.emoji)
                            .font(.title)
                            .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)

                        Spacer()

                        if entry.widgetData.streak > 0 {
                            HStack(spacing: 3) {
                                Text(entry.widgetData.streakEmoji)
                                    .font(.caption)
                                Text("\(entry.widgetData.streak)")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color(hex: entry.dailyPrompt.category.color))
                            )
                        }
                    }

                    // Category badge
                    Text(entry.dailyPrompt.category.rawValue.uppercased())
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(Color(hex: entry.dailyPrompt.category.color).opacity(0.15))
                        )

                    Spacer(minLength: 4)

                    // Main prompt text
                    Text(entry.dailyPrompt.prompt)
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                        .foregroundColor(.primary)
                        .padding(.horizontal, 8)
                        .fixedSize(horizontal: false, vertical: true)

                    Spacer(minLength: 4)

                    // Call to action with status
                    VStack(spacing: 3) {
                        HStack(spacing: 4) {
                            Image(systemName: entry.widgetData.hasWrittenToday ? "checkmark.circle.fill" : "pencil.circle.fill")
                                .foregroundColor(entry.widgetData.hasWrittenToday ? .green : Color(hex: entry.dailyPrompt.category.color))
                                .font(.caption)

                            Text(entry.widgetData.hasWrittenToday ? "Written today!" : "Tap to write")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(entry.widgetData.hasWrittenToday ? .green : Color(.secondaryLabel))
                        }

                        if !entry.widgetData.hasWrittenToday && entry.widgetData.streak > 0 {
                            Text("Keep your streak alive!")
                                .font(.caption2)
                                .foregroundColor(.orange)
                                .fontWeight(.medium)
                        }
                    }
                }
                .padding(16)
            }
        }
    }
}

struct MediumWidgetView: View {
    let entry: Provider.Entry

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Clean background
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)

                // Accent border
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color(hex: entry.dailyPrompt.category.color).opacity(0.3), lineWidth: 2)

                HStack(spacing: 20) {
                    // Left side - Emoji, stats, and category
                    VStack(spacing: 12) {
                        Text(entry.dailyPrompt.emoji)
                            .font(.system(size: 44))
                            .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)

                        // Streak info
                        if entry.widgetData.streak > 0 {
                            VStack(spacing: 3) {
                                HStack(spacing: 4) {
                                    Text(entry.widgetData.streakEmoji)
                                        .font(.caption)
                                    Text("\(entry.widgetData.streak)")
                                        .font(.title3)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                }
                                Text("day streak")
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.9))
                                    .fontWeight(.medium)
                            }
                            .padding(.horizontal, 10)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(hex: entry.dailyPrompt.category.color))
                            )
                        }

                        Text(entry.dailyPrompt.category.rawValue.uppercased())
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color(hex: entry.dailyPrompt.category.color).opacity(0.15))
                            )

                        Spacer()

                        // Stats
                        VStack(spacing: 2) {
                            Text("\(entry.widgetData.totalEntries)")
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                            Text("entries")
                                .font(.caption2)
                                .foregroundColor(Color(.secondaryLabel))
                                .fontWeight(.medium)
                        }
                    }
                    .frame(width: 90)

                    // Right side - Prompt and action
                    VStack(alignment: .leading, spacing: 14) {
                        // Motivational message
                        Text(entry.widgetData.motivationalMessage)
                            .font(.caption)
                            .foregroundColor(Color(.secondaryLabel))
                            .italic()

                        Text(entry.dailyPrompt.prompt)
                            .font(.system(size: 17, weight: .medium, design: .rounded))
                            .lineLimit(nil)
                            .foregroundColor(.primary)
                            .fixedSize(horizontal: false, vertical: true)

                        Spacer()

                        // Status and action
                        VStack(alignment: .leading, spacing: 8) {
                            if entry.widgetData.hasWrittenToday {
                                HStack(spacing: 6) {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                        .font(.system(size: 16))
                                    Text("You've written today!")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.green)
                                }
                                .padding(.horizontal, 14)
                                .padding(.vertical, 10)
                                .background(
                                    RoundedRectangle(cornerRadius: 22)
                                        .fill(Color.green.opacity(0.12))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 22)
                                                .stroke(Color.green.opacity(0.3), lineWidth: 1)
                                        )
                                )
                            } else {
                                HStack(spacing: 6) {
                                    Image(systemName: "pencil.circle.fill")
                                        .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                                        .font(.system(size: 16))

                                    Text("Start writing in Journal")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                                }
                                .padding(.horizontal, 14)
                                .padding(.vertical, 10)
                                .background(
                                    RoundedRectangle(cornerRadius: 22)
                                        .fill(Color(hex: entry.dailyPrompt.category.color).opacity(0.12))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 22)
                                                .stroke(Color(hex: entry.dailyPrompt.category.color).opacity(0.3), lineWidth: 1)
                                        )
                                )

                                if entry.widgetData.streak > 0 {
                                    Text("Keep your \(entry.widgetData.streak)-day streak alive! 🔥")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                        .fontWeight(.semibold)
                                }
                            }
                        }
                    }
                }
                .padding(20)
            }
        }
    }
}

struct XYZWidget: Widget {
    let kind: String = "DailyPromptWidget"

    var body: some WidgetConfiguration {
        AppIntentConfiguration(kind: kind, intent: ConfigurationAppIntent.self, provider: Provider()) { entry in
            XYZWidgetEntryView(entry: entry)
                .containerBackground(for: .widget) {
                    // Subtle gradient background
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: entry.dailyPrompt.category.color).opacity(0.05),
                            Color(hex: entry.dailyPrompt.category.color).opacity(0.02)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                }
                .widgetURL(URL(string: "journal://new-entry?prompt=\(entry.dailyPrompt.prompt.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")"))
        }
        .configurationDisplayName("Daily Journal Prompt")
        .description("Get inspired with a new writing prompt every day!")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

#Preview(as: .systemSmall) {
    XYZWidget()
} timeline: {
    SimpleEntry(
        date: .now,
        configuration: ConfigurationAppIntent(),
        dailyPrompt: DailyPrompt(emoji: "✨", prompt: "What made you smile today?", category: .gratitude),
        widgetData: WidgetDisplayData(streak: 7, streakMessage: "7 day streak! 🔥", streakEmoji: "🔥", motivationalMessage: "Your thoughts matter! 💭", hasWrittenToday: false, totalEntries: 23)
    )
    SimpleEntry(
        date: .now,
        configuration: ConfigurationAppIntent(),
        dailyPrompt: DailyPrompt(emoji: "🎨", prompt: "If you could create anything, what would it be?", category: .creativity),
        widgetData: WidgetDisplayData(streak: 12, streakMessage: "12 day streak! 🔥", streakEmoji: "🔥", motivationalMessage: "Express yourself freely 🎨", hasWrittenToday: true, totalEntries: 45)
    )
    SimpleEntry(
        date: .now,
        configuration: ConfigurationAppIntent(),
        dailyPrompt: DailyPrompt(emoji: "🌟", prompt: "What are three things you're grateful for right now?", category: .gratitude),
        widgetData: WidgetDisplayData(streak: 0, streakMessage: "Start your journey! ✨", streakEmoji: "✨", motivationalMessage: "Every story starts with a single word ✍️", hasWrittenToday: false, totalEntries: 0)
    )
}

#Preview(as: .systemMedium) {
    XYZWidget()
} timeline: {
    SimpleEntry(
        date: .now,
        configuration: ConfigurationAppIntent(),
        dailyPrompt: DailyPrompt(emoji: "🚀", prompt: "What's your wildest dream for the future?", category: .creativity),
        widgetData: WidgetDisplayData(streak: 15, streakMessage: "15 days amazing! 🌟", streakEmoji: "🌟", motivationalMessage: "Make today memorable 🌟", hasWrittenToday: false, totalEntries: 67)
    )
    SimpleEntry(
        date: .now,
        configuration: ConfigurationAppIntent(),
        dailyPrompt: DailyPrompt(emoji: "🌈", prompt: "What's the best compliment you received recently?", category: .gratitude),
        widgetData: WidgetDisplayData(streak: 3, streakMessage: "3 days strong! 💪", streakEmoji: "💪", motivationalMessage: "Capture this moment 📸", hasWrittenToday: true, totalEntries: 12)
    )
}
