# 意日记 - 记日记的iOS应用

## 简介

旨在打造一款简约而温馨的日记应用，通过精心设计的界面和流畅的用户体验，鼓励用户养成记录日记的习惯。应用采用柔和的色彩和简洁的界面元素，营造宁静的氛围，让用户能够专注于记录自己的思想和感受。

### 主要功能

### 技术实现

- 采用MVVM架构
- 使用Combine进行数据绑定
- SnapKit进行UI布局
- Alamofire处理网络请求
- PresentationManager统一管理视图控制器展示
- 密码保护功能确保数据安全

### 安装与运行

1. 克隆仓库
2. 安装依赖：`pod install`
3. 打开生成的`.xcworkspace`文件
4. 构建并运行项目

## 开发指南

### 添加新功能

1. 在`Features`目录下创建相应模块
2. 遵循MVVM架构：Model、ViewModel、View、ViewController
3. 使用Configurator进行依赖注入

### 代码规范

- 使用Swift风格指南
- 添加适当的注释
- 确保支持深色模式
- 使用本地化字符串
- 使用PresentationManager进行视图控制器展示
- 遵循安全最佳实践

## 依赖库

- SnapKit: UI布局
- Alamofire: 网络请求
- Kingfisher: 图片加载

## 架构特性

### PresentationManager - 视图控制器展示管理

为了解决视图控制器展示冲突问题，我们实现了一个统一的展示管理系统：

#### 主要功能
- **冲突解决**: 自动处理多个视图控制器同时展示的冲突
- **优先级系统**: 重要的界面（如密码保护）优先展示
- **队列管理**: 按优先级排队展示视图控制器
- **安全展示**: 防止应用崩溃和异常行为

#### 使用方法
```swift
// 基本使用
self.presentSafely(viewController)

// 设置优先级
self.presentSafely(viewController, priority: .high)

// 带完成回调
self.presentSafely(viewController) {
    // 展示完成后的操作
}
```

#### 优先级说明
- `.critical`: 关键界面（登录界面）
- `.high`: 重要界面（密码保护）
- `.normal`: 普通界面（默认）
- `.low`: 低优先级界面

### 密码保护系统

应用集成了完整的密码保护功能：
- 本地密码存储（SHA256加密）
- 应用启动时自动检查
- 与生物识别认证集成
- 完整的密码管理界面

本设计方案旨在打造一款简约而温馨的日记应用，通过精心设计的界面和流畅的用户体验，鼓励用户养成记录日记的习惯。应用采用柔和的色彩和简洁的界面元素，营造宁静的氛围，让用户能够专注于记录自己的思想和感受。

## 颜色

| 用途 | 颜色代码 | 预览 |
|------|----------|-------|
| 主色 | #3498db | ![](https://via.placeholder.com/15/3498db/3498db.png) |
| 辅助色 | #2ecc71 | ![](https://via.placeholder.com/15/2ecc71/2ecc71.png) |
| 强调色 | #f39c12 | ![](https://via.placeholder.com/15/f39c12/f39c12.png) |
| 警告色 | #e74c3c | ![](https://via.placeholder.com/15/e74c3c/e74c3c.png) |
| 文本色 | #34495e | ![](https://via.placeholder.com/15/34495e/34495e.png) |

## 首页

    - 最近日记
    - 开始记日记
    - 日记列表入口
        - 日记总数
        - 日记日历
        - 选择查看某天的日历

## Profile

    - 编辑个人资料
        - First Name
        - Last Name
        - Gender
        - Bio
    - 设置入口
        - 主题切换
            - 黑暗
            - 浅色
            - 系统
        - 退出登录
    - 关于APP
