# Uncomment the next line to define a global platform for your project
platform :ios, '16.0'

target 'XYZ' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for XYZ
  pod 'Alamofire', '5.10'
  pod 'AlamofireImage'
  pod 'SnapKit', '5.7.1'
  pod 'SwifterSwift'
  pod 'GoogleSignIn'
  pod 'GoogleSignInSwiftSupport'
  pod 'SwiftGen', '~> 6.0'
  pod 'SwiftLint'
  pod 'OnboardKit'
  pod 'JWTDecode', '~> 3.2'
  pod 'CryptoSwift', '1.8.4'
  pod 'SQLite.swift', '~> 0.14.0'
  pod 'KeychainAccess'
  pod 'RevenueCat', '5.20.0'
  pod 'RevenueCatUI'
  pod 'SideMenuSwift'
  pod 'Kingfisher', '~> 8.0'
  pod "Qiniu", "~> 8.8.1"
  pod 'Google-Mobile-Ads-SDK'

  # Optional: Charts for statistics
#  pod 'Charts', '~> 4.0.0'

  target 'XYZTests' do
    inherit! :search_paths
    # Pods for testing
  end

  target 'XYZUITests' do
    # Pods for testing
  end

  
  post_install do |installer|
      installer.generated_projects.each do |project|
          project.targets.each do |target|
              target.build_configurations.each do |config|
                  config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
              end
          end
      end
  end
  
end
