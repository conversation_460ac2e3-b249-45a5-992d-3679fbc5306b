platform :ios, '16.0'

# Define a shared abstract target for all common pods
abstract_target 'XYZCommon' do
  use_frameworks!

  # Shared Pods for all targets
  pod 'SQLite.swift', '~> 0.14.0'

  target 'XYZ' do
    # Main app target
    pod 'Alamofire', '5.10'
    pod 'AlamofireImage'
    pod 'SnapKit', '5.7.1'
    pod 'SwifterSwift'
    pod 'GoogleSignIn'
    pod 'GoogleSignInSwiftSupport'
    pod 'SwiftGen', '~> 6.0'
    pod 'SwiftLint'
    pod 'OnboardKit'
    pod 'JWTDecode', '~> 3.2'
    pod 'CryptoSwift', '1.8.4'
    pod 'SQLite.swift', '~> 0.14.0'
    pod 'KeychainAccess'
    pod 'RevenueCat', '5.20.0'
    pod 'RevenueCatUI'
    pod 'SideMenuSwift'
    pod 'Kingfisher', '~> 8.0'
    pod 'Qiniu', '~> 8.8.1'
    pod 'Google-Mobile-Ads-SDK'
  
  end

  target 'XYZWidgetExtension' do
    # Already inherits all common pods (including SQLite.swift)
  end

  target 'XYZTests' do
    inherit! :search_paths
    # Additional pods for tests (if any)
  end

  target 'XYZUITests' do
    # Additional pods for UI tests (if any)
  end

end

post_install do |installer|
  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
      end
    end
  end
end
