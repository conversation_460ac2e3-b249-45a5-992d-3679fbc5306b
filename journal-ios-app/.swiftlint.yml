# By default, SwiftLint uses a set of sensible default rules you can adjust:
disabled_rules: # rule identifiers turned on by default to exclude from running
  - colon
  - comma
  - control_statement
  - force_cast
  - multiple_closures_with_trailing_closure
  - large_tuple
  - line_length

opt_in_rules: # some rules are turned off by default, so you need to opt-in
  - empty_count # find all the available rules by running: `swiftlint rules`
  - indentation_width

# Alternatively, specify all rules explicitly by uncommenting this option:
# only_rules: # delete `disabled_rules` & `opt_in_rules` if using this
#   - empty_parameters
#   - vertical_whitespace

analyzer_rules: # rules run by `swiftlint analyze`
  - explicit_self

# Case-sensitive paths to include during linting. Directory paths supplied on the
# command line will be ignored.
included:
  - XYZ
excluded: # case-sensitive paths to ignore during linting. Takes precedence over `included`
  - Pods

# If true, SwiftLint will not fail if no lintable files are found.
allow_zero_lintable_files: false

# If true, SwiftLint will treat all warnings as errors.
strict: false

# If true, SwiftLint will treat all errors as warnings.
lenient: false

# configurable rules can be customized from this configuration file
# binary rules can set their severity level
force_try:
  severity: warning # explicitly
# rules that have both warning and error levels, can set just the warning level
# implicitly
line_length: 150
# they can set both implicitly with an array
type_body_length:
  - 1000 # warning
  - 1500 # error
# or they can set both explicitly
file_length:
  warning: 1000
  error: 1200
# naming rules can set warnings/errors for min_length and max_length
# additionally they can set excluded names
type_name:
  min_length: 2 # only warning
  max_length: # warning and error
    warning: 40
    error: 50
  excluded:  # excluded via string
    - iPhone
    - T
  allowed_symbols: ["_"] # these are allowed in type names
identifier_name:
  min_length: # only min_length
    error: 1 # only error
  excluded: # excluded via string array
    - id
    - URL
    - GlobalAPIKey
    - ScreenSwith
    - db
    - t
    - T
    - ScreenHeight
    - ScreenWidth
reporter: "xcode" # reporter type (xcode, json, csv, checkstyle, codeclimate, junit, html, emoji, sonarqube, markdown, github-actions-logging, summary)