PODS:
  - Alamofire (5.10.0)
  - AlamofireImage (4.3.0):
    - Alamofire (~> 5.8)
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - CryptoSwift (1.8.4)
  - Google-Mobile-Ads-SDK (12.5.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleSignInSwiftSupport (8.0.0):
    - GoogleSignIn (~> 8.0)
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - HappyDNS (1.0.4)
  - JWTDecode (3.2.0)
  - KeychainAccess (4.2.2)
  - Kingfisher (8.3.1)
  - OnboardKit (1.5.0)
  - PromisesObjC (2.4.0)
  - Qiniu (8.8.1):
    - HappyDNS (~> 1.0.4)
  - RevenueCat (5.20.0)
  - RevenueCatUI (5.20.0):
    - RevenueCat (= 5.20.0)
  - SideMenuSwift (2.1.1)
  - SnapKit (5.7.1)
  - SQLite.swift (0.14.1):
    - SQLite.swift/standard (= 0.14.1)
  - SQLite.swift/standard (0.14.1)
  - SwifterSwift (7.0.0):
    - SwifterSwift/AppKit (= 7.0.0)
    - SwifterSwift/CoreAnimation (= 7.0.0)
    - SwifterSwift/CoreGraphics (= 7.0.0)
    - SwifterSwift/CoreLocation (= 7.0.0)
    - SwifterSwift/CryptoKit (= 7.0.0)
    - SwifterSwift/Dispatch (= 7.0.0)
    - SwifterSwift/Foundation (= 7.0.0)
    - SwifterSwift/HealthKit (= 7.0.0)
    - SwifterSwift/MapKit (= 7.0.0)
    - SwifterSwift/SceneKit (= 7.0.0)
    - SwifterSwift/SpriteKit (= 7.0.0)
    - SwifterSwift/StoreKit (= 7.0.0)
    - SwifterSwift/SwiftStdlib (= 7.0.0)
    - SwifterSwift/UIKit (= 7.0.0)
    - SwifterSwift/WebKit (= 7.0.0)
  - SwifterSwift/AppKit (7.0.0)
  - SwifterSwift/CoreAnimation (7.0.0)
  - SwifterSwift/CoreGraphics (7.0.0)
  - SwifterSwift/CoreLocation (7.0.0)
  - SwifterSwift/CryptoKit (7.0.0)
  - SwifterSwift/Dispatch (7.0.0)
  - SwifterSwift/Foundation (7.0.0)
  - SwifterSwift/HealthKit (7.0.0)
  - SwifterSwift/MapKit (7.0.0)
  - SwifterSwift/SceneKit (7.0.0)
  - SwifterSwift/SpriteKit (7.0.0)
  - SwifterSwift/StoreKit (7.0.0)
  - SwifterSwift/SwiftStdlib (7.0.0)
  - SwifterSwift/UIKit (7.0.0)
  - SwifterSwift/WebKit (7.0.0)
  - SwiftGen (6.6.3)
  - SwiftLint (0.58.2)

DEPENDENCIES:
  - Alamofire (= 5.10)
  - AlamofireImage
  - CryptoSwift (= 1.8.4)
  - Google-Mobile-Ads-SDK
  - GoogleSignIn
  - GoogleSignInSwiftSupport
  - JWTDecode (~> 3.2)
  - KeychainAccess
  - Kingfisher (~> 8.0)
  - OnboardKit
  - Qiniu (~> 8.8.1)
  - RevenueCat (= 5.20.0)
  - RevenueCatUI
  - SideMenuSwift
  - SnapKit (= 5.7.1)
  - SQLite.swift (~> 0.14.0)
  - SwifterSwift
  - SwiftGen (~> 6.0)
  - SwiftLint

SPEC REPOS:
  trunk:
    - Alamofire
    - AlamofireImage
    - AppAuth
    - AppCheckCore
    - CryptoSwift
    - Google-Mobile-Ads-SDK
    - GoogleSignIn
    - GoogleSignInSwiftSupport
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - HappyDNS
    - JWTDecode
    - KeychainAccess
    - Kingfisher
    - OnboardKit
    - PromisesObjC
    - Qiniu
    - RevenueCat
    - RevenueCatUI
    - SideMenuSwift
    - SnapKit
    - SQLite.swift
    - SwifterSwift
    - SwiftGen
    - SwiftLint

SPEC CHECKSUMS:
  Alamofire: cd0b98508df05796dd2ff278f3bb055a631b5390
  AlamofireImage: 843953fa97bee5f561cf05d83abd759e590b068d
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  Google-Mobile-Ads-SDK: 6df9aadcee32bce0ff05bcad56ef2c88a4a5e82b
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleSignInSwiftSupport: de6a0f0c2f0f7420d77a58275fbef7c7f4fcf3c2
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  HappyDNS: aefbd28cdcda93cffac64013dfe3342a2f87ed0a
  JWTDecode: 7dae24cb9bf9b608eae61e5081029ec169bb5527
  KeychainAccess: c0c4f7f38f6fc7bbe58f5702e25f7bd2f65abf51
  Kingfisher: 3204d23de16b5ea53541c44ca5a8efb55741dec3
  OnboardKit: 73a4e9c9a1ae88afee0514ac3f2bc4f82126b66e
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  Qiniu: 37131e52fdf43fe70da0c89dd0df3c9ce25bb74b
  RevenueCat: e47ff90dfd271fde155a4befe48e8fc02e8281e4
  RevenueCatUI: aa9362b300c9025d30a1b36a5cd396d0313fc8ea
  SideMenuSwift: 4dc9c94ffd8874b8331c0d5e316928385add9150
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  SQLite.swift: 2992550ebf3c5b268bf4352603e3df87d2a4ed72
  SwifterSwift: e9caf990fc72e835432280755d1f4c43f2a483d5
  SwiftGen: 4993cbf71cbc4886f775e26f8d5c3a1188ec9f99
  SwiftLint: 365bcd9ffc83d0deb874e833556d82549919d6cd

PODFILE CHECKSUM: 36c39e5ea811fb1380864243672ace55ae423a4c

COCOAPODS: 1.16.2
