# AdMob Integration for Journal iOS App

This document describes the Google AdMob integration implemented in the journal iOS app.

## Overview

The app now includes Google AdMob banner ads that are displayed within the journal entries list. The implementation ensures that:
- Only one banner ad is shown at most
- Ads appear after every 5 journal entries
- Ads are only shown in the first section (most recent year)
- Test ads are used in debug mode, production ads in release mode

## Files Added/Modified

### New Files
1. `XYZ/Components/AdMobBannerView.swift` - Reusable banner ad component
2. `XYZ/Features/Journal/JournalHome/AdBannerTableViewCell.swift` - Table view cell for banner ads
3. `XYZ/Managers/AdMobManager.swift` - Centralized AdMob configuration manager

### Modified Files
1. `Podfile` - Added Google-Mobile-Ads-SDK dependency
2. `XYZ/Info.plist` - Added GADApplicationIdentifier
3. `XYZ/App/AppDelegate/BaseAppDelegate.swift` - Added AdMob initialization
4. `XYZ/Features/Journal/JournalHome/JournalHomeViewController.swift` - Integrated banner ads

## Configuration

### Test vs Production
- **Debug builds**: Uses Google's test ad unit IDs
- **Release builds**: Uses production ad unit IDs (need to be configured)

### Ad Unit IDs
Current test ad unit IDs are configured in `AdMobManager.swift`:
- Banner Test: `ca-app-pub-****************/**********`
- App ID: `ca-app-pub-****************~**********`

### Production Setup
To use in production:
1. Create an AdMob account at https://admob.google.com
2. Create an app in AdMob console
3. Create banner ad units
4. Replace the production ad unit IDs in `AdMobManager.swift`
5. Replace the GADApplicationIdentifier in `Info.plist`

## Ad Display Logic

- Ads are shown after every 5 journal entries (`adFrequency = 5`)
- Only one ad is displayed per session (`hasShownAd` flag)
- Ads only appear in the first section (most recent year)
- Ad state resets when journal data is refreshed

## Privacy and Compliance

Remember to:
1. Update your privacy policy to mention ad tracking
2. Implement App Tracking Transparency (ATT) if targeting users
3. Comply with GDPR/CCPA requirements for EU/CA users
4. Consider implementing User Messaging Platform (UMP) for consent

## Testing

The integration uses Google's test ad unit IDs in debug mode, so you'll see test ads when running in the simulator or on a debug build. Test ads will show "Test Ad" labels.

## Revenue Optimization

Consider implementing:
- Interstitial ads for better revenue
- Rewarded ads for premium features
- Ad frequency capping
- User segmentation for ad targeting 