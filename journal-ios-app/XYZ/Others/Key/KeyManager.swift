//
//  KeyManager.swift

//
//  Created by <PERSON> on 2025/2/13.
//
import Foundation
import Security

class KeyManager {

    private static let publicKeyFileName = "publicKey"  // Your PEM file name

    // Load the public key from a file in your bundle (e.g., public_key.pem)
    func loadPublicKey() -> Sec<PERSON><PERSON>? {
        guard let path = Bundle.main.path(forResource: KeyManager.publicKeyFileName, ofType: "pem")
        else {
            XYLog("Public key file not found.")
            return nil
        }

        do {
            let pemData = try Data(contentsOf: URL(fileURLWithPath: path))

            // Convert PEM file data to a string
            guard let keyString = String(data: pemData, encoding: .utf8) else {
                XYLog("Failed to convert PEM data to string.")
                return nil
            }

            // Strip the PEM header and footer
            let cleanedKeyString =
                keyString
                .replacingOccurrences(of: "-----BEGIN PUBLIC KEY-----", with: "")
                .replacingOccurrences(of: "-----E<PERSON> PUBLIC KEY-----", with: "")
                .replacingOccurrences(of: "\n", with: "")  // Remove newlines
                .replacingOccurrences(of: "\r", with: "")  // Remove carriage returns
                .trimmingCharacters(in: .whitespacesAndNewlines)

            // Base64 decode the key string to get the DER-encoded key data
            guard let keyData = Data(base64Encoded: cleanedKeyString) else {
                XYLog("Error decoding PEM to DER format. Ensure the PEM content is valid base64.")
                return nil
            }

            // Debug: XYLog the decoded key data
            XYLog("Decoded Key Data: \(keyData as NSData)")

            // Create the public key from the DER-encoded key data
            let options: [NSString: Any] = [
                kSecAttrKeyType as NSString: kSecAttrKeyTypeRSA,
                kSecAttrKeyClass as NSString: kSecAttrKeyClassPublic
            ]

            var error: Unmanaged<CFError>?
            guard
                let publicKey = SecKeyCreateWithData(
                    keyData as CFData, options as CFDictionary, &error)
            else {
                XYLog("Error creating public key: \(error!.takeRetainedValue() as Error)")
                return nil
            }

            return publicKey
        } catch {
            XYLog("Error loading public key: \(error)")
            return nil
        }
    }

    func generateRandomKey() -> Data? {
        var key = Data(count: 16)

        let result = key.withUnsafeMutableBytes { bytes in
            SecRandomCopyBytes(kSecRandomDefault, bytes.count, bytes.baseAddress!)
        }

        if result == errSecSuccess {
            return key
        } else {
            XYLog("Failed to generate random key.")
            return nil
        }
    }

    func encryptRandomKey(randomKey: Data, publicKey: SecKey) -> Data? {
        let algorithm: SecKeyAlgorithm = .rsaEncryptionPKCS1  // Using OAEP for better security

        var error: Unmanaged<CFError>?
        guard
            let encryptedData = SecKeyCreateEncryptedData(
                publicKey, algorithm, randomKey as CFData, &error)
        else {
            XYLog("Error encrypting data: \(error!.takeRetainedValue() as Error)")
            return nil
        }

        return encryptedData as Data
    }
}
