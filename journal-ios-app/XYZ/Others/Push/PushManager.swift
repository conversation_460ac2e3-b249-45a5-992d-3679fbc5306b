//
//  PushManager.swift

//
//  Created by <PERSON> on 2025/1/9.
//

import Alamofire
import GTMAppAuth
import UIKit
import UserNotifications

class PushManager: NSObject, UNUserNotificationCenterDelegate {

    static let shared = PushManager()
    let notificationDelete = NotificationDelegate()
    var token: String?
    var uid: Int64?

    func registerNotification() {
        let center = UNUserNotificationCenter.current()
        center.delegate = notificationDelete
        center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if let error = error {
                XYLog("Failed to request authorization: \(error.localizedDescription)")
            } else if granted {
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            } else {
                XYLog("User denied notification permissions.")
            }
        }
    }

    func handleNotificationRegister(token: String) {
        self.token = token
        guard let uid = App.shared.instance?.uid else {
            return
        }
        registerDeviceToken(token: token, uid: uid)
    }

    func didUserLogin(uid: Int64) {
        self.uid = uid
        guard let token = self.token else {
            return
        }
        registerDeviceToken(token: token, uid: uid)
    }

    func didUserLogout() {
        self.uid = nil
        guard let token = self.token else {
            return
        }
        unregisterDeviceToken(token: token)
    }

    func registerDeviceToken(token: String, uid: Int64) {

        let uuid = KeychainStore().getDeviceId()
        let url = JSONFiles.pushEndpoint
        let accessKey = JSONFiles.pushAccessKey
        let parameters: [String: Any] = [
            "aliases": ["uid": "\(uid)"],
            "platform": "ios",
            "token": token,
            "access_key": accessKey,
            "uuid": uuid
        ]

        // Send the POST request
        AF.request(url, method: .post, parameters: parameters, encoding: JSONEncoding.default)
            .response { response in
                switch response.result {
                case .success(let data):
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        XYLog("Response: \(responseString)")
                    } else {
                        XYLog("No response data")
                    }
                case .failure(let error):
                    XYLog("Error: \(error.localizedDescription)")
                }
            }
    }

    func unregisterDeviceToken(token: String) {
        let url = JSONFiles.pushEndpoint
        let accessKey = JSONFiles.pushAccessKey
        let parameters: [String: Any] = [
            "token": token,
            "access_key": accessKey
        ]

        // Send the DELETE request
        AF.request(url, method: .delete, parameters: parameters, encoding: JSONEncoding.default)
            .response { response in
                switch response.result {
                case .success(let data):
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        XYLog("Response: \(responseString)")
                    } else {
                        XYLog("No response data")
                    }
                case .failure(let error):
                    XYLog("Error: \(error.localizedDescription)")
                }
            }
    }
}

class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    func userNotificationCenter(
        _ center: UNUserNotificationCenter, willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) ->
            Void
    ) {
        // Show the notification even when the app is in the foreground
        completionHandler([.banner, .sound, .badge])
    }

    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        // Handle notification tap
        XYLog("Notification received: \(response.notification.request.content.userInfo)")
        completionHandler()
    }

    func userNotificationCenter(
        _ center: UNUserNotificationCenter, openSettingsFor notification: UNNotification?
    ) {
        //
    }
}
