//
//  BaseView.swift
//  XYZ
//
//  Created by <PERSON> on 2025/6/5.
//

import UIKit

class BaseView: UIView {
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupTraitCollection()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func setupTraitCollection() {
        if #available(iOS 17.0, *) {
            registerForTraitChanges([UITraitUserInterfaceStyle.self], handler: { (self: Self, _: UITraitCollection) in
                self.themeDidChange(interfaceStyle: self.traitCollection.userInterfaceStyle)
            })
        } else {
            // Fallback on earlier versions
        }
    }

    open override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        if #available(iOS 17.0, *) {
            return
        }
        self.themeDidChange(interfaceStyle: self.traitCollection.userInterfaceStyle)
    }

    open func themeDidChange(interfaceStyle: UIUserInterfaceStyle) {
        // override in subclass
    }
}
