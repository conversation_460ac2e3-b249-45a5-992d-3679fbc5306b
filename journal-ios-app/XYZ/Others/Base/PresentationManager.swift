//
//  PresentationManager.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/12.
//

import UIKit
import Combine

/// A centralized manager for handling view controller presentations
/// This ensures consistent presentation behavior and prevents conflicts
class PresentationManager {
    static let shared = PresentationManager()

    private init() {}

    // MARK: - Properties
    private var presentationQueue: [PresentationRequest] = []
    private var isProcessingQueue = false

    // MARK: - Presentation Request
    private struct PresentationRequest {
        let viewController: UIViewController
        let presentingViewController: UIViewController
        let animated: Bool
        let completion: (() -> Void)?
        let priority: PresentationPriority
    }

    enum PresentationPriority: Int, Comparable {
        case low = 0
        case normal = 1
        case high = 2
        case critical = 3

        static func < (lhs: PresentationPriority, rhs: PresentationPriority) -> Bool {
            return lhs.rawValue < rhs.rawValue
        }
    }

    // MARK: - Public Methods

    /// Present a view controller with proper conflict handling
    /// - Parameters:
    ///   - viewController: The view controller to present
    ///   - from: The presenting view controller
    ///   - animated: Whether to animate the presentation
    ///   - priority: The priority of this presentation
    ///   - completion: Completion block called after presentation
    func present(
        _ viewController: UIViewController,
        from presentingViewController: UIViewController,
        animated: Bool = true,
        priority: PresentationPriority = .normal,
        completion: (() -> Void)? = nil
    ) {
        let request = PresentationRequest(
            viewController: viewController,
            presentingViewController: presentingViewController,
            animated: animated,
            completion: completion,
            priority: priority
        )

        // Add to queue and process
        presentationQueue.append(request)
        presentationQueue.sort { $0.priority > $1.priority }

        processQueue()
    }

    /// Dismiss the currently presented view controller
    /// - Parameters:
    ///   - from: The view controller to dismiss from
    ///   - animated: Whether to animate the dismissal
    ///   - completion: Completion block called after dismissal
    func dismiss(
        from viewController: UIViewController,
        animated: Bool = true,
        completion: (() -> Void)? = nil
    ) {
        viewController.dismiss(animated: animated, completion: completion)
    }

    /// Check if a view controller can present (no conflicts)
    /// - Parameter viewController: The view controller to check
    /// - Returns: True if it can present, false otherwise
    func canPresent(from viewController: UIViewController) -> Bool {
        return viewController.presentedViewController == nil
    }

    /// Get the top-most view controller that can present
    /// - Returns: The top-most presentable view controller
    func getTopPresentableViewController() -> UIViewController? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first(where: { $0.isKeyWindow }) else {
            return nil
        }

        return getTopViewController(from: window.rootViewController)
    }

    // MARK: - Private Methods

    private func processQueue() {
        guard !isProcessingQueue, !presentationQueue.isEmpty else { return }

        isProcessingQueue = true

        let request = presentationQueue.removeFirst()

        // Check if the presenting view controller can present
        if canPresent(from: request.presentingViewController) {
            // Present immediately
            request.presentingViewController.present(
                request.viewController,
                animated: request.animated
            ) { [weak self] in
                request.completion?()
                self?.isProcessingQueue = false
                self?.processQueue() // Process next in queue
            }
        } else {
            // Dismiss current presentation first, then present
            dismiss(from: request.presentingViewController, animated: false) { [weak self] in
                request.presentingViewController.present(
                    request.viewController,
                    animated: request.animated
                ) {
                    request.completion?()
                    self?.isProcessingQueue = false
                    self?.processQueue() // Process next in queue
                }
            }
        }
    }

    private func getTopViewController(from viewController: UIViewController?) -> UIViewController? {
        if let presented = viewController?.presentedViewController {
            return getTopViewController(from: presented)
        }

        if let navigationController = viewController as? UINavigationController {
            return getTopViewController(from: navigationController.topViewController)
        }

        if let tabBarController = viewController as? UITabBarController {
            return getTopViewController(from: tabBarController.selectedViewController)
        }

        return viewController
    }
}

// MARK: - UIViewController Extension
extension UIViewController {

    /// Present a view controller using the PresentationManager
    /// - Parameters:
    ///   - viewController: The view controller to present
    ///   - animated: Whether to animate the presentation
    ///   - priority: The priority of this presentation
    ///   - completion: Completion block called after presentation
    func presentSafely(
        _ viewController: UIViewController,
        animated: Bool = true,
        priority: PresentationManager.PresentationPriority = .normal,
        completion: (() -> Void)? = nil
    ) {
        PresentationManager.shared.present(
            viewController,
            from: self,
            animated: animated,
            priority: priority,
            completion: completion
        )
    }

    /// Dismiss using the PresentationManager
    /// - Parameters:
    ///   - animated: Whether to animate the dismissal
    ///   - completion: Completion block called after dismissal
    func dismissSafely(
        animated: Bool = true,
        completion: (() -> Void)? = nil
    ) {
        PresentationManager.shared.dismiss(
            from: self,
            animated: animated,
            completion: completion
        )
    }
}
