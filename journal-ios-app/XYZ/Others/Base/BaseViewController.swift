//
//  BaseViewController.swift

//
//  Created by <PERSON> on 2025/1/9.
//

import Combine
import UIKit

open class BaseViewController: UIViewController, ObservableObject {

    deinit {
        XYLog("\(self) deinit")
    }

    public var hideNavigationBar: Bool = false
    public var showCloseBackButton: Bool = false
    public var largeTitleDisplayMode: UINavigationItem.LargeTitleDisplayMode = .never

    @Published var isLoading = false
    @Published var error: Error?
    @Published var isViewAppear = false
    @Published var isLoadingParts = false

    private var cancellables = Set<AnyCancellable>()
    private let loadingView = UIView()
    private let loadingIndicator = UIActivityIndicatorView(style: .medium)

    let backgroundImageView = UIImageView()

    open override func viewDidLoad() {
        super.viewDidLoad()

        setupBackground()
        setupSkeletonView()
        hideBackButtonTitle()
        handleLoadingAndError()

        if #available(iOS 17.0, *) {
            registerForTraitChanges([UITraitUserInterfaceStyle.self], handler: { (self: Self, _: UITraitCollection) in
                self.themeDidChange(interfaceStyle: self.traitCollection.userInterfaceStyle)
            })
        }

        navigationItem.largeTitleDisplayMode = self.largeTitleDisplayMode
    }

    func hideBackButtonTitle() {

        if self.showCloseBackButton == false {
            // if self is not the first view controller, show back button
            if self.navigationController?.viewControllers.count ?? 0 > 1 {
                self.showCloseBackButton = true
            }
        }

        // if self is first view controller, hide back button title, show close button
        if self.showCloseBackButton {
            let closeButton = UIButton(type: .custom)
            closeButton.setImage(UIImage(named: "back")?.withTintColor(Theme.Colors.textPrimary.dynamicColor()), for: .normal)
            closeButton.imageView?.contentMode = .scaleAspectFill
            closeButton.frame = .init(x: 0, y: 0, width: 22, height: 22)
            navigationItem.leftBarButtonItem = UIBarButtonItem.init(customView: closeButton)
            if self.navigationController?.viewControllers.count ?? 0 == 1 {
                closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
            } else {
                closeButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
            }
        }
    }

    @objc func closeButtonTapped() {
        self.navigationController?.dismiss(animated: true)
    }

    @objc func backButtonTapped() {
        self.navigationController?.popViewController(animated: true)
    }

    open func validate(data: [String: Any]?) -> Self? {
        return self
    }

    // if before iOS 17.0, use this method how to add constraint 
    open override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        if #available(iOS 17.0, *) {
            return
        }
        self.themeDidChange(interfaceStyle: self.traitCollection.userInterfaceStyle)
    }

    open func themeDidChange(interfaceStyle: UIUserInterfaceStyle) {
        hideBackButtonTitle()
    }

    func handleLoadingAndError() {
        self.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                guard let self = self else { return }
                self.loadingView.backgroundColor = Theme.Colors.pageBg.dynamicColor()
                if isLoading {
                    self.showLoadingView()
                } else {
                    self.hideLoadingView()
                }
            }.store(in: &cancellables)

        self.$isLoadingParts
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                guard let self = self else { return }
                self.loadingView.backgroundColor = .clear
                if isLoading {
                    self.showLoadingView()
                } else {
                    self.hideLoadingView()
                }
            }.store(in: &cancellables)

        self.$error
            .compactMap({
                $0
            })
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.showError(error: error)
            }.store(in: &cancellables)
    }

    // MARK: Loading and Error
    func showLoadingView() {
        view.bringSubviewToFront(loadingView)
        loadingView.isHidden = false
        loadingIndicator.startAnimating()
    }

    func hideLoadingView() {
        loadingView.isHidden = true
        loadingIndicator.stopAnimating()
    }

    func beforeHanleError(error: Error) -> Bool {
        return false
    }

    func afterHanleError(error: Error) {}

    func showError(error: Error) {
        if beforeHanleError(error: error) { return }
        if let error = error as? XYError {
            switch error {
            case .raw(let message, _):
                ToastView.showError(message)
            case .networkNotConnected:
                ToastView.showError(XYLocalize.XYLocalize("error_not_connected_to_internet"))
            default:
                ToastView.showError(error.localizedDescription)
            }
        }
        afterHanleError(error: error)
    }

    private func setupSkeletonView() {
        loadingView.backgroundColor = Theme.Colors.pageBg.dynamicColor()
        loadingContainerView().addSubview(loadingView)
        loadingView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalTo(view.safeAreaLayoutGuide)
        }

        loadingView.addSubview(loadingIndicator)
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(30)
        }
    }

    func errorContainerView(error: Error) -> UIView {
        return self.view
    }

    func loadingContainerView() -> UIView {
        return self.view
    }

    open override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.isViewAppear = true
    }

    open override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        self.isViewAppear = false
    }

    // MARK: - Background
    func setupBackground() {

        view.backgroundColor = Theme.Colors.pageBg.dynamicColor()

        backgroundImageView.contentMode = .scaleAspectFill
        view.addSubview(backgroundImageView)
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}

class ThemedViewContoller: BaseViewController {

    // MARK: - Background
    override func setupBackground() {
        super.setupBackground()
        // backgroundImageView.image = UIImage(named: "lovart-1")

        // Make navigation bar transparent
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
    }
}
