//
//  DB.swift

//
//  Created by <PERSON> on 2025/2/12.
//

import SQLite

class DB {

    static var shared: DB = DB()

    var connection: Connection!
    let users = Table("users")
    let id = Expression<Int64>("id")
    let uid = Expression<Int64>("uid")
    let email = Expression<String>("email")
    let givenName = Expression<String>("given_name")
    let familyName = Expression<String>("family_name")
    let bio = Expression<String>("bio")
    let googleId = Expression<String?>("google_id")
    let appleId = Expression<String?>("apple_id")
    let picture = Expression<String>("picture")

    let tokens = Table("tokens")
    let token = Expression<String>("token")

    let session = Table("session")
    let biometricAuth = Expression<Bool>("biometric_auth")
    let passwordHash = Expression<String?>("password_hash")

    // Journal table
    let journals = Table("journals")
    let journalId = Expression<String>("journal_id")
    let journalDate = Expression<Date>("date")
    let journalMoodEmoji = Expression<String>("mood_emoji")
    let journalContentSnippet = Expression<String>("content_snippet")
    let journalTitle = Expression<String>("title")  // Added title column
    let journalUserId = Expression<Int64>("user_id")
    let journalWeather = Expression<String>("weather")
    let journalTags = Expression<String>("tags")
    let journalUpdated = Expression<Date>("updated")
    let journalLocation = Expression<String>("location")
    let journalCreated = Expression<Date>("created")
    let journalContentItems = Expression<String>("content_items")  // 新增
    let journalTextAlignment = Expression<Int>("text_alignment")  // Added text alignment column
    let journalFontSize = Expression<Int>("font_size")  // Added font size column
    let journalLineHeightMultiple = Expression<Double>("line_height_multiple")  // Added line height multiple column
    let journalTextColor = Expression<String>("text_color")  // Added text color column

    init() {}

    func setup() {
        // Perform data migration if needed before setting up database
        DataMigrationManager.shared.performMigrationIfNeeded()

        // Use shared container path for database
        guard let sharedDatabaseURL = SharedFileManager.shared.sharedDatabaseURL else {
            XYLog("Failed to get shared database URL")
            return
        }

        do {
            // Ensure shared directories exist
            try SharedFileManager.shared.createSharedDirectoriesIfNeeded()

            let db = try Connection(sharedDatabaseURL.path)
            try setUpSchema(db: db)
            connection = db

            // Run migrations after schema setup
            try runMigrations()

            // Update content item paths if migration was just completed
            if DataMigrationManager.shared.isMigrationCompleted {
                try DataMigrationManager.shared.updateContentItemPaths()
            }

        } catch {
            XYLog(error)
        }
    }

    func setUpSchema(db: Connection) throws {
        try db.run(
            users.create(ifNotExists: true) { t in  // CREATE TABLE "users" (
                t.column(id, primaryKey: true)  //     "id" INTEGER PRIMARY KEY NOT NULL,
                t.column(uid, unique: true)  //     "uid" INTEGER UNIQUE NOT NULL,
                t.column(email, unique: true)  //     "email" TEXT UNIQUE NOT NULL,
                t.column(givenName, unique: false)  //     "given_name" TEXT NOT NULL,
                t.column(familyName, unique: false)  //     "family_name" TEXT NOT NULL,
                t.column(bio, unique: false)  //     "family_name" TEXT NOT NULL,
                t.column(googleId, unique: false)  //     "googleId" TEXT NULL,
                t.column(appleId, unique: false)  //     "appleId" TEXT NULL,
                t.column(picture)
            })

        try db.run(
            tokens.create(ifNotExists: true) { t in  // CREATE TABLE "users" (
                t.column(id, primaryKey: true)  //     "id" INTEGER PRIMARY KEY NOT NULL,
                t.column(uid, unique: true)  //     "uid" INTEGER UNIQUE NOT NULL,
                t.column(token, unique: true)  //     "token" TEXT UNIQUE NOT NULL,
            })

        try db.run(
            session.create(ifNotExists: true) { t in  // CREATE TABLE "users" (
                t.column(id, primaryKey: true)  //     "id" INTEGER PRIMARY KEY NOT NULL,
                t.column(uid, unique: true)  //     "uid" INTEGER UNIQUE NOT NULL,
                t.column(biometricAuth, unique: false, defaultValue: false)
                t.column(passwordHash, unique: false)  // password_hash TEXT NULL
            })

        try db.run(
            journals.create(ifNotExists: true) { t in
                t.column(id, primaryKey: true)  // "id" INTEGER PRIMARY KEY NOT NULL
                t.column(journalId, unique: true)  // "journal_id" TEXT UNIQUE NOT NULL
                t.column(journalDate)  // "date" DATE NOT NULL
                t.column(journalMoodEmoji)  // "mood_emoji" TEXT NOT NULL
                t.column(journalContentSnippet)  // "content_snippet" TEXT NOT NULL
                t.column(journalTitle)  // "title" TEXT NOT NULL // Added title column
                t.column(journalUserId)  // "user_id" INTEGER NOT NULL
                t.column(journalWeather)  // "weather" TEXT NOT NULL
                t.column(journalTags)  // "tags" TEXT NOT NULL
                t.column(journalLocation)  // "location" TEXT NOT NULL
                t.column(journalCreated)  // "created" DATE NOT NULL
                t.column(journalUpdated)  // "updated" DATE NOT NULL
                t.column(journalContentItems)  // 新增
                t.column(journalTextAlignment, defaultValue: 0)  // Added text alignment column with default value 0 (left)
                t.column(journalFontSize, defaultValue: 16)  // Added font size column with default value 16
                t.column(journalLineHeightMultiple, defaultValue: 1.5)  // Added line height multiple column with default value 1.5
                t.column(journalTextColor, defaultValue: "#191919")  // Added text color column with default value
            })
    }

    // Run database migrations for schema changes
    // swiftlint:disable:next function_body_length cyclomatic_complexity
    func runMigrations() throws {
        // Check if text_alignment column exists in journals table
        let tableInfo = try connection.prepare("PRAGMA table_info(journals)")
        var hasTextAlignmentColumn = false
        var hasTextColorColumn = false

        for row in tableInfo {
            if let columnName = row[1] as? String {
                if columnName == "text_alignment" {
                    hasTextAlignmentColumn = true
                } else if columnName == "text_color" {
                    hasTextColorColumn = true
                }
            }
        }

        // Add text_alignment column if it doesn't exist
        if !hasTextAlignmentColumn {
            XYLog("Adding text_alignment column to journals table")
            try connection.run("ALTER TABLE journals ADD COLUMN text_alignment INTEGER DEFAULT 0")
        }

        // Check if font_size column exists in journals table
        var hasFontSizeColumn = false

        for row in tableInfo {
            if let columnName = row[1] as? String, columnName == "font_size" {
                hasFontSizeColumn = true
                break
            }
        }

        // Add font_size column if it doesn't exist
        if !hasFontSizeColumn {
            XYLog("Adding font_size column to journals table")
            try connection.run("ALTER TABLE journals ADD COLUMN font_size INTEGER DEFAULT 16")
        }

        // Check if line_height_multiple column exists in journals table
        var hasLineHeightMultipleColumn = false

        for row in tableInfo {
            if let columnName = row[1] as? String, columnName == "line_height_multiple" {
                hasLineHeightMultipleColumn = true
                break
            }
        }

        // Add line_height_multiple column if it doesn't exist
        if !hasLineHeightMultipleColumn {
            XYLog("Adding line_height_multiple column to journals table")
            try connection.run(
                "ALTER TABLE journals ADD COLUMN line_height_multiple REAL DEFAULT 1.5")
            // Add text_color column if it doesn't exist
            if !hasTextColorColumn {
                XYLog("Adding text_color column to journals table")
                try connection.run("ALTER TABLE journals ADD COLUMN text_color TEXT DEFAULT '#191919'")
            }
        }

        // Check if password_hash column exists in session table
        let sessionTableInfo = try connection.prepare("PRAGMA table_info(session)")
        var hasPasswordHashColumn = false

        for row in sessionTableInfo {
            if let columnName = row[1] as? String, columnName == "password_hash" {
                hasPasswordHashColumn = true
                break
            }
        }

        // Add password_hash column if it doesn't exist
        if !hasPasswordHashColumn {
            XYLog("Adding password_hash column to session table")
            try connection.run("ALTER TABLE session ADD COLUMN password_hash TEXT")
        }
    }

    func userWithUid(uid: Int64) -> UserProfile? {
        let query = users.select(users[*]).filter(self.uid == uid)
        if let row = try? connection.pluck(query) {
            let user = UserProfile(
                email: row[self.email],
                firstName: row[self.givenName],
                lastName: row[self.familyName],
                bio: row[self.bio],
                appleId: row[self.appleId],
                googleId: row[self.googleId],
                picture: row[self.picture]
            )
            return user
        }
        return nil
    }

    // swiftlint:disable:next function_body_length
    @discardableResult
    func updateUserWithUid(
        uid: Int64,
        email: String? = nil,
        firstName: String? = nil,
        lastName: String? = nil,
        bio: String? = nil,
        appleId: String? = nil,
        googleId: String? = nil,
        picture: String? = nil
    ) -> Bool {

        let query = users.select(users[*]).filter(self.uid == uid)
        if (try? connection.pluck(query)) != nil {
            var setters = [Setter]()

            if let email = email {
                setters.append(self.email <- email)
            }

            if let email = email {
                setters.append(self.email <- email)
            }

            if let firstName = firstName {
                setters.append(self.givenName <- firstName)
            }

            if let lastName = lastName {
                setters.append(self.familyName <- lastName)
            }

            if let bio = bio {
                setters.append(self.bio <- bio)
            }

            setters.append(self.appleId <- appleId)
            setters.append(self.googleId <- googleId)

            if let picture = picture {
                setters.append(self.picture <- picture)
            }

            let record = self.users.filter(self.uid == uid)
            do {
                return try self.connection.run(record.update(setters)) > 0
            } catch {
                XYLog(error)
                return false
            }
        } else {
            do {
                return try self.connection.run(
                    self.users.insert(
                        self.uid <- uid,
                        self.email <- email ?? "",
                        self.givenName <- firstName ?? "",
                        self.familyName <- lastName ?? "",
                        self.bio <- bio ?? "",
                        self.googleId <- googleId,
                        self.appleId <- appleId,
                        self.picture <- picture ?? ""
                    )
                ) > 0
            } catch {
                XYLog(error)
                return false
            }
        }
    }

    @discardableResult
    func updateTokenWithUid(uid: Int64, token: String) -> Bool {

        let query = self.tokens.select(tokens[*]).filter(self.uid == uid)
        if (try? connection.pluck(query)) != nil {
            var setters = [Setter]()
            setters.append(self.token <- token)

            let record = self.tokens.filter(self.uid == uid)
            do {
                return try self.connection.run(record.update(setters)) > 0
            } catch {
                return false
            }
        } else {
            do {
                return try connection.run(
                    self.tokens.insert(
                        self.token <- token,
                        self.uid <- uid
                    )
                ) > 0
            } catch {
                return false
            }
        }
    }

    func tokenWithUid(uid: Int64) -> Token? {
        let query = tokens.select(self.tokens[*]).filter(self.uid == uid)
        if let row = try? connection.pluck(query) {
            let token = Token(
                token: row[self.token],
                type: "Bearer"
            )
            return token
        }
        return nil
    }

    func setLoginUser(uid: Int64) {
        _ = try? connection.run(
            session.delete()
        )

        _ = try? connection.run(
            session.insert(
                self.uid <- uid
            )
        )
    }

    func logOut() {
        _ = try? connection.run(
            session.delete()
        )
    }

    func loadLoginUser() -> Int64? {
        let max = try? connection.scalar(session.select(self.uid.max))  // -> Int64?
        return max
    }

    func loadUserSession(uid: Int64) -> UserSession? {
        let query = session.select(self.session[*]).filter(self.uid == uid)
        if let row = try? connection.pluck(query) {
            let userSession = UserSession(
                uid: uid,
                biometricAuth: row[self.biometricAuth],
                passwordHash: try? row.get(self.passwordHash)
            )
            return userSession
        }
        return nil
    }

    func setBiometricAuth(uid: Int64, biometricAuth: Bool) {
        // first fetch the current user session
        let currentUserSession = loadUserSession(uid: uid)
        // if current user session is nil, insert a new record
        if currentUserSession == nil {
            _ = try? connection.run(
                session.insert(
                    self.uid <- uid,
                    self.biometricAuth <- biometricAuth
                )
            )
            return
        } else {
            let update = session.filter(self.uid == uid).update(self.biometricAuth <- biometricAuth)
            _ = try? connection.run(
                update
            )
        }
    }

    func setPasswordHash(uid: Int64, passwordHash: String?) {
        // first fetch the current user session
        let currentUserSession = loadUserSession(uid: uid)
        // if current user session is nil, insert a new record
        if currentUserSession == nil {
            _ = try? connection.run(
                session.insert(
                    self.uid <- uid,
                    self.biometricAuth <- true,
                    self.passwordHash <- passwordHash
                )
            )
            return
        } else {
            let update = session.filter(self.uid == uid).update(
                self.passwordHash <- passwordHash,
                self.biometricAuth <- true
            )
            _ = try? connection.run(
                update
            )
        }
    }

    func hasPasswordProtection(uid: Int64) -> Bool {
        let userSession = loadUserSession(uid: uid)
        return userSession?.passwordHash != nil && !(userSession?.passwordHash?.isEmpty ?? true)
    }

    func verifyPassword(uid: Int64, password: String) -> Bool {
        guard let userSession = loadUserSession(uid: uid),
            let storedHash = userSession.passwordHash
        else {
            return false
        }

        // Simple hash comparison (in production, use proper password hashing like bcrypt)
        let inputHash = password.sha256()
        return inputHash == storedHash
    }

    // MARK: - Pattern Lock Methods
    func setPatternHash(uid: Int64, patternHash: String?) {
        // first fetch the current user session
        let currentUserSession = loadUserSession(uid: uid)
        // if current user session is nil, insert a new record
        if currentUserSession == nil {
            _ = try? connection.run(
                session.insert(
                    self.uid <- uid,
                    self.biometricAuth <- true,
                    self.passwordHash <- patternHash
                )
            )
            return
        } else {
            let update = session.filter(self.uid == uid).update(
                self.passwordHash <- patternHash,
                self.biometricAuth <- true
            )
            _ = try? connection.run(
                update
            )
        }
    }

    func hasPatternProtection(uid: Int64) -> Bool {
        guard let userSession = loadUserSession(uid: uid) else { return false }
        return userSession.passwordHash != nil && !(userSession.passwordHash?.isEmpty ?? true) && userSession.biometricAuth
    }

    func verifyPattern(uid: Int64, pattern: String) -> Bool {
        guard let userSession = loadUserSession(uid: uid),
            let storedHash = userSession.passwordHash
        else {
            return false
        }

        // Simple hash comparison (in production, use proper password hashing like bcrypt)
        let inputHash = pattern.sha256()
        return inputHash == storedHash
    }

    // MARK: - Journal Methods

    @discardableResult
    // swiftlint:disable:next function_body_length
    func saveJournalEntry(_ entry: JournalEntry) throws -> Bool {
        let currentUserId = loadLoginUser() ?? 0
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601

        // ContentItems 序列化为 JSON
        let contentItemsJSON: String
        do {
            let data = try encoder.encode(entry.contentItems)
            contentItemsJSON = String(data: data, encoding: .utf8) ?? "[]"
        } catch {
            contentItemsJSON = "[]"
        }

        // ContentItems 序列化为 JSON
        let titleItemJSON: String
        do {
            let data = try encoder.encode(entry.title)
            titleItemJSON = String(data: data, encoding: .utf8) ?? "{}"
        } catch {
            titleItemJSON = "{}"
        }

        // Check if the journal already exists (for updates)
        let query = journals.select(journals[*]).filter(journalId == entry.id)

        if (try? connection.pluck(query)) != nil {
            // Update existing entry
            let setters: [Setter] = [
                journalDate <- entry.date,
                journalMoodEmoji <- entry.moodEmoji,
                journalContentSnippet <- entry.contentSnippet,
                journalTitle <- titleItemJSON,
                journalWeather <- entry.weather,
                journalTags <- entry.tags.joined(separator: ","),
                journalLocation <- entry.location,
                journalDate <- entry.date,
                journalCreated <- entry.createdAt,
                journalUpdated <- entry.updatedAt,
                journalContentItems <- contentItemsJSON,
                journalFontSize <- entry.fontSize,  // Save font size
                journalTextAlignment <- entry.textAlignment,  // Save text alignment
                journalLineHeightMultiple <- entry.lineHeightMultiple,  // Save line height multiple
                journalTextColor <- entry.textColor  // Save text color
            ]

            let record = journals.filter(journalId == entry.id)
            return try connection.run(record.update(setters)) > 0
        } else {
            // Insert new entry
            return try connection.run(
                journals.insert(
                    journalId <- entry.id,
                    journalDate <- entry.date,
                    journalMoodEmoji <- entry.moodEmoji,
                    journalContentSnippet <- entry.contentSnippet,
                    journalUserId <- currentUserId,
                    journalTitle <- titleItemJSON,
                    journalWeather <- entry.weather,
                    journalTags <- entry.tags.joined(separator: ","),
                    journalLocation <- entry.location,
                    journalDate <- entry.date,
                    journalCreated <- entry.createdAt,
                    journalUpdated <- entry.updatedAt,
                    journalContentItems <- contentItemsJSON,  // 新增
                    journalTextAlignment <- entry.textAlignment,  // Save text alignment
                    journalFontSize <- entry.fontSize,  // Save font size
                    journalLineHeightMultiple <- entry.lineHeightMultiple,  // Save line height multiple
                    journalTextColor <- entry.textColor  // Save text color
                )
            ) > 0
        }
    }

    func getLatestJournalEntry() -> JournalEntry? {
        guard let currentUserId = loadLoginUser() else { return nil }

        let query =
            journals
            .select(journals[*])
            .filter(journalUserId == currentUserId)
            .order(journalDate.desc)
            .limit(1)

        if let row = try? connection.pluck(query) {
            return rowToJournal(row: row)
        }

        return nil
    }

    func getJournalEntryById(id: String) -> JournalEntry? {
        let query =
            journals
            .select(journals[*])
            .filter(journalId == id)
        if let row = try? connection.pluck(query) {
            let entry = rowToJournal(row: row)
            return entry
        }
        return nil
    }

    func rowToJournal(row: Row) -> JournalEntry {
        // 反序列化 content_items 字段
        var contentItems: [ContentItem] = []
        var titleItem: ContentItem = ContentItem(id: "", type: .text, content: "")

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601

        if let json = try? row.get(journalContentItems), !json.isEmpty {
            if let data = json.data(using: .utf8),
                let items = try? decoder.decode([ContentItem].self, from: data) {
                contentItems = items
            }
        }

        if let json = try? row.get(journalTitle), !json.isEmpty {
            if let data = json.data(using: .utf8),
                let item = try? decoder.decode(ContentItem.self, from: data) {
                titleItem = item
            }
        }

        // Get text alignment from database, default to 0 (left) if not found
        let textAlignment: Int
        do {
            textAlignment = try row.get(journalTextAlignment)
        } catch {
            textAlignment = 0  // Default to left alignment if column doesn't exist yet
        }

        // Get text color from database, default to "#191919" if not found
        let textColor: String
        do {
            textColor = try row.get(journalTextColor)
        } catch {
            textColor = "#191919"  // Default text color if column doesn't exist yet
        }

        let entry = JournalEntry(
            id: row[journalId],
            title: titleItem,
            contentItems: contentItems,
            contentSnippet: row[journalContentSnippet],
            moodEmoji: row[journalMoodEmoji],
            weather: row[journalWeather],
            tags: row[journalTags].split(separator: ",").map { String($0) },
            location: row[journalLocation],
            fontSize: row[journalFontSize],  // 可根据需要调整
            lineHeightMultiple: row[journalLineHeightMultiple],  // 可根据需要调整
            textAlignment: textAlignment,  // Set text alignment from database
            textColor: textColor,  // Set text color from database
            date: row[journalDate],
            createdAt: row[journalCreated],
            updatedAt: row[journalUpdated]
        )
        return entry
    }

    func getJournalEntries(limit: Int = 10, offset: Int = 0) -> [JournalEntry] {
        let currentUserId = loadLoginUser() ?? 0
        let query =
            journals
            .select(journals[*])
            .filter(journalUserId == currentUserId)
            .order(journalDate.desc)
            .limit(limit, offset: offset)

        var entries: [JournalEntry] = []

        do {
            for row in try connection.prepare(query) {
                let entry = rowToJournal(row: row)
                entries.append(entry)
            }
        } catch {
            XYLog(error)
        }

        return entries
    }

    func getJournalEntriesCount() -> Int {
        guard let currentUserId = loadLoginUser() else { return 0 }

        do {
            let count = try connection.scalar(
                journals
                    .filter(journalUserId == currentUserId)
                    .count
            )
            return count
        } catch {
            XYLog(error)
            return 0
        }
    }

    @discardableResult
    func deleteJournalEntry(id: String) -> Bool {
        let record = journals.filter(journalId == id)
        do {
            return try connection.run(record.delete()) > 0
        } catch {
            XYLog(error)
            return false
        }
    }
}
