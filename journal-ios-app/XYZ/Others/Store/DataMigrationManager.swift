//
//  DataMigrationManager.swift
//  XYZ
//
//  Created by <PERSON> on 2025/7/1.
//

import Foundation
import SQLite

class DataMigrationManager {
    static let shared = DataMigrationManager()
    
    private let userDefaults = UserDefaults.standard
    private let migrationCompletedKey = "AppGroupMigrationCompleted"
    
    private init() {}
    
    // MARK: - Migration Status
    
    var isMigrationCompleted: Bool {
        return userDefaults.bool(forKey: migrationCompletedKey)
    }
    
    private func markMigrationCompleted() {
        userDefaults.set(true, forKey: migrationCompletedKey)
        userDefaults.synchronize()
    }
    
    // MARK: - Main Migration
    
    func performMigrationIfNeeded() {
        guard !isMigrationCompleted else {
            XYLog("Migration already completed, skipping...")
            return
        }
        
        XYLog("Starting App Group migration...")
        
        do {
            try performMigration()
            markMigrationCompleted()
            XYLog("App Group migration completed successfully")
        } catch {
            XYLog("Migration failed: \(error)")
            // Don't mark as completed if migration fails
        }
    }
    
    private func performMigration() throws {
        let sharedFileManager = SharedFileManager.shared
        
        // 1. Create shared directories
        try sharedFileManager.createSharedDirectoriesIfNeeded()
        
        // 2. Migrate database
        try migrateDatabaseIfNeeded()
        
        // 3. Migrate media files
        try migrateMediaFiles()
        
        XYLog("All migration steps completed")
    }
    
    // MARK: - Database Migration
    
    private func migrateDatabaseIfNeeded() throws {
        let sharedFileManager = SharedFileManager.shared
        
        guard let sharedDatabaseURL = sharedFileManager.sharedDatabaseURL else {
            throw SharedFileManagerError.appGroupNotAvailable
        }
        
        let legacyDatabaseURL = sharedFileManager.legacyDatabaseURL
        
        // Check if legacy database exists and shared database doesn't
        let fileManager = FileManager.default
        if fileManager.fileExists(atPath: legacyDatabaseURL.path) && 
           !fileManager.fileExists(atPath: sharedDatabaseURL.path) {
            
            XYLog("Migrating database from \(legacyDatabaseURL.path) to \(sharedDatabaseURL.path)")
            
            // Copy the database file
            try sharedFileManager.copyFileToShared(from: legacyDatabaseURL, to: sharedDatabaseURL)
            
            // Also copy any associated files (WAL, SHM)
            let legacyWalURL = legacyDatabaseURL.appendingPathExtension("wal")
            let legacyShmURL = legacyDatabaseURL.appendingPathExtension("shm")
            let sharedWalURL = sharedDatabaseURL.appendingPathExtension("wal")
            let sharedShmURL = sharedDatabaseURL.appendingPathExtension("shm")
            
            if fileManager.fileExists(atPath: legacyWalURL.path) {
                try? sharedFileManager.copyFileToShared(from: legacyWalURL, to: sharedWalURL)
            }
            
            if fileManager.fileExists(atPath: legacyShmURL.path) {
                try? sharedFileManager.copyFileToShared(from: legacyShmURL, to: sharedShmURL)
            }
            
            XYLog("Database migration completed")
        } else {
            XYLog("Database migration not needed")
        }
    }
    
    // MARK: - Media Files Migration
    
    private func migrateMediaFiles() throws {
        let sharedFileManager = SharedFileManager.shared
        let legacyDocumentsURL = sharedFileManager.legacyDocumentsURL
        
        guard let sharedDocumentsURL = sharedFileManager.sharedDocumentsURL else {
            throw SharedFileManagerError.appGroupNotAvailable
        }
        
        let fileManager = FileManager.default
        
        // Get all files in legacy documents directory
        guard let legacyFiles = try? fileManager.contentsOfDirectory(at: legacyDocumentsURL, 
                                                                     includingPropertiesForKeys: nil, 
                                                                     options: .skipsHiddenFiles) else {
            XYLog("No legacy files found to migrate")
            return
        }
        
        var migratedCount = 0
        var skippedCount = 0
        
        for legacyFileURL in legacyFiles {
            let fileName = legacyFileURL.lastPathComponent
            
            // Skip database files (already handled separately)
            if fileName.hasPrefix("db.sqlite") {
                continue
            }
            
            // Only migrate media files
            if isMediaFile(fileName) {
                let sharedFileURL = sharedDocumentsURL.appendingPathComponent(fileName)
                
                do {
                    try sharedFileManager.copyFileToShared(from: legacyFileURL, to: sharedFileURL)
                    migratedCount += 1
                    XYLog("Migrated media file: \(fileName)")
                } catch {
                    XYLog("Failed to migrate file \(fileName): \(error)")
                    skippedCount += 1
                }
            }
        }
        
        XYLog("Media migration completed: \(migratedCount) files migrated, \(skippedCount) files skipped")
    }
    
    private func isMediaFile(_ fileName: String) -> Bool {
        let mediaExtensions = ["jpg", "jpeg", "png", "gif", "mp4", "mov", "m4v", "m4a", "mp3", "wav", "aac"]
        let fileExtension = fileName.lowercased().components(separatedBy: ".").last ?? ""
        return mediaExtensions.contains(fileExtension)
    }
    
    // MARK: - Content Items Migration
    
    /// Updates content items in the database to use shared container paths
    func updateContentItemPaths() throws {
        guard let sharedDatabaseURL = SharedFileManager.shared.sharedDatabaseURL else {
            throw SharedFileManagerError.appGroupNotAvailable
        }
        
        let db = try Connection(sharedDatabaseURL.path)
        let journals = Table("journals")
        let journalContentItems = Expression<String>("content_items")
        let journalId = Expression<String>("journal_id")
        
        // Get all journal entries
        let entries = try db.prepare(journals.select(journalId, journalContentItems))
        
        for entry in entries {
            let id = entry[journalId]
            let contentItemsJSON = entry[journalContentItems]
            
            // Parse content items
            if let data = contentItemsJSON.data(using: .utf8),
               let contentItems = try? JSONDecoder().decode([ContentItem].self, from: data) {
                
                var updatedItems = contentItems
                var hasChanges = false
                
                // Update file paths in content items
                for (index, item) in contentItems.enumerated() {
                    if item.type == .image || item.type == .video || item.type == .audio {
                        let updatedContent = updateFilePathToShared(item.content)
                        if updatedContent != item.content {
                            updatedItems[index] = ContentItem(
                                id: item.id,
                                type: item.type,
                                content: updatedContent,
                                metadata: item.metadata
                            )
                            hasChanges = true
                        }
                    }
                }
                
                // Update database if there are changes
                if hasChanges {
                    if let updatedData = try? JSONEncoder().encode(updatedItems),
                       let updatedJSON = String(data: updatedData, encoding: .utf8) {
                        
                        let update = journals.filter(journalId == id).update(journalContentItems <- updatedJSON)
                        try db.run(update)
                        XYLog("Updated content items for journal: \(id)")
                    }
                }
            }
        }
    }
    
    private func updateFilePathToShared(_ filePath: String) -> String {
        guard let url = URL(string: filePath),
              let sharedDocumentsURL = SharedFileManager.shared.sharedDocumentsURL else {
            return filePath
        }
        
        let fileName = url.lastPathComponent
        let sharedURL = sharedDocumentsURL.appendingPathComponent(fileName)
        return sharedURL.absoluteString
    }
}
