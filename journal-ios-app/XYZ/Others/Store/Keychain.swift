//
//  Keychain.swift

//
//  Created by <PERSON> on 2025/2/21.
//

import KeychainAccess
import Foundation

class KeychainStore {

    let keychain = Keychain(service: "cn.seungyu.journal")
    let deviceId = "journal-device-id"

    func getDeviceId() -> String {
        let other = NSUUID().uuidString
        if let id = keychain[deviceId] {
            return id
        }
        setDeviceId(uuid: other)
        return other
    }

    func setDeviceId(uuid: String) {
        keychain[deviceId] = uuid
    }
}
