//
//  SharedFileManager.swift
//  XYZ
//
//  Created by <PERSON> on 2025/7/1.
//

import Foundation
import UIKit

class SharedFileManager {
    static let shared = SharedFileManager()
    
    private let appGroupIdentifier = "group.cn.seungyu.journal"
    
    private init() {}
    
    // MARK: - App Group Container
    
    /// Returns the shared container URL for the app group
    var sharedContainerURL: URL? {
        return FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier)
    }
    
    /// Returns the shared documents directory URL
    var sharedDocumentsURL: URL? {
        guard let containerURL = sharedContainerURL else { return nil }
        return containerURL.appendingPathComponent("Documents")
    }
    
    /// Returns the shared database URL
    var sharedDatabaseURL: URL? {
        guard let documentsURL = sharedDocumentsURL else { return nil }
        return documentsURL.appendingPathComponent("db.sqlite3")
    }
    
    // MARK: - Directory Setup
    
    /// Creates the shared documents directory if it doesn't exist
    func createSharedDirectoriesIfNeeded() throws {
        guard let documentsURL = sharedDocumentsURL else {
            throw SharedFileManagerError.appGroupNotAvailable
        }
        
        let fileManager = FileManager.default
        if !fileManager.fileExists(atPath: documentsURL.path) {
            try fileManager.createDirectory(at: documentsURL, withIntermediateDirectories: true, attributes: nil)
        }
    }
    
    // MARK: - Legacy App Container Paths
    
    /// Returns the legacy app documents directory URL
    var legacyDocumentsURL: URL {
        return FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
    }
    
    /// Returns the legacy database URL
    var legacyDatabaseURL: URL {
        return legacyDocumentsURL.appendingPathComponent("db.sqlite3")
    }
    
    // MARK: - File Operations
    
    /// Moves a file from legacy location to shared location
    func moveFileToShared(from legacyURL: URL, to sharedURL: URL) throws {
        let fileManager = FileManager.default
        
        // Create parent directory if needed
        let parentDirectory = sharedURL.deletingLastPathComponent()
        if !fileManager.fileExists(atPath: parentDirectory.path) {
            try fileManager.createDirectory(at: parentDirectory, withIntermediateDirectories: true, attributes: nil)
        }
        
        // Move the file if it exists in legacy location and doesn't exist in shared location
        if fileManager.fileExists(atPath: legacyURL.path) && !fileManager.fileExists(atPath: sharedURL.path) {
            try fileManager.moveItem(at: legacyURL, to: sharedURL)
        }
    }
    
    /// Copies a file from legacy location to shared location
    func copyFileToShared(from legacyURL: URL, to sharedURL: URL) throws {
        let fileManager = FileManager.default
        
        // Create parent directory if needed
        let parentDirectory = sharedURL.deletingLastPathComponent()
        if !fileManager.fileExists(atPath: parentDirectory.path) {
            try fileManager.createDirectory(at: parentDirectory, withIntermediateDirectories: true, attributes: nil)
        }
        
        // Copy the file if it exists in legacy location and doesn't exist in shared location
        if fileManager.fileExists(atPath: legacyURL.path) && !fileManager.fileExists(atPath: sharedURL.path) {
            try fileManager.copyItem(at: legacyURL, to: sharedURL)
        }
    }
    
    /// Saves media file to shared container
    func saveMediaFile(data: Data, fileName: String) throws -> URL {
        guard let documentsURL = sharedDocumentsURL else {
            throw SharedFileManagerError.appGroupNotAvailable
        }
        
        try createSharedDirectoriesIfNeeded()
        
        let fileURL = documentsURL.appendingPathComponent(fileName)
        try data.write(to: fileURL)
        return fileURL
    }
    
    /// Saves image to shared container
    func saveImageToShared(_ image: UIImage, fileName: String? = nil) throws -> URL {
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw SharedFileManagerError.imageCompressionFailed
        }
        
        let finalFileName = fileName ?? "image_\(Date().timeIntervalSince1970).jpg"
        return try saveMediaFile(data: imageData, fileName: finalFileName)
    }
    
    /// Copies video file to shared container
    func copyVideoToShared(from sourceURL: URL, fileName: String? = nil) throws -> URL {
        guard let documentsURL = sharedDocumentsURL else {
            throw SharedFileManagerError.appGroupNotAvailable
        }
        
        try createSharedDirectoriesIfNeeded()
        
        let finalFileName = fileName ?? "video_\(Date().timeIntervalSince1970).\(sourceURL.pathExtension)"
        let destinationURL = documentsURL.appendingPathComponent(finalFileName)
        
        let fileManager = FileManager.default
        if !fileManager.fileExists(atPath: destinationURL.path) {
            try fileManager.copyItem(at: sourceURL, to: destinationURL)
        }
        
        return destinationURL
    }
    
    /// Generates a unique file name for audio recording
    func generateAudioFileName() -> String {
        return "recording_\(Date().timeIntervalSince1970).m4a"
    }
    
    /// Returns the URL for audio recording in shared container
    func audioRecordingURL(fileName: String? = nil) throws -> URL {
        guard let documentsURL = sharedDocumentsURL else {
            throw SharedFileManagerError.appGroupNotAvailable
        }
        
        try createSharedDirectoriesIfNeeded()
        
        let finalFileName = fileName ?? generateAudioFileName()
        return documentsURL.appendingPathComponent(finalFileName)
    }
}

// MARK: - Error Types

enum SharedFileManagerError: Error, LocalizedError {
    case appGroupNotAvailable
    case imageCompressionFailed
    case fileNotFound
    case migrationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .appGroupNotAvailable:
            return "App Group container is not available"
        case .imageCompressionFailed:
            return "Failed to compress image"
        case .fileNotFound:
            return "File not found"
        case .migrationFailed(let message):
            return "Migration failed: \(message)"
        }
    }
}
