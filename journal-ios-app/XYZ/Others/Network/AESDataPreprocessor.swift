//
//  Untitled.swift

//
//  Created by <PERSON> on 2025/2/13.
//

import Alamofire
import CryptoSwift
import Foundation

struct AESDataPreprocessor: DataPreprocessor {

    struct Response: Decodable {
        var data: String
    }

    let aesKey: Data
    let aesIV: Data

    func preprocess(_ data: Data) throws -> Data {

        let response = try JSONDecoder().decode(Response.self, from: data)

        XYLog(response.data)

        guard let decodedData = Data(base64Encoded: response.data) else {
            throw XYError.raw("can not decode data", 0)
        }

        // data processerr
        let decryptor = AESDecryptor(key: aesKey, aiv: aesIV)
        guard let decryptedData = decryptor.decrypt(data: decodedData) else {
            throw XYError.raw("can not decode data", 0)
        }

        XYLog("DecryptedData: \(decryptedData.string(encoding: .utf8) ?? "")")

        return decryptedData
    }
}

class AESDecryptor {

    private let aesKey: Data
    private let aesIv: Data

    init(key: Data, aiv: Data) {
        self.aesKey = key
        self.aesIv = aiv
    }

    func decrypt(data: Data) -> Data? {
        guard
            let decrypted = try? AES(
                key: Array(aesKey.map({ $0 })),
                blockMode: CBC.init(iv: Array(aesIv.map({ $0 }))),
                padding: .pkcs7
            ).decrypt([UInt8](data))
        else {
            return nil
        }

        let decryptedData = Data(decrypted)
        return decryptedData
    }
}
