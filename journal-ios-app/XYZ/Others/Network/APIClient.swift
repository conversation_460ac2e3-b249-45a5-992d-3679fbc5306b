//
//  APIClient.swift

//
//  Created by <PERSON> on 2025/1/8.
//

import Alamofire

struct RequestModel {
    let path: String  // Path for the endpoint (this can be dynamic)
    let parameters: [String: Any]?  // Parameters to be sent in the request
    var method: HTTPMethod = .get

    // Initialize the request model with dynamic path and parameters
    init(path: String, parameters: [String: Any]? = nil, method: HTTPMethod = .get) {
        self.path = path
        self.parameters = parameters
        self.method = method
    }

    mutating func setMethod(method: HTTPMethod) -> RequestModel {
        self.method = method
        return self
    }
}

@MainActor
class APIClient {
    static var shared = APIClient()

    private let baseURL = JSONFiles.apiEndpoint
    private let session: Session
    private let keyManager = KeyManager()
    private let dataPreprocessor: AESDataPreprocessor

    private init() {

        let serverTrustManager = ServerTrustManager(evaluators: [
            "api.seungyu.cn": DisabledTrustEvaluator()
        ])

        var snocValue = JSONFiles.defaultSnocKey
        var aesKey = JSONFiles.aesDefaultKey.data(using: .ascii)!

        if let key = self.keyManager.loadPublicKey(),
            let random = self.keyManager.generateRandomKey(),
            let snoc = self.keyManager.encryptRandomKey(randomKey: random, publicKey: key) {
            snocValue = snoc.base64EncodedString()
            aesKey = random
        }

        session = Session(
            configuration: .default,
            interceptor: Interceptor(
                adapter: SNRequestAdapter(snoc: snocValue),
                retrier: SNRequestRetrier()
            ),
            serverTrustManager: serverTrustManager
        )
        dataPreprocessor = AESDataPreprocessor(aesKey: aesKey, aesIV: aesKey)
    }

    // MARK: - Generic Request Method
    func sendRequest<T: Decodable>(
        requestModel: RequestModel, completion: @escaping (Result<T, Error>) -> Void
    ) {
        let url = "\(baseURL)/\(requestModel.path)"
        let method: HTTPMethod = requestModel.method

        self.session.request(
            url, method: method,
            parameters: requestModel.parameters,
            encoding: method == .get ? URLEncoding.default : JSONEncoding.default
        )
        .validate(statusCode: 200..<300)
        .responseDecodable(of: Response<T>.self, dataPreprocessor: dataPreprocessor) { response in
            switch response.result {
            case .success(let decodedResponse):
                completion(.success(decodedResponse.data))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    func sendRequestAsync<T: Decodable>(requestModel: RequestModel) async throws -> T {
        let url = "\(baseURL)/\(requestModel.path)"
        let method: HTTPMethod = requestModel.method

        return try await withCheckedThrowingContinuation { continuation in
            session.request(
                url,
                method: method,
                parameters: requestModel.parameters,
                encoding: method == .get ? URLEncoding.default : JSONEncoding.default
            )
            .validate(statusCode: 200..<300)
            .responseDecodable(
                of: Response<T>.self,
                dataPreprocessor: dataPreprocessor
            ) { response in
                switch response.result {
                case .success(let decodedResponse):
                    continuation.resume(returning: decodedResponse.data)

                case .failure(let afError):
                    // 如果status code 不对，尝试XYError
                    if case .responseValidationFailed(let reason) = afError {
                        if case .unacceptableStatusCode(let code) = reason {
                            let errorKey = "error_code_\(code)"
                            let errorMessage = XYLocalize.XYLocalize(errorKey)
                            if errorKey == errorMessage.lowercased() {
                                continuation.resume(throwing: XYError.api(code, ""))
                                return
                            }
                            continuation.resume(throwing: XYError.raw(errorMessage, code))
                        } else if let data = response.data {
                            do {
                                let decrypted = try self.dataPreprocessor.preprocess(data)
                                let apiError = try JSONDecoder().decode(
                                    APIError.self, from: decrypted)
                                let message = apiError.message
                                var error = XYError.unknown
                                guard let code = apiError.code else {
                                    error = .unknown
                                    continuation.resume(throwing: error)
                                    return
                                }
                                let errorKey = "error_code_\(code)"
                                let errorMessage = XYLocalize.XYLocalize(errorKey)
                                if errorKey == errorMessage.lowercased() {
                                    error = .api(code, message)
                                    continuation.resume(throwing: error)
                                    return
                                }
                                error = .raw(errorMessage, code)
                                continuation.resume(throwing: error)
                            } catch {
                                continuation.resume(throwing: error)
                            }
                        } else {
                            continuation.resume(throwing: afError)
                        }
                    } else if case .responseSerializationFailed = afError {
                        continuation.resume(throwing: XYError.decoding(afError))
                    } else if case .sessionTaskFailed = afError {
                        continuation.resume(throwing: XYError.networkNotConnected)
                    } else if let urlError = afError.underlyingError as? URLError {
                        switch urlError.code {
                        case .notConnectedToInternet:
                            continuation.resume(throwing: XYError.networkNotConnected)
                        default:
                            continuation.resume(throwing: afError)
                        }
                    } else {
                        continuation.resume(throwing: afError)
                    }
                }
            }
        }
    }

    @MainActor
    func convertErrorMap(error: APIError) -> XYError {
        let message = error.message
        guard let code = error.code else {
            return .unknown
        }

        let errorKey = "error_code_\(code)"
        let errorMessage = XYLocalize.XYLocalize(errorKey)
        if errorKey == errorMessage.lowercased() {
            return .api(code, message)
        }

        return .raw(errorMessage, code)
    }
}

final class SNRequestAdapter: RequestAdapter {

    let snoc: String
    init(snoc: String) {
        self.snoc = snoc
    }

    func adapt(
        _ urlRequest: URLRequest,
        for session: Session,
        completion: @escaping (Result<URLRequest, Error>) -> Void
    ) {
        var request = urlRequest
        if let token = App.shared.instance?.token?.token {
            request.addValue("Bearer " + token, forHTTPHeaderField: "Authorization")
        }
        request.addValue(snoc, forHTTPHeaderField: "Snoc")

        completion(Result.success(request))
    }
}

struct SNRequestRetrier: RequestRetrier {

    func retry(
        _ request: Request,
        for session: Session,
        dueTo error: Error,
        completion: @escaping (RetryResult) -> Void
    ) {

        completion(RetryResult.doNotRetry)
    }
}
