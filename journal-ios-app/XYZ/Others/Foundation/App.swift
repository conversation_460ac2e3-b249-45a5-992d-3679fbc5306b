//
//  App.swift

//
//  Created by <PERSON> on 2025/2/14.
//

import Combine

class UserInstance {
    var user: UserProfile?
    var token: Token?
    var uid: Int64!
    var isAuthEnabled = false

    init(
        user: UserProfile? = nil,
        token: Token? = nil,
        uid: Int64 = 0,
        isAuthEnabled: Bool = false
    ) {
        self.user = user
        self.token = token
        self.uid = uid
        self.isAuthEnabled = isAuthEnabled
    }
}

class App: ObservableObject {
    static var shared = App()

    private var storeInstance: UserInstance?

    var isUserLoggedin = false

    @Published var isLoggedIn = false
    @Published var isAuthed = false
    @Published var isGuestMode = true // Track if user is in guest mode

    var instance: UserInstance? {
        get {
            storeInstance
        }
        set {
            storeInstance = newValue
            if newValue != nil {
                isUserLoggedin = true
                isLoggedIn = true
                isGuestMode = false
            } else {
                isUserLoggedin = false
                isLoggedIn = false
                isGuestMode = true
            }
        }
    }

    func userName() -> String {
        let firstName = App.shared.instance?.user?.firstName ?? ""
        let lastName = App.shared.instance?.user?.lastName ?? ""

        if !firstName.isEmpty && !lastName.isEmpty {
            return "\(firstName) \(lastName)"
        } else if !firstName.isEmpty {
            return firstName
        } else if !lastName.isEmpty {
            return lastName
        } else {
            return isGuestMode ? "访客用户" : "意日记用户"
        }
    }

    // Helper methods for guest mode
    func isInGuestMode() -> Bool {
        return isGuestMode
    }

    func canSyncData() -> Bool {
        return !isGuestMode && isLoggedIn
    }
}
