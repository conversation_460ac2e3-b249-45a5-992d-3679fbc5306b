//
//  Throttle.swift

//
//  Created by <PERSON> on 2025/2/14.
//

import Foundation
import Combine

typealias Throttled = () -> Void

class ThrottledAction {
    private var cancellables = Set<AnyCancellable>()

    // Create a publisher that emits events (you can think of this as a button press or network trigger)
    private var actionPublisher = PassthroughSubject<Void, Never>()

    // Throttling interval (e.g., allow only one action every 2 seconds)
    private let throttleInterval: TimeInterval = 2
    private let actionDelay: TimeInterval = 0.01

    private let throttle: Throttled

    init(throttle: @escaping Throttled) {
        self.throttle = throttle
        setupPublisher()
    }

    // Setting up the Combine publisher
    private func setupPublisher() {
        actionPublisher
            .throttle(for: .seconds(throttleInterval), scheduler: DispatchQueue.main, latest: false)
            .delay(for: .seconds(actionDelay), scheduler: DispatchQueue.main) // Add delay before performing the action
            .sink { [weak self] _ in
                self?.performAction()
            }
            .store(in: &cancellables)
    }

    // Trigger the action from an external event (like a button press)
    func triggerAction() {
        actionPublisher.send()
    }

    // The actual action you want to perform
    private func performAction() {
        self.throttle()
    }
}
