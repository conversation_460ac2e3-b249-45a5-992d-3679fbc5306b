//
//  AssetsManager.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/29.
//

import UIKit
import Qiniu

struct QiniuTokenResponse: Codable {
    let token: String
    let domain: String
    let prefix: String
}

struct QiniuUploadResponse {
    let url: String
    let key: String
}

enum QiniuUploadError: Error {
    case invalidImageData
    case uploadFailed(Error?)
    case tokenFetchFailed
}

class QiniuService {
    static let shared = QiniuService()

    private let uploadManager = QNUploadManager()

    private let qiniuToken = "qiniu/token"

    func fetchUploadToken() async throws -> QiniuTokenResponse {
        let token: QiniuTokenResponse = try await APIClient.shared.sendRequestAsync(requestModel: RequestModel(path: qiniuToken, parameters: [:]))
        return token
    }
}

extension QiniuService {

    // 带进度回调的上传方法
      func uploadImagesWithProgress(
          _ images: [UIImage],
          progressHandler: @escaping (Double) -> Void
      ) async throws -> [QiniuUploadResponse] {
          let tokenInfo = try await fetchUploadToken()
          var completedCount = 0
          let totalCount = images.count

          return try await withThrowingTaskGroup(of: QiniuUploadResponse.self) { group in
              // 启动所有上传任务
              for (_, image) in images.enumerated() {
                  group.addTask {
                      guard let imageData = image.jpegData(compressionQuality: 0.8) else {
                          throw QiniuUploadError.invalidImageData
                      }

                      let fileName = "\(tokenInfo.prefix)/\(UUID().uuidString).jpg"
                      return try await withCheckedThrowingContinuation { continuation in
                          self.uploadManager?.put(
                              imageData,
                              key: fileName,
                              token: tokenInfo.token,
                              complete: { info, key, _ in
                                  if let error = info?.error {
                                      continuation.resume(throwing: QiniuUploadError.uploadFailed(error))
                                  } else if let key = key {
                                      let url = "\(tokenInfo.domain)/\(key)"
                                      continuation.resume(returning: QiniuUploadResponse(url: url, key: key))
                                  } else {
                                      continuation.resume(throwing: QiniuUploadError.uploadFailed(nil))
                                  }
                              },
                              option: QNUploadOption(
                                  progressHandler: { _, _ in
                                      DispatchQueue.main.async {
                                          completedCount += 1
                                          progressHandler(Double(completedCount) / Double(totalCount))
                                      }
                                  }
                              )
                          )
                      }
                  }
              }

              // 收集结果
              var results = [QiniuUploadResponse]()
              for try await result in group {
                  results.append(result)
              }

              return results
          }
      }
}
