//
//  AdMobBannerView.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/15.
//

import GoogleMobileAds
import UIKit

class AdMobBannerView: UIView {

    // MARK: - Properties
    private var bannerView: BannerView!
    private var isAdLoaded = false

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupBannerView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupBannerView()
    }

    // MARK: - Setup
    private func setupBannerView() {
        bannerView = BannerView(adSize: AdSizeBanner)
        bannerView.adUnitID = AdMobManager.shared.bannerAdUnitID
        bannerView.delegate = self
        bannerView.backgroundColor = UIColor.clear

        addSubview(bannerView)

        // Set constraints
        bannerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            bannerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            bannerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            bannerView.widthAnchor.constraint(equalToConstant: AdSizeBanner.size.width),
            bannerView.heightAnchor.constraint(equalToConstant: AdSizeBanner.size.height)
        ])

        // Set initial height constraint for the container
        heightAnchor.constraint(equalToConstant: AdSizeBanner.size.height + 16).isActive = true

        // Initially hide the view until ad is loaded
        isHidden = true
    }

    // MARK: - Public Methods
    func loadAd(from viewController: UIViewController) {
        bannerView.rootViewController = viewController
        let request = AdMobManager.shared.createAdRequest()
        bannerView.load(request)
    }

    func refreshAd() {
        if isAdLoaded {
            let request = AdMobManager.shared.createAdRequest()
            bannerView.load(request)
        }
    }
}

// MARK: - BannerViewDelegate
extension AdMobBannerView: BannerViewDelegate {
    func bannerViewDidReceiveAd(_ bannerView: BannerView) {
        XYLog("AdMob banner ad loaded successfully")
        isAdLoaded = true
        isHidden = false

        // Animate the appearance
        alpha = 0
        UIView.animate(withDuration: 0.3) {
            self.alpha = 1
        }
    }

    func bannerView(_ bannerView: BannerView, didFailToReceiveAdWithError error: Error) {
        XYLog("AdMob banner ad failed to load: \(error.localizedDescription)")
        isAdLoaded = false
        isHidden = true
    }

    func bannerViewDidRecordImpression(_ bannerView: BannerView) {
        XYLog("AdMob banner ad recorded impression")
    }

    func bannerViewWillPresentScreen(_ bannerView: BannerView) {
        XYLog("AdMob banner ad will present screen")
    }

    func bannerViewWillDismissScreen(_ bannerView: BannerView) {
        XYLog("AdMob banner ad will dismiss screen")
    }

    func bannerViewDidDismissScreen(_ bannerView: BannerView) {
        XYLog("AdMob banner ad did dismiss screen")
    }
}
