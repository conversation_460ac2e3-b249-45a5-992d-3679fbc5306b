//
//  AdMobManager.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/15.
//

import Foundation
import GoogleMobileAds

class AdMobManager {
    static let shared = AdMobManager()

    private init() {}

    // MARK: - Ad Unit IDs
    struct AdUnitIDs {
        // Test ad unit IDs - replace with your actual ad unit IDs in production
        static let bannerTest = "ca-app-pub-3940256099942544/2435281174"
        static let interstitialTest = "ca-app-pub-3940256099942544/4411468910"

        // Production ad unit IDs (replace these with your actual IDs)
        static let bannerProduction = "ca-app-pub-7665954428157405/7533091183"
        static let interstitialProduction = "YOUR_INTERSTITIAL_AD_UNIT_ID"
    }

    // MARK: - Configuration
    var isTestMode: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }

    var bannerAdUnitID: String {
        return isTestMode ? AdUnitIDs.bannerTest : AdUnitIDs.bannerProduction
    }

    var interstitialAdUnitID: String {
        return isTestMode ? AdUnitIDs.interstitialTest : AdUnitIDs.interstitialProduction
    }

    // MARK: - Ad Request
    func createAdRequest() -> Request {
        let request = Request()

        // Add test device IDs if in test mode
        if isTestMode {
            MobileAds.shared.requestConfiguration.testDeviceIdentifiers = [
                "" // Simulator ID - empty string for simulator
            ]
        }

        return request
    }

    // MARK: - Initialization
    func initializeAdMob(completion: (() -> Void)? = nil) {
        MobileAds.shared.start { status in
            XYLog("AdMob initialization completed with status: \(status)")
            completion?()
        }
    }
}
