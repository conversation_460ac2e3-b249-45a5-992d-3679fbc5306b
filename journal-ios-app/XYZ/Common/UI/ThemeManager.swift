import UIKit

/// ThemeManager manages application theme preferences and provides methods for theme switching
class ThemeManager {
    // MARK: - Singleton
    static let shared = ThemeManager()
    private var themeMode: ThemeMode = .system

    private init() {
        loadSavedTheme()
    }

    // MARK: - User Defaults Keys
    private enum UserDefaultsKeys {
        static let themeMode = "com.journal.themeMode"
    }

    /// Called when theme changes
    var onThemeChanged: (() -> Void)?

    /// Current theme mode
    var currentThemeMode: ThemeMode {
        get {
            return themeMode
        }
        set {
            Theme.currentThemeMode = newValue
            saveThemePreference(newValue)
            onThemeChanged?()
        }
    }

    var darkOrLight: ThemeMode {
        switch Theme.currentThemeMode {
        case .dark:
            return .dark
        case .light:
            return .light
        default:
            if UITraitCollection.current.userInterfaceStyle == .dark {
                return .dark
            }
            return .light
        }
    }

    /// Toggle between light and dark mode
    func toggleLightDarkMode() {
        switch currentThemeMode {
        case .light, .system:
            currentThemeMode = .dark
        case .dark:
            currentThemeMode = .light
        }
    }

    /// Save the current theme preference to UserDefaults
    private func saveThemePreference(_ mode: ThemeMode) {
        let rawValue: Int
        switch mode {
        case .light:
            rawValue = 1
        case .dark:
            rawValue = 2
        case .system:
            rawValue = 3
        }
        UserDefaults.standard.set(rawValue, forKey: UserDefaultsKeys.themeMode)
    }

    /// Load the saved theme from UserDefaults
    private func loadSavedTheme() {
        let rawValue = UserDefaults.standard.integer(forKey: UserDefaultsKeys.themeMode)
        let mode: ThemeMode

        switch rawValue {
        case 1:
            mode = .light
        case 2:
            mode = .dark
        default:
            mode = .system
        }
        Theme.currentThemeMode = mode
    }
}
