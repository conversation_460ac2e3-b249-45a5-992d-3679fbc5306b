import UIKit

enum ThemeMode {
    case system
    case light
    case dark
}

class Theme {
    // MARK: - Theme Configuration

    static var currentThemeMode: ThemeMode = .system {
        didSet {
            applyThemeMode()
        }
    }

    static func systemTheme() -> ThemeMode {
        if #available(iOS 13.0, *) {
            return UITraitCollection.current.userInterfaceStyle == .dark ? .dark : .light
        } else {
            return .light
        }
    }

    static func primaryColorString() -> String {
        if systemTheme() == .light {
            return "#191919"
        }
        return "#FFFFFF"
    }

    // MARK: - Dimensions
    static let cornerRadius: CGFloat = 12
    static let smallCornerRadius: CGFloat = 8
    static let padding: CGFloat = 16
    static let smallPadding: CGFloat = 8

    // MARK: - Theme Application
    private static func applyThemeMode() {
        switch currentThemeMode {
        case .light:
            configureLightMode()
        case .dark:
            configureDarkMode()
        case .system:
            configureSystemMode()
        }
    }

    static func configureSystemMode() {
        UIApplication.shared.connectedScenes.forEach { scene in
            if let windowScene = scene as? UIWindowScene {
                windowScene.windows.forEach { window in
                    window.overrideUserInterfaceStyle = .unspecified
                }
            }
        }
    }

    static func configureDarkMode() {
        UIApplication.shared.connectedScenes.forEach { scene in
            if let windowScene = scene as? UIWindowScene {
                windowScene.windows.forEach { window in
                    window.overrideUserInterfaceStyle = .dark
                }
            }
        }
    }

    static func configureLightMode() {
        UIApplication.shared.connectedScenes.forEach { scene in
            if let windowScene = scene as? UIWindowScene {
                windowScene.windows.forEach { window in
                    window.overrideUserInterfaceStyle = .light
                }
            }
        }
    }
}
