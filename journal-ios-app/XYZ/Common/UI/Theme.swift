import UIKit

enum ThemeMode {
    case system
    case light
    case dark
}

class Theme {
    // MARK: - Theme Configuration

    static var currentThemeMode: ThemeMode = .system {
        didSet {
            applyThemeMode()
        }
    }

    static func systemTheme() -> ThemeMode {
        if #available(iOS 13.0, *) {
            return UITraitCollection.current.userInterfaceStyle == .dark ? .dark : .light
        } else {
            return .light
        }
    }

    static func primaryColorString() -> String {
        if systemTheme() == .light {
            return "#191919"
        }
        return "#FFFFFF"
    }

    // MARK: - Colors
    struct Colors {
        // Brand Colors
        static let primary = ColorSet(
            light: UIColor(hexString: "#191919")!,
            dark: UIColor(hexString: "#FFFFFF")!
        )
        static let secondary = ColorSet(
            light: UIColor(hexString: "#999999")!,
            dark: UIColor(hexString: "#999999")!
        )
        static let accent = ColorSet(
            light: UIColor(named: "AccentColor") ?? .systemOrange,
            dark: UIColor(named: "AccentColor") ?? .systemOrange
        )
        static let lightTextColor = ColorSet(
            light: UIColor(hexString: "#F2F3F5")!,
            dark: UIColor(hexString: "#F2F3F5")!
        )
        static let danger = ColorSet(
            light: UIColor(named: "DangerColor") ?? .systemRed,
            dark: UIColor(named: "DangerColor") ?? .systemRed
        )
        static let welcomeTitle = ColorSet(
            light: UIColor(hexString: "#FFFFFF")!,
            dark: UIColor(hexString: "#FFFFFF")!
        )
        static let avatarTint = ColorSet(
            light: UIColor(hexString: "#666666")!,
            dark: UIColor(hexString: "#666666")!
        )

        // Background Colors
        static let background = ColorSet(
            light: UIColor(hexString: "#FFFFFF")!,
            dark: UIColor(hexString: "#191919")!
        )
        static let cardBackground = ColorSet(
            light: UIColor(hexString: "#F0F0F0", transparency: 1)!,
            dark: UIColor(hexString: "#FFFFFF", transparency: 0.1)!
        )
        static let tabBarBackground = ColorSet(
            light: UIColor(hexString: "#F2F3F5", transparency: 1)!,
            dark: UIColor(hexString: "#333333", transparency: 1)!
        )
        static let border = UIColor.separator

        // Text Colors
        static let text = ColorSet(
            light: UIColor(hexString: "#191919")!,
            dark: UIColor(hexString: "#F2F3F5")!
        )
        static let textPrimary = ColorSet(
            light: UIColor(hexString: "#191919")!,
            dark: UIColor(hexString: "#FFFFFF")!
        )
        static let textS = ColorSet(
            light: .secondaryLabel,
            dark: .secondaryLabel
        )
        static let secondaryText = ColorSet(
            light: .secondaryLabel,
            dark: .secondaryLabel
        )

        // Common Dynamic Colors
        static let pageBg = ColorSet(
            light: UIColor(hexString: "#F7F8F9")!,
            dark: UIColor(hexString: "#000000")!
        )
        static let separator = ColorSet(
            light: UIColor(hexString: "#DDDDDD")!,
            dark: UIColor(hexString: "#272727")!
        )
        static public let tabColor = ColorSet(
            light: UIColor(hexString: "#F2F3F5")!,
            dark: UIColor(hexString: "#111111")!
        )
        static public let tabSelectedColor = ColorSet(
            light: UIColor(hexString: "#FFFFFF")!,
            dark: UIColor(hexString: "#333333")!
        )
        static let borderColor = ColorSet(
            light: UIColor(hexString: "#DDDDDD")!,
            dark: UIColor(hexString: "#666666")!
        )

        // main theme
        static let themeColor = ColorSet(
            light: UIColor(hexString: String.themeStr())!,
            dark: UIColor(hexString: "#191919")!
        )
        static let theme = ColorSet(
            light: UIColor(hexString: String.themeStr())!,
            dark: UIColor(hexString: String.themeStr())!
        )

        static let themeColorGreen = ColorSet(
            light: UIColor(hexString: "#5CBA60")!,
            dark: UIColor(hexString: "#5CBA60")!
        )
        static let buttonDisable = ColorSet(
            light: UIColor(hexString: "#999999")!,
            dark: UIColor(hexString: "#999999")!
        )

        static let themeColorRed = ColorSet(
            light: UIColor(hexString: "#FF5757")!,
            dark: UIColor(hexString: "#FF5757")!
        )
        static let themeColorBlue = ColorSet(
            light: UIColor(hexString: "#3498DB")!,
            dark: UIColor(hexString: "#3498DB")!
        )

        // space
        static let lightBackGround = ColorSet(
            light: UIColor(hexString: "#F2F3F5")!,
            dark: UIColor(hexString: "#222222")!
        )
    }

    // MARK: - Dimensions
    static let cornerRadius: CGFloat = 12
    static let smallCornerRadius: CGFloat = 8
    static let padding: CGFloat = 16
    static let smallPadding: CGFloat = 8

    // MARK: - Theme Application
    private static func applyThemeMode() {
        switch currentThemeMode {
        case .light:
            configureLightMode()
        case .dark:
            configureDarkMode()
        case .system:
            configureSystemMode()
        }
    }

    static func configureSystemMode() {
        UIApplication.shared.connectedScenes.forEach { scene in
            if let windowScene = scene as? UIWindowScene {
                windowScene.windows.forEach { window in
                    window.overrideUserInterfaceStyle = .unspecified
                }
            }
        }
    }

    static func configureDarkMode() {
        UIApplication.shared.connectedScenes.forEach { scene in
            if let windowScene = scene as? UIWindowScene {
                windowScene.windows.forEach { window in
                    window.overrideUserInterfaceStyle = .dark
                }
            }
        }
    }

    static func configureLightMode() {
        UIApplication.shared.connectedScenes.forEach { scene in
            if let windowScene = scene as? UIWindowScene {
                windowScene.windows.forEach { window in
                    window.overrideUserInterfaceStyle = .light
                }
            }
        }
    }
}

// MARK: - Color Set Structure
// 确保 dynamicColor() 方法正确实现
struct ColorSet {
    let light: UIColor
    let dark: UIColor

    func dynamicColor() -> UIColor {
        return UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return self.dark
            default:
                return self.light
            }
        }
    }
}
