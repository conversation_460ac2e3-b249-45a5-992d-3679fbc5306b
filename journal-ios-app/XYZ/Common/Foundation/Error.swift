//
//  Error.swift

//
//  Created by <PERSON> on 2025/3/20.
//

import Foundation

struct APIError: Decodable {
    var message: String
    var code: Int?

    struct Codes {
        let toastCodes: [Int] = [1]
    }
}

enum XYError: Error {
    case response(data: Data?, statusCode: Int)
    case raw(String, Int)
    case networkNotConnected  // Errors related to network connectivity
    case api(Int, String)  // Errors from the API with status code and message
    case decoding(Error)  // Errors during JSON decoding
    case authentication  // Authentication-related errors
    case unauthorized  // Specific unauthorized error.
    case forbidden  // Specific forbidden error.
    case serverError(Int)  // Server error with status code
    case noSuchPlan
    case unknown  // A catch-all for unexpected errors
}
