import UIKit
import SnapKit

protocol CategoryViewDelegate: AnyObject {
    func categoryView(_ categoryView: CategoryView, didSelectCategoryAt index: Int)
    func categoryViewDidTapExtraButton(_ categoryView: CategoryView)
}

class CategoryView: UIView {
    // MARK: - Properties
    private let scrollView = UIScrollView()
    private let stackView = UIStackView()
    private var buttons: [UIButton] = []
    private var selectedIndex: Int = 0

    weak var delegate: CategoryViewDelegate?

    // MARK: - Initialization
    init(categories: [String], extraButtonTitle: String? = nil) {
        super.init(frame: .zero)
        setupUI(categories: categories, extraButtonTitle: extraButtonTitle)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI(categories: [String], extraButtonTitle: String?) {
        backgroundColor = Theme.Colors.pageBg.dynamicColor()

        scrollView.showsHorizontalScrollIndicator = false
        addSubview(scrollView)

        stackView.axis = .horizontal
        stackView.spacing = 20
        stackView.distribution = .fillProportionally
        scrollView.addSubview(stackView)

        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(50)
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16))
            make.height.equalToSuperview()
        }

        // Create category buttons
        for (index, category) in categories.enumerated() {
            let button = createButton(title: category, tag: index)
            stackView.addArrangedSubview(button)
            buttons.append(button)

            // Set first category as selected
            if index == 0 {
                updateButtonAppearance(button, isSelected: true)
            }
        }

        // Add extra button if needed
        if let extraTitle = extraButtonTitle {
            let extraButton = createButton(title: extraTitle, tag: -1)
            extraButton.setTitleColor(Theme.Colors.themeColorGreen.dynamicColor(), for: .normal)
            stackView.addArrangedSubview(extraButton)
        }
    }

    private func createButton(title: String, tag: Int) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.setTitleColor(UIColor.dynamicColorWithHex(color1: "#999999", color2: "#999999"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.tag = tag
        button.addTarget(self, action: #selector(buttonTapped(_:)), for: .touchUpInside)
        return button
    }

    // MARK: - Actions
    @objc private func buttonTapped(_ sender: UIButton) {
        if sender.tag == -1 {
            delegate?.categoryViewDidTapExtraButton(self)
            return
        }

        updateSelection(at: sender.tag)
        delegate?.categoryView(self, didSelectCategoryAt: sender.tag)
    }

    // MARK: - Public Methods
    func updateSelection(at index: Int) {
        buttons.forEach { button in
            updateButtonAppearance(button, isSelected: button.tag == index)
        }
        selectedIndex = index
    }

    private func updateButtonAppearance(_ button: UIButton, isSelected: Bool) {
        if isSelected {
            button.setTitleColor(Theme.Colors.themeColorGreen.dynamicColor(), for: .normal)
            button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        } else {
            button.setTitleColor(UIColor.dynamicColorWithHex(color1: "#999999", color2: "#999999"), for: .normal)
            button.titleLabel?.font = .systemFont(ofSize: 14)
        }
    }
}
