//
//  ToastView.swift

//
//  Created by <PERSON> on 2025/3/31.
//

import SnapKit
import UIKit

class ToastView: UIView {

    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = Theme.Colors.lightBackGround.dynamicColor()
        view.layer.cornerRadius = 15
        view.clipsToBounds = true
        return view
    }()

    private let iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.layer.cornerRadius = 5
        imageView.layer.masksToBounds = true
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = Theme.Colors.text.dynamicColor()
        imageView.image = UIImage(named: "Logo")
        return imageView
    }()

    private let messageLabel: UILabel = {
        let label = UILabel()
        label.textColor = Theme.Colors.text.dynamicColor()
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textAlignment = .left
        label.numberOfLines = 0
        return label
    }()

    // MARK: - Properties
    private static var activeToasts: [ToastView] = []
    private var hideTimer: Timer?

    // MARK: - Initialization
    init(message: String) {
        super.init(frame: .zero)
        messageLabel.text = message
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        hideTimer?.invalidate()
    }

    // MARK: - UI Setup
    private func setupUI() {
        backgroundColor = .clear

        addSubview(containerView)
        containerView.addSubview(iconImageView)
        containerView.addSubview(messageLabel)

        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.size.equalTo(50)
            make.top.greaterThanOrEqualToSuperview().offset(16)
        }

        messageLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(8)
            make.trailing.equalToSuperview().offset(-12)
            make.top.bottom.equalToSuperview().inset(12)
        }
    }

    // MARK: - Public Methods
    class func show(message: String, duration: TimeInterval = 2.0, in view: UIView? = nil) {
        DispatchQueue.main.async {
            let hostView = getKeyWindow(fromView: view)
            guard let hostView = hostView else { return }

            let toastView = ToastView(message: message)
            activeToasts.append(toastView)

            hostView.addSubview(toastView)
            toastView.snp.makeConstraints { make in
                make.centerX.equalToSuperview()
                make.leading.trailing.equalToSuperview().inset(16)
                make.top.equalTo(hostView.safeAreaLayoutGuide).offset(-60)  // Start position
            }
            hostView.layoutIfNeeded()

            UIView.animate(
                withDuration: 0.3,
                animations: {

                    toastView.snp.remakeConstraints { make in
                        make.centerX.equalToSuperview()
                        make.leading.trailing.equalToSuperview().inset(16)
                        make.top.equalTo(hostView.safeAreaLayoutGuide).offset(16)
                    }
                    hostView.layoutIfNeeded()
                })

            // Auto-hide
            toastView.hideTimer = Timer.scheduledTimer(withTimeInterval: duration, repeats: false) { _ in
                toastView.hide()
            }
        }
    }

    private func hide() {
        UIView.animate(
            withDuration: 0.3,
            animations: {
                self.snp.remakeConstraints { make in
                    make.centerX.equalToSuperview()
                    make.leading.trailing.equalToSuperview().inset(16)
                    make.top.equalTo(self.superview!.safeAreaLayoutGuide).offset(-100)
                }
                self.superview?.layoutIfNeeded()
            }
        ) { _ in
            if let index = ToastView.activeToasts.firstIndex(of: self) {
                ToastView.activeToasts.remove(at: index)
            }
            self.removeFromSuperview()
        }
    }

    // MARK: - Helper Methods
    private static func getKeyWindow(fromView: UIView?) -> UIView? {
        if let fromView = fromView {
            return fromView
        }

        let windowScene = UIApplication.shared.connectedScenes.first { scene in
            (scene as? UIWindowScene) != nil
        }.map { scene -> UIWindowScene? in
            return scene as? UIWindowScene
        }

        return windowScene??.windows.first
    }
}

// MARK: - Usage Examples
extension ToastView {
    class func showSuccess(_ message: String, duration: TimeInterval = 2.0, in view: UIView? = nil) {
        show(message: "✅ " + message, duration: duration, in: view)
    }

    class func showError(_ message: String, duration: TimeInterval = 2.0, in view: UIView? = nil) {
        show(message: "❌ " + message, duration: duration, in: view)
    }

    class func showInfo(_ message: String, duration: TimeInterval = 2.0, in view: UIView? = nil) {
        show(message: "ℹ️ " + message, duration: duration, in: view)
    }
}
