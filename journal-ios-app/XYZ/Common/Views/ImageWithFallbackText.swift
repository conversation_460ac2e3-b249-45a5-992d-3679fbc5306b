//
//  KFImageView.swift

//
//  Created by <PERSON> on 2025/3/26.
//

import UIKit
import Kingfisher

class ImageWithFallbackText: UIView {

    private let imageView = UIImageView()
    private let textLabel = UILabel()

    var imageUrl: String?
    var descriptionText: String = ""

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    private func setupViews() {
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = bounds.width / 2 // Make it a circle
        imageView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(imageView)

        textLabel.textAlignment = .center
        textLabel.textColor = .white
        textLabel.font = UIFont.systemFont(ofSize: 30, weight: .regular) // Adjust font size
        textLabel.backgroundColor = .gray // Adjust background color
        textLabel.layer.cornerRadius = bounds.width / 2 // Make it a circle
        textLabel.clipsToBounds = true
        textLabel.translatesAutoresizingMaskIntoConstraints = false
        addSubview(textLabel)

        NSLayoutConstraint.activate([
            imageView.centerXAnchor.constraint(equalTo: centerXAnchor),
            imageView.centerYAnchor.constraint(equalTo: centerYAnchor),
            imageView.widthAnchor.constraint(equalTo: widthAnchor),
            imageView.heightAnchor.constraint(equalTo: heightAnchor),

            textLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            textLabel.centerYAnchor.constraint(equalTo: centerYAnchor),
            textLabel.widthAnchor.constraint(equalTo: widthAnchor),
            textLabel.heightAnchor.constraint(equalTo: heightAnchor)
        ])
    }

    private func loadImage(imageUrl: String?, descriptionText: String?) {

        imageView.image = nil

        guard let urlString = imageUrl, let url = URL(string: urlString) else {
            textLabel.isHidden = false
            updateTextLabel()
            return
        }

        imageView.kf.setImage(with: url) { result in
            switch result {
            case .failure:
                self.textLabel.isHidden = false
            default:
                break
            }
        }
    }

    private func updateTextLabel() {
        let firstLetter = String(descriptionText.prefix(1)).uppercased()
        textLabel.text = firstLetter
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        imageView.layer.cornerRadius = bounds.width / 2
        textLabel.layer.cornerRadius = bounds.width / 2
    }
}
