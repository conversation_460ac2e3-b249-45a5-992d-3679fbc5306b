//
//  LoadingView.swift

//
//  Created by <PERSON> on 2025/3/20.
//

import UIKit

class LoadingView: UIView {

    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 10
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    private let activityIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.color = .white
        indicator.translatesAutoresizingMaskIntoConstraints = false
        return indicator
    }()

    private let messageLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("loading_text")
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 16)
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    // MARK: - Singleton Instance
    private static var shared: LoadingView = {
        return LoadingView()
    }()

    // MARK: - Initialization
    private override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    // MARK: - View Setup
    private func setupView() {
        backgroundColor = UIColor(white: 0, alpha: 0.3)

        addSubview(containerView)
        containerView.addSubview(activityIndicator)
        containerView.addSubview(messageLabel)

        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            containerView.widthAnchor.constraint(equalToConstant: 160),
            containerView.heightAnchor.constraint(equalToConstant: 120),

            activityIndicator.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(
                equalTo: containerView.centerYAnchor, constant: -16),

            messageLabel.topAnchor.constraint(
                equalTo: activityIndicator.bottomAnchor, constant: 16),
            messageLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor)
        ])
    }

    // MARK: - Loading Management
    private static var loadingCount = 0

    class func showLoading(on view: UIView? = nil, message: String? = nil) {

        let hostView = self.getKeyWindow(fromView: view)

        DispatchQueue.main.async {
            loadingCount += 1

            guard loadingCount == 1 else { return }

            let loadingView = shared
            loadingView.messageLabel.text = message ?? "Loading..."

            if let hostView = hostView {
                loadingView.frame = hostView.bounds
                hostView.addSubview(loadingView)
                loadingView.activityIndicator.startAnimating()
            }
        }
    }

    class func getKeyWindow(fromView: UIView?) -> UIView? {

        if fromView != nil {
            return fromView
        }

        let windowScene = UIApplication.shared.connectedScenes.first { scene in
            (scene as? UIWindowScene) != nil
        }.map { scene -> UIWindowScene? in
            return scene as? UIWindowScene
        }

        return windowScene??.windows.first
    }

    class func hideLoading() {
        DispatchQueue.main.async {
            loadingCount = max(loadingCount - 1, 0)

            guard loadingCount == 0 else { return }

            shared.activityIndicator.stopAnimating()
            shared.removeFromSuperview()
        }
    }

}
