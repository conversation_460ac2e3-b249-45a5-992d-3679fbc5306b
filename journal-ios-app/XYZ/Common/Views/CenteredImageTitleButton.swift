//
//  CeterButton.swift

//
//  Created by <PERSON> on 2025/4/3.
//

import UIKit

class CenteredImageTitleButton: UIControl {

    var imageSize: CGSize = CGSize(width: 25, height: 25) {
        didSet {
            imageView.snp.remakeConstraints { make in
                make.width.height.equalTo(imageSize.width)
                make.left.equalToSuperview()
                make.centerY.equalToSuperview()
            }
        }
    }

    var imagePadding: CGFloat = 8 {
        didSet {
            titleLabel.snp.updateConstraints { make in
                make.left.equalTo(imageView.snp.right).offset(imagePadding)
            }
        }
    }
    var textColor: UIColor = .white {
        didSet {
            titleLabel.textColor = textColor
        }
    }
    var titleColor: UIColor = .white {
        didSet {
            titleLabel.textColor = titleColor
        }
    }

    private let imageView: UIImageView = {
        let view = UIImageView()
        view.contentMode = .scaleAspectFit
        return view
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = .systemFont(ofSize: 18, weight: .regular)
        return label
    }()

    private let stackView: UIView = {
        let stack = UIView()
        return stack
    }()

    var image: UIImage? {
        didSet {
            imageView.image = image
        }
    }

    var title: String? {
        didSet {
            titleLabel.text = title
        }
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    private func setupViews() {

        layer.cornerRadius = 5
        layer.masksToBounds = true

        addSubview(stackView)
        stackView.addSubview(imageView)
        stackView.addSubview(titleLabel)

        stackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        imageView.snp.makeConstraints { make in
            make.width.height.equalTo(25)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(imageView.snp.right).offset(8)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
    }

    override var isHighlighted: Bool {
        didSet {
            alpha = isHighlighted ? 0.7 : 1.0
        }
    }
}
