//
//  UpgradePremiumViewController.swift

//
//  Created by <PERSON> on 2025/2/27.
//

import RevenueCatUI
import SwiftUI

class PremiumViewController: UIHostingController<PremiumView> {

    override init(rootView: PremiumView) {
        super.init(rootView: rootView)
    }

    @MainActor @preconcurrency required dynamic init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
