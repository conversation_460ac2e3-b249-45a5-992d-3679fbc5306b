import Combine
import UIKit
import Foundation

protocol AnalyticsServiceProtocol {
    func trackEvent(_ event: AnalyticsEvent, parameters: [String: Any])
}

enum AnalyticsEvent {
    case journalSaved
    case journalViewed
}

class AnalyticsService: AnalyticsServiceProtocol {
    func trackEvent(_ event: AnalyticsEvent, parameters: [String: Any]) {
        // In a real app, this would send events to an analytics provider
        XYLog("Analytics event: \(event) with parameters: \(parameters)")
    }
}

struct FontFormat {
    enum Weight {
        case thin
        case regular
        case bold

        func toUIFontWeight() -> UIFont.Weight {
            switch self {
            case .bold:
                return .bold
            case .thin:
                return .thin
            case .regular:
                return .regular
            }
        }

    }

    var fontSize: Int = 16
    var weight : Weight = .regular
    var isItalic: Bool = false
    var alignment: NSTextAlignment = .left
    var textColor: String
    var lineHeightMultiple: CGFloat = 1.4

    init(fontSize: Int, weight: Weight, isItalic: Bool, alignment: NSTextAlignment, textColor: String, lineHeightMultiple: CGFloat) {
        self.fontSize = fontSize
        self.weight = weight
        self.isItalic = isItalic
        self.alignment = alignment
        self.textColor = textColor
        self.lineHeightMultiple = lineHeightMultiple
        self.textColor = Theme.primaryColorString()
    }

    init() {
        self.textColor = Theme.primaryColorString()
    }
}

class JournalNewViewModel: ObservableObject, FormatProtocol {
    private var cancellables = Set<AnyCancellable>()
    private let analyticsService: AnalyticsServiceProtocol
    private let journalRepository: JournalRepositoryProtocol
    private var existingJournal: JournalEntry?

    var format: FontFormat = FontFormat()
    var selectedDate: Date = Date()

    var disableEditing: Bool {
        return false
    }

    var isEditMode: Bool {
        return existingJournal != nil
    }

    init(
        analyticsService: AnalyticsServiceProtocol = AnalyticsService(),
        journalRepository: JournalRepositoryProtocol = JournalRepository(),
        journalEntry: JournalEntry? = nil
    ) {
        self.analyticsService = analyticsService
        self.journalRepository = journalRepository
        self.existingJournal = journalEntry

        // If editing an existing journal, load its formatting
        if let journal = journalEntry {
            format.fontSize = journal.fontSize
            format.lineHeightMultiple = journal.lineHeightMultiple
            format.textColor = journal.textColor

            // Load text alignment
            if let alignment = NSTextAlignment(rawValue: journal.textAlignment) {
                format.alignment = alignment
                XYLog("Loaded text alignment from journal: \(journal.textAlignment) -> \(alignment)")
            } else {
                XYLog("Failed to load text alignment: \(journal.textAlignment) is not a valid NSTextAlignment value")
            }

            XYLog("Loaded text color from journal: \(journal.textColor)")
        } else {
            XYLog("Creating new journal with default alignment: \(format.alignment.rawValue)")
            XYLog("Creating new journal with default text color: \(format.textColor)")
        }
    }

    // Create journal with mixed content
    // swiftlint:disable:next function_parameter_count
    func saveJournalWithContentItems(
        title: ContentItem,
        contentItems: [ContentItem],
        contentSnippet: String,
        moodEmoji: String,
        weather: String,
        tags: [String],
        location: String,
        fontSize: Int,
        lineHeightMultiple: CGFloat
    ) -> Bool {
        let journalEntry = JournalEntry(
            id: existingJournal?.id ?? UUID().uuidString,
            title: title,
            contentItems: contentItems,
            contentSnippet: contentSnippet,
            moodEmoji: moodEmoji,
            weather: weather,
            tags: tags,
            location: location,
            fontSize: fontSize,
            lineHeightMultiple: lineHeightMultiple,
            textAlignment: format.alignment.rawValue,
            textColor: format.textColor,
            date: selectedDate
        )

        let saved = journalRepository.saveJournal(journalEntry)
        if saved {
            // Track analytics event
            analyticsService.trackEvent(
                .journalSaved,
                parameters: [
                    "journalId": journalEntry.id,
                    "isEdit": existingJournal != nil
                ])

            // Check if we should show registration prompt for guest users
            checkAndShowRegistrationPrompt()
        }
        return saved
    }

    // Legacy method for backward compatibility
    // swiftlint:disable:next function_parameter_count
    func saveJournalWithMedia(
        title: String,
        content: String,
        contentSnippet: String,
        moodEmoji: String,
        weather: String,
        tags: [String],
        location: String,
        images: [String],
        videos: [String]
    ) -> Bool {
        // Convert to content items
        var contentItems: [ContentItem] = []

        // Add main content as first text item
        if !content.isEmpty {
            contentItems.append(ContentItem(id: UUID().uuidString, type: .text, content: content))
        }

        // Add all images and videos
        for imageURL in images {
            contentItems.append(ContentItem(id: UUID().uuidString, type: .image, content: imageURL))
        }

        for videoURL in videos {
            contentItems.append(ContentItem(id: UUID().uuidString, type: .video, content: videoURL))
        }

        // Use the new method
        return saveJournalWithContentItems(
            title: ContentItem(id: "", type: .text, content: title),
            contentItems: contentItems,
            contentSnippet: contentSnippet,
            moodEmoji: moodEmoji,
            weather: weather,
            tags: tags,
            location: location,
            fontSize: format.fontSize,
            lineHeightMultiple: format.lineHeightMultiple
        )
    }

    func getExistingJournal() -> JournalEntry? {
        return existingJournal
    }

    // Check if we should show registration prompt for guest users
    private func checkAndShowRegistrationPrompt() {
        // Only show for guest users
        guard App.shared.isInGuestMode() else { return }

        // Get total journal count
        let totalJournals = DB.shared.getJournalEntriesCount()

        // Show prompt after 3, 7, or 15 journals (not too intrusive)
        let promptThresholds = [3, 7, 15]

        if promptThresholds.contains(totalJournals) {
            // Post notification to show registration prompt
            NotificationCenter.default.post(
                name: NSNotification.Name("ShowRegistrationPrompt"),
                object: nil,
                userInfo: ["journalCount": totalJournals]
            )
        }
    }
}
