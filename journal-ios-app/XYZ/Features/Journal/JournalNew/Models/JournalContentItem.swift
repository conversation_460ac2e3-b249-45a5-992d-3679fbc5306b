import UIKit

// MARK: - Models
enum JournalContentItemType {
    case title
    case content
    case image
    case video
    case audio
}

class JournalContentItem {
    var type: JournalContentItemType
    var text: String = ""
    var url: URL?
    var thumbnail: UIImage?
    var duration: TimeInterval = 0
    var widthMode: WidthMode = .full
    var size: CGSize = .zero

    // For binding to views
    weak var associatedView: UIView?

    // MARK: - Initializers
    init(type: JournalContentItemType) {
        self.type = type
    }

    static func title(text: String) -> JournalContentItem {
        let item = JournalContentItem(type: .title)
        item.text = text
        return item
    }

    static func content(text: String) -> JournalContentItem {
        let item = JournalContentItem(type: .content)
        item.text = text
        return item
    }

    static func image(url: URL) -> JournalContentItem {
        let item = JournalContentItem(type: .image)
        item.url = url
        return item
    }

    static func video(url: URL, thumbnail: UIImage?) -> JournalContentItem {
        let item = JournalContentItem(type: .video)
        item.url = url
        item.thumbnail = thumbnail
        return item
    }

    static func audio(url: URL, duration: TimeInterval) -> JournalContentItem {
        let item = JournalContentItem(type: .audio)
        item.url = url
        item.duration = duration
        return item
    }

    func setWidthMode(mode: WidthMode) -> JournalContentItem {
        self.widthMode = mode
        return self
    }

    func setSize(size: CGSize) -> JournalContentItem {
        self.size = size
        return self
    }
}

// MARK: - JournalContentItem Extension
extension JournalContentItem {
    func isSameItem(as other: JournalContentItem) -> Bool {
        // First check if they're the same object
        if self === other {
            return true
        }

        // If types don't match, they're not the same
        if self.type != other.type {
            return false
        }

        // Check for equality based on type
        switch type {
        case .title, .content:
            return text == other.text
        case .image, .video:
            return url == other.url
        case .audio:
            return url == other.url && duration == other.duration
        }
    }

    // Convert to ContentItem for database storage
    func toContentItem() -> ContentItem {
        switch type {
        case .title, .content:
            return ContentItem(
                id: UUID().uuidString, type: type == .title ? .text : .text, content: text)
        case .image:
            return ContentItem(
                id: UUID().uuidString, type: .image, content: url?.absoluteString ?? "")
        case .video:
            return ContentItem(
                id: UUID().uuidString, type: .video, content: url?.absoluteString ?? "")
        case .audio:
            return ContentItem(
                id: UUID().uuidString,
                type: .audio,
                content: url?.absoluteString ?? "",
                metadata: ["duration": String(duration)]
            )
        }
    }
}
