import UIKit

class JournalNewViewConfigurator {
    func configure() -> UIViewController {
        let viewModel = JournalNewViewModel()
        let viewController = JournalNewViewController()
        viewController.viewModel = viewModel
        let router = JournalNewRouter(viewController: viewController)
        viewController.router = router

        let navigationController = NavigationViewController(rootViewController: viewController)
        navigationController.modalPresentationStyle = .fullScreen
        return navigationController
    }

    func configureForEditing(journalEntry: JournalEntry) -> UIViewController {
        let viewModel = JournalNewViewModel(journalEntry: journalEntry)
        let viewController = JournalNewViewController()
        viewController.viewModel = viewModel
        viewController.isEditingMode = true
        let router = JournalNewRouter(viewController: viewController)
        viewController.router = router
        let navigationController = NavigationViewController(rootViewController: viewController)
        navigationController.modalPresentationStyle = .fullScreen
        return navigationController
    }

    func configureForUserList() -> UIViewController {
        let listVC = JournalListViewController()
        listVC.router = JournalListRouter(viewController: listVC)
        listVC.viewModel = JournalListViewModel()
        listVC.title = "日记列表"
        return listVC
    }

}
