import AVFoundation
import AVKit
import Combine
import ObjectiveC
import PhotosUI
import Photos
import SnapKit
import UIKit
import CoreLocation
import MapKit

// swiftlint:disable file_length
class JournalNewViewController: ThemedViewContoller,
                                MediaContentViewDelegate,PHPickerViewControllerDelegate,
                                UIImagePickerControllerDelegate,
                                UINavigationControllerDelegate,
                                AudioContentViewDelegate, UIScrollViewDelegate {

    // MARK: - Properties
    var viewModel: JournalNewViewModel!
    var router: JournalNewRouter!
    var isEditingMode: Bool = false
    var cancellables = Set<AnyCancellable>()
    let analyticsService: AnalyticsServiceProtocol = AnalyticsService()

    // Widget integration
    var prefilledPrompt: String?

    // Focus tracking
    weak var currentlyFocusedTextView: RSKPlaceholderTextView?
    var lastCursorPosition: Int = 0

    // MARK: - UI Elements
    let scrollView = UIScrollView()
    let contentStackView = UIStackView()
    let headerView = UIView()  // For date, month, and mood

    var presentView: UIView?
    var presentViewController: UIViewController?
    var presentContainerView: UIView = UIView()
    var tagsLocationView: UIView?

    let dateContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    let dayLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 32)
        label.textColor = Colors.themeColorBlue.dynamicColor()
        return label
    }()

    let monthYearLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = Colors.secondaryText.dynamicColor()
        return label
    }()

    let dateArrowButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "chevron.down"), for: .normal)
        button.tintColor = Colors.secondaryText.dynamicColor()
        button.isEnabled = false
        return button
    }()

    let moodButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("😀", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 28)
        return button
    }()

    // Fixed toolbar for when no text view is being edited
    let fixedToolbarView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    let optionContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    let fixedFormattingToolbar = FormattingToolbar()

    // Floating toolbar for use as input accessory view when editing
    var floatingToolbar: FormattingToolbar?

    // Flag to track if we're currently using the floating toolbar
    var isUsingFloatingToolbar = false

    var images: [UIImage] = []
    var selectedMood: String = "😀"
    var selectedWeather: String = "☀️"
    var tableData: [JournalContentItem] = []

    // Location and Tags
    var selectedTags: [String] = []
    var selectedLocation: String = ""

    // MARK: - Lifecycle
    override func viewDidLoad() {
        showCloseBackButton = true
        super.viewDidLoad()
        setupUI()
        setupKeyboardHandling()

        if isEditingMode {
            loadExistingJournal()
        } else if let prompt = prefilledPrompt {
            // Handle prefilled prompt from widget
            handlePrefilledPrompt(prompt)
        }
    }

    // Handle formatting options from either toolbar
    // swiftlint:disable:next cyclomatic_complexity function_body_length
    func handleFormattingOption(_ option: FormattingToolbar.FormattingOption) {
        // First handle media-related options
        switch option {
        case .image:
            presentCamera()
            return
        case .video:
            presentVideoRecorder()
            return
        case .audio:
            recordAudio()
            return
        case .gallery:
            presentPhotoPicker()
            return
        case .emoji(let emoji):
            insertTextAtCurrentPosition(emoji)
            return
        case .list(let isNumbered):
            if let textView = currentlyFocusedTextView {
                TextFormatter.insertFormattedList(to: textView, isNumbered: isNumbered)
            }
            return
        case .link:
            addLink()
            return
        case .location:
            showLocationPicker()
            return
        case .tags:
            showTagsPicker()
            return
        case .media:
            showMediaSelection()
            return
        default:
            break
        }

        // Handle text formatting options
        let isFocusedTextView = currentlyFocusedTextView != nil

        // Update view model format first
        switch option {
        case .alignment(let alignment):
            viewModel.format.alignment = alignment
            applyTextAlignmentToAllMediaViews(alignment)
        case .bold:
            if viewModel.format.weight == .bold {
                viewModel.format.weight = .regular
            } else {
                viewModel.format.weight = .bold
            }
            viewModel.format.isItalic = false
        case .italic:
            viewModel.format.weight = .regular
            viewModel.format.isItalic.toggle()
        case .thin:
            viewModel.format.weight = .thin
        case .regular:
            viewModel.format.weight = .regular
        case .fontSize(let size):
            viewModel.format.fontSize = Int(size)
        case .lineHeight(let lineHeight):
            viewModel.format.lineHeightMultiple = lineHeight
        case .textColor(let textColor):
            if !isFocusedTextView {
                viewModel.format.textColor = textColor
            }
        default:
            break
        }

        // Apply formatting to appropriate text views
        if isFocusedTextView, let textView = currentlyFocusedTextView {
            // Apply to focused text view only
            applyFormatting(option, to: textView)
            self.updateTextItemFromView(textView)
            return
        }

        // Apply to all text views if no focused view
        applyFormattingToAllTextViews { [weak self] textView in
            self?.applyFormatting(option, to: textView)
            self?.updateTextItemFromView(textView)
        }

        // After applying formatting, ensure title text views have proper size and weight
        updateTitleTextViewsFormatting()

        updateContentInScrollView()

        // Refresh placeholders to ensure they remain visible
        refreshAllTextViewPlaceholders()
    }

    private func applyTextAlignmentToAllMediaViews(_ alignment: NSTextAlignment) {
        // Iterate through all subviews of contentStackView
        contentStackView.subviews.forEach {
            if let mediaView = $0.subviews.first as? MediaContentView {
                mediaView.alignment = alignment
                mediaView.updateAlignment()
            }
        }
    }

    // Helper function to apply a specific formatting option to a text view
    // swfitlint:disable:next cyclomatic_complexity
    private func applyFormatting(_ option: FormattingToolbar.FormattingOption, to textView: UITextView) {
        switch option {
        case .underline:
            TextFormatter.applyUnderline(to: textView)
        case .strikethrough:
            TextFormatter.applyStrikethrough(to: textView)
        case .bold:
            TextFormatter.applyFontStyle(to: textView, isBold: true)
        case .italic:
            TextFormatter.applyFontStyle(to: textView, isItalic: viewModel.format.isItalic)
        case .thin:
            TextFormatter.applyFontStyle(to: textView, isBold: false)
        case .regular:
            TextFormatter.applyFontStyle(to: textView, isBold: false, isItalic: false)
        case .fontSize(let size):
            // Check if this is a title text view and apply appropriate font size
            if isTitleTextView(textView) {
                let titleFontSize = size * 1.2
                TextFormatter.applyFontSize(titleFontSize, to: textView)
            } else {
                TextFormatter.applyFontSize(size, to: textView)
            }
        case .lineHeight(let lineHeight):
            TextFormatter.applyLineHeight(lineHeight, to: textView)
        case .alignment(let alignment):
            TextFormatter.applyTextAlignment(alignment, to: textView)
        case .textColor(let hexColor):
            // More robust color handling
            if let color = UIColor(hexString: hexColor) {
                XYLog("Applying color: \(hexColor) to text view")

                // Apply color directly to text view
                textView.textColor = color

                // Then use the TextFormatter to handle attributed text
                TextFormatter.applyTextColor(color, to: textView)

                // Force refresh
                textView.setNeedsDisplay()
            } else {
                XYLog("Failed to create color from hex: \(hexColor)")
            }
        default:
            break
        }
    }

    // Helper function to check if a text view is a title text view
    private func isTitleTextView(_ textView: UITextView) -> Bool {
        if let placeholderTextView = textView as? RSKPlaceholderTextView {
            return placeholderTextView.placeholder == "标题"
        }
        return false
    }

    // Update title text views to have proper formatting (1.2x size and bolder weight)
    private func updateTitleTextViewsFormatting() {
        applyFormattingToAllTextViews { textView in
            if isTitleTextView(textView) {
                // Apply title-specific formatting
                let titleFontSize = CGFloat(viewModel.format.fontSize) * 1.2
                let titleWeight = getBolderWeight(from: viewModel.format.weight)

                // Create the proper font
                let font: UIFont
                if viewModel.format.isItalic {
                    font = UIFont.italicSystemFont(ofSize: titleFontSize)
                } else {
                    font = UIFont.systemFont(ofSize: titleFontSize, weight: titleWeight.toUIFontWeight())
                }

                // Apply to the entire text view
                let attributedText = NSMutableAttributedString(
                    attributedString: textView.attributedText ?? NSAttributedString(string: textView.text)
                )
                let range = NSRange(location: 0, length: attributedText.length)

                if range.length > 0 {
                    attributedText.addAttribute(.font, value: font, range: range)
                    textView.attributedText = attributedText
                }

                // Also update the base font property
                textView.font = font
            }
        }
    }

    func saveBarButton() -> UIBarButtonItem {
        let button = UIButton(type: .custom)
        button.frame = .init(x: 0, y: 0, width: 70, height: 32)
        button.setTitle("保存", for: .normal)
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        button.titleLabel?.textAlignment = .center
        button.backgroundColor = Colors.themeColor.light
        button.layer.cornerRadius = 5
        button.layer.masksToBounds = true
        button.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        return UIBarButtonItem(customView: button)
    }

    func handleOptionView(_ optionView: UIView, dismissed: Bool) {
        if dismissed {
            if let presentView = self.presentView {
                // Animate to final position (slide up)
                if let viewController = self.presentViewController {
                    UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
                        // do nothing
                    } completion: { _ in
                        presentView.removeFromSuperview()
                        self.presentContainerView.removeFromSuperview()
                        self.presentContainerView.gestureRecognizers?.removeAll()
                        self.presentView = nil
                        viewController.dismiss(animated: false)
                    }
                }
            }
        } else {
            self.view.endEditing(true)

            let viewController = BaseViewController(nibName: nil, bundle: nil)
            viewController.view.backgroundColor = .clear
            viewController.modalPresentationStyle = .overCurrentContext
            viewController.view.addSubview(self.presentContainerView)
            self.presentViewController = viewController

            self.presentContainerView.addSubview(optionView)
            self.presentContainerView.backgroundColor = .clear
            self.presentContainerView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }

            // Add tap gesture to dismiss
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissOptionView(_:)))
            self.presentContainerView.addGestureRecognizer(tapGesture)

            optionView.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview()
                make.bottom.equalTo(self.presentContainerView.snp.bottom).offset(0)

                // Set appropriate height based on the view type
                if optionView is LocationPickerView {
                    make.height.equalTo(500)
                } else if optionView is TagsPickerView {
                    make.height.equalTo(400)
                } else {
                    make.height.greaterThanOrEqualTo(200)
                }
            }

            self.presentView = optionView
            self.present(viewController, animated: true)
        }
    }

    func updateDate() {
        let dayFormatter = DateFormatter()
        dayFormatter.dateFormat = "dd"
        dayLabel.text = dayFormatter.string(from: viewModel.selectedDate)

        let monthYearFormatter = DateFormatter()
        monthYearFormatter.dateFormat = "MMMM, yyyy"
        monthYearLabel.text = monthYearFormatter.string(from: viewModel.selectedDate).uppercased()
    }

    private func setupKeyboardHandling() {
        NotificationCenter.default.addObserver(
            self, selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(
            self, selector: #selector(keyboardWillHide),
            name: UIResponder.keyboardWillHideNotification, object: nil)
    }

    // swiftlint:disable:next function_body_length cyclomatic_complexity
    private func loadExistingJournal() {
        guard let journal = viewModel.getExistingJournal() else {
            return
        }

        // Clear existing table data
        tableData.removeAll()

        // Set basic journal data to UI
        selectedMood = journal.moodEmoji
        selectedWeather = journal.weather
        viewModel.selectedDate = journal.date
        selectedTags = journal.tags
        selectedLocation = journal.location

        // Debug: print alignment info from loaded journal
        XYLog("Loaded journal with alignment: \(journal.textAlignment)")
        XYLog("Loaded journal with font size: \(journal.fontSize)")
        XYLog("Loaded journal with line height: \(journal.lineHeightMultiple)")
        XYLog("Current viewModel alignment: \(viewModel.format.alignment.rawValue)")

        // Add the title
        tableData.append(JournalContentItem.title(text: journal.title.content))

        // Update UI
        moodButton.setTitle(selectedMood, for: .normal)
        updateDate()

        // Create table rows for each content item in order
        for item in journal.contentItems {
            switch item.type {
            case .text:
                tableData.append(JournalContentItem.content(text: item.content))

            case .image:
                if let url = URL(string: item.content) {
                    tableData.append(
                        JournalContentItem.image(url: url)
                            .setWidthMode(mode: item.mode)
                            .setSize(size: item.size)
                    )
                }

            case .video:
                if let url = URL(string: item.content) {
                    self.tableData.append(
                        JournalContentItem.video(url: url, thumbnail: nil)
                            .setWidthMode(mode: item.mode)
                            .setSize(size: item.size)
                    )
                }
            case .audio:
                if let url = URL(string: item.content),
                    let durationStr = item.metadata?["duration"],
                    let duration = TimeInterval(durationStr) {
                    tableData.append(JournalContentItem.audio(url: url, duration: duration))
                }
            }
        }

        // If no content cells, add one empty content cell
        if !tableData.contains(where: { $0.type == .content }) {
            tableData.append(JournalContentItem.content(text: ""))
        }

		// if last item is not content, add a new text item
		if case .content = tableData.last?.type {
			// do nothing
		} else {
			// if last item is not content, add a new text item
			tableData.append(JournalContentItem.content(text: ""))
		}

        // Update the scroll view with the content
        updateContentInScrollView()

        // Refresh all placeholders after loading content
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            self?.refreshAllTextViewPlaceholders()
        }
    }

    // Helper method to find the right index for an item being loaded
    private func findIndexForItem(at originalIndex: Int) -> Int? {
        // If it's beyond our current count, append at the end
        if originalIndex >= tableData.count {
            return tableData.count
        }

        // Otherwise, try to maintain the original position
        return originalIndex
    }

    // Define MediaContentType enum outside any method
    enum MediaContentType {
        case image
        case video
        case audio(duration: TimeInterval)
    }

    // Add a method to scroll to the bottom of the scroll view
    private func scrollToBottom() {
        // Scroll to the bottom of the content
        let bottomOffset = CGPoint(
            x: 0,
            y: scrollView.contentSize.height - scrollView.bounds.height
            + scrollView.contentInset.bottom
        )
        scrollView.setContentOffset(bottomOffset, animated: true)
    }

    // MARK: - PHPickerViewControllerDelegate
    // swiftlint:disable:next cyclomatic_complexity function_body_length
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
        picker.dismiss(animated: true)

        self.isLoadingParts = true

        let group = DispatchGroup()
        var newImageURLs: [URL] = []
        var newVideoItems: [(url: URL, thumbnail: UIImage?)] = []

        for result in results {
            let itemProvider = result.itemProvider

            if itemProvider.canLoadObject(ofClass: UIImage.self) {
                group.enter()
                itemProvider.loadObject(ofClass: UIImage.self) { [weak self] object, error in
                    defer { group.leave() }
                    guard let image = object as? UIImage, error == nil else { return }

                    if let imageURL = self?.saveImageToLocal(image) {
                        newImageURLs.append(imageURL)
                        // Extract date from PHPickerResult
                        self?.extractDateFromPHPickerResult(result) { date in
                            DispatchQueue.main.async {
                                if let imageDate = date {
                                    self?.askUserToUseMediaDate(imageDate, mediaType: "图片")
                                }
                            }
                        }
                    }
                }
            } else if itemProvider.hasItemConformingToTypeIdentifier(UTType.movie.identifier) {
                group.enter()
                itemProvider.loadFileRepresentation(forTypeIdentifier: UTType.movie.identifier) { url, error in
                    defer {
                        group.leave()
                    }
                    guard let sourceURL = url, error == nil else { return }

                    do {
                        let destinationURL = try SharedFileManager.shared.copyVideoToShared(from: sourceURL)

                        group.enter()
                        MediaHandler.generateVideoThumbnail(from: destinationURL) { thumbnail in
                            defer { group.leave() }
                            newVideoItems.append((url: destinationURL, thumbnail: thumbnail))
                        }
                    } catch {
                        XYLog("Error copying video file: \(error)")
                    }
                }
            }
        }

        group.notify(queue: .main) { [weak self] in
            guard let self = self else { return }

            self.isLoadingParts = false

            // Add all images
            for imageURL in newImageURLs {
                self.addImageToTable(url: imageURL)
            }

            // Add all videos with date detection
            for videoItem in newVideoItems {
                self.addVideoToTable(url: videoItem.url, thumbnail: videoItem.thumbnail)
            }

            if let videoItem = newVideoItems.first {
                Task {
                    if let videoDate = try await self.extractDateFromVideoURL(videoItem.url) {
                        self.askUserToUseMediaDate(videoDate, mediaType: "视频")
                    }
                }
            }
        }
    }

    // MARK: - UIImagePickerControllerDelegate
    func imagePickerController(
        _ picker: UIImagePickerController,
        didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]
    ) {
        picker.dismiss(animated: true)

        if let videoURL = info[.mediaURL] as? URL {
            // Handle video selection with date detection
            Task {
                do {
                    if let videoDate = try await extractDateFromVideoURL(videoURL) {
                        askUserToUseMediaDate(videoDate, mediaType: "视频")
                    }
                } catch { /* do nothing */ }
            }

            MediaHandler.generateVideoThumbnail(from: videoURL) { [weak self] thumbnail in
                DispatchQueue.main.async {
                    guard let self = self else { return }
                    self.addVideoToTable(url: videoURL, thumbnail: thumbnail)
                }
            }
        } else if let image = info[.originalImage] as? UIImage {
            // Handle image selection - save to file and use URL
            if let imageURL = saveImageToLocal(image) {
                addImageToTable(url: imageURL)

                // Try to extract date from image (limited without EXIF access)
                if let imageDate = extractDateFromImage(image) {
                    askUserToUseMediaDate(imageDate, mediaType: "图片")
                }
            }
        }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }

    func didTapDelete(mediaView: AudioPlayerView) {
        let alert = UIAlertController(title: "删除音频", message: "确定要删除这个音频吗？", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            // Find the container view that contains this media view
            var containerView: UIView? = mediaView.superview
            while containerView != nil && containerView?.superview != self?.contentStackView {
                containerView = containerView?.superview
            }

            if let containerView = containerView as? JournalItemContainerView,
               let contentItem: JournalContentItem = containerView.journalItem {
                // Remove this item from table data
                if let itemIndex = self?.tableData.firstIndex(where: { $0 === contentItem }) {
                    self?.tableData.remove(at: itemIndex)
                    self?.mergeAdjacentTextItems()
                    self?.updateContentInScrollView()
                }
            }
        })
        present(alert, animated: true)
    }

    // MARK: - MediaContentViewDelegate
    func didTapDelete(mediaView: MediaContentView, mediaType: MediaContentView.MediaType) {
        var mediaString: String = "媒体"
        if case MediaContentView.MediaType.image = mediaType {
            mediaString = "图片"
        } else if case MediaContentView.MediaType.video = mediaType {
            mediaString = "视频"
        }

        let alert = UIAlertController(title: "删除\(mediaString)", message: "确定要删除这个\(mediaString)吗？", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            // Find the container view that contains this media view
            var containerView: UIView? = mediaView.superview
            while containerView != nil && containerView?.superview != self?.contentStackView {
                containerView = containerView?.superview
            }

            if let containerView = containerView as? JournalItemContainerView,
               let contentItem: JournalContentItem = containerView.journalItem {
                // Remove this item from table data
                if let itemIndex = self?.tableData.firstIndex(where: { $0 === contentItem }) {
                    self?.tableData.remove(at: itemIndex)
                    self?.mergeAdjacentTextItems()
                    self?.updateContentInScrollView()
                }
            }
        })
        present(alert, animated: true)
    }

    // Reload table data
    // if next item is text view, if it is empty should remove it from the data
    func reloadTableData() {
        updateContentInScrollView()
    }

    func didChangeWidthMode(mediaView: MediaContentView, isFullWidth: Bool) {
        // get associated container view
        var containerView: UIView? = mediaView.superview
        while containerView != nil && containerView?.superview != contentStackView {
            containerView = containerView?.superview
        }

        // get associated content item
        if let containerView = containerView as? JournalItemContainerView,
           let contentItem: JournalContentItem = containerView.journalItem {
            contentItem.widthMode = isFullWidth ? .full : .half
        }
    }

    @objc private func saveButtonTapped() {
        saveJournal()
    }

    @objc func dateButtonTapped() {
        showDatePicker()
    }

    @objc func moodButtonTapped() {
        showMoodPicker()
    }

    private func insertTextAtCurrentPosition(_ text: String) {
        guard let textView = currentlyFocusedTextView, let selectedRange = textView.selectedTextRange else { return }
        textView.replace(selectedRange, withText: text)
    }

    @objc private func formatText() {
        // Now handled by FormattingToolbar
    }

    private func showColorPicker(for textView: UITextView) {
        let colorPicker = UIColorPickerViewController()
        colorPicker.delegate = self
        present(colorPicker, animated: true)
    }

    private func showFontSizePicker(for textView: UITextView) {
        let alertController = UIAlertController(
            title: "选择字体大小", message: nil, preferredStyle: .actionSheet)

        let sizes = [12, 14, 16, 18, 20, 24, 28, 32]

        for size in sizes {
            alertController.addAction(
                UIAlertAction(title: "\(size)", style: .default) { [weak self] _ in
                    guard let textView = self?.currentlyFocusedTextView else { return }
                    TextFormatter.applyFontSize(CGFloat(size), to: textView)
                })
        }

        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(alertController, animated: true)
    }

    @objc private func formatList() {
        // Now handled by FormattingToolbar
    }

    @objc private func addLink() {
        guard let textView = currentlyFocusedTextView else { return }

        // Check if text is selected
        guard let selectedRange = textView.selectedTextRange, let selectedText = textView.text(in: selectedRange), !selectedText.isEmpty
        else {
            // No text selected, show alert
            router.showErrorAlert(title: "无法添加链接", message: "请先选择要添加链接的文本")
            return
        }

        let alertController = UIAlertController(title: "添加链接", message: nil, preferredStyle: .alert)

        alertController.addTextField { textField in
            textField.placeholder = "请输入URL"
            textField.text = "https://"
            textField.keyboardType = .URL
        }

        alertController.addAction(
            UIAlertAction(title: "确定", style: .default) { [weak self, weak alertController] _ in
                guard let urlString = alertController?.textFields?.first?.text,
                        !urlString.isEmpty,
                        let url = URL(string: urlString)
                else {
                    self?.router.showErrorAlert(title: "无效的URL", message: "请输入有效的URL")
                    return
                }

                TextFormatter.applyLink(url, to: textView, range: selectedRange)
            })

        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(alertController, animated: true)
    }

    @objc private func recordAudio() {
        // Show the new recording view controller
        let recordVC = RecordViewController()
        recordVC.delegate = self
        recordVC.modalTransitionStyle = .crossDissolve
        recordVC.modalPresentationStyle = .overFullScreen
        recordVC.view.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        router.showRecording(recordVC)
    }

    @objc private func keyboardWillShow(_ notification: Notification) {
        guard
            let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey]
                as? CGRect
        else {
            return
        }

        let bottomInset = keyboardFrame.height - view.safeAreaInsets.bottom
        scrollView.contentInset.bottom = bottomInset
        scrollView.verticalScrollIndicatorInsets.bottom = bottomInset

        if let focusedTextView = currentlyFocusedTextView {
            // Find the container view that contains the textView
            var containerView: UIView? = focusedTextView.superview
            while containerView != nil && containerView?.superview != contentStackView {
                containerView = containerView?.superview
            }

            if let containerView = containerView {
                // Scroll to make the container view visible
                scrollView.scrollRectToVisible(containerView.frame, animated: true)
            }
        }

        UIView.animate(withDuration: 0.3) {
            self.tagsLocationView?.alpha = 0
            self.tagsLocationView?.alpha = 0
        } completion: { _ in
            self.tagsLocationView?.isHidden = true
            self.fixedToolbarView.isHidden = true
        }
    }

    @objc private func keyboardWillHide(_ notification: Notification) {
        scrollView.contentInset.bottom = 0
        scrollView.verticalScrollIndicatorInsets.bottom = 0

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            guard let self = self else { return }
            self.fixedToolbarView.isHidden = false
            self.tagsLocationView?.isHidden = false

            UIView.animate(withDuration: 0.3) {
                self.tagsLocationView?.alpha = 1
                self.tagsLocationView?.alpha = 1
            }
        }
    }

    private func showDatePicker() {
        let datePicker = UIDatePicker()
        datePicker.datePickerMode = .date
        datePicker.preferredDatePickerStyle = .wheels
        datePicker.date = viewModel.selectedDate

        let alert = UIAlertController(
            title: "选择日期", message: "\n\n\n\n\n\n\n\n\n", preferredStyle: .actionSheet)
        alert.view.addSubview(datePicker)
        datePicker.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(20)
        }

        alert.addAction(
            UIAlertAction(title: "确定", style: .default) { [weak self] _ in
                self?.viewModel.selectedDate = datePicker.date
                self?.updateDate()
            })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(alert, animated: true)
    }

    private func showMoodPicker() {
        let moodPicker = UIAlertController(
            title: "选择心情", message: nil, preferredStyle: .actionSheet)
        let moods = ["😀", "😊", "😐", "😔", "😡", "🥰", "😴"]

        for mood in moods {
            moodPicker.addAction(
                UIAlertAction(title: mood, style: .default) { [weak self] _ in
                    self?.selectedMood = mood
                    self?.moodButton.setTitle(mood, for: .normal)
                })
        }

        moodPicker.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(moodPicker, animated: true)
    }

    // Helper method to apply formatting to all text views in the content stack view
    private func applyFormattingToAllTextViews(formatter: (UITextView) -> Void) {
        for case let containerView as JournalItemContainerView in contentStackView.arrangedSubviews {
            for case let textView as UITextView in containerView.subviews {
                formatter(textView)
            }
        }
    }

    // Helper method specifically for applying text color to all text views
    private func applyTextColorToAllTextViews(hexColor: String) {
        guard let color = UIColor(hexString: hexColor) else {
            XYLog("Failed to create color from hex: \(hexColor)")
            return
        }

        XYLog("Applying text color \(hexColor) to all text views")

        // Update the view model
        viewModel.format.textColor = hexColor

        // Apply to all text views
        applyFormattingToAllTextViews { textView in
            // First set the direct property
            textView.textColor = color

            // Then handle attributed text
            if let attributedText = textView.attributedText {
                let mutableAttributedText = NSMutableAttributedString(attributedString: attributedText)
                let range = NSRange(location: 0, length: mutableAttributedText.length)
                mutableAttributedText.addAttribute(.foregroundColor, value: color, range: range)
                textView.attributedText = mutableAttributedText
            }

            // Force refresh
            textView.setNeedsDisplay()
        }
    }

    // Helper method specifically for applying text alignment to all text views
    private func applyTextAlignmentToAllTextViews(alignment: NSTextAlignment) {
        XYLog("Applying text alignment \(alignment) to all text views")

        // Update the view model
        viewModel.format.alignment = alignment
        XYLog("Updated viewModel alignment to: \(viewModel.format.alignment.rawValue)")

        // Apply to all text views
        applyFormattingToAllTextViews { textView in
            textView.textAlignment = alignment
            TextFormatter.applyTextAlignment(alignment, to: textView)
        }
    }

    func showLocationPicker() {
        let locationPickerVC = LocationPickerViewController()
        locationPickerVC.delegate = self
        locationPickerVC.modalPresentationStyle = .overFullScreen
        locationPickerVC.modalTransitionStyle = .crossDissolve
        present(locationPickerVC, animated: true)
    }

    func showTagsPicker() {
        let tagsPickerVC = TagsPickerViewController()
        tagsPickerVC.delegate = self
        tagsPickerVC.setTags(selectedTags)
        tagsPickerVC.modalPresentationStyle = .overFullScreen
        tagsPickerVC.modalTransitionStyle = .crossDissolve
        present(tagsPickerVC, animated: true)
    }

    func showMediaSelection() {
        let mediaSelectionVC = MediaSelectionViewController()
        mediaSelectionVC.delegate = self
        mediaSelectionVC.modalPresentationStyle = .overFullScreen
        mediaSelectionVC.modalTransitionStyle = .crossDissolve
        present(mediaSelectionVC, animated: false)
    }

    @objc private func dismissOptionView(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: presentContainerView)
        if let presentView = self.presentView {
            // Only dismiss if tap is outside the option view
            if !presentView.frame.contains(location) {
                handleOptionView(presentView, dismissed: true)
            }
        }
    }

    // MARK: - Media Date Detection
    private func extractDateFromImage(_ image: UIImage) -> Date? {
        // This would require access to the original image file with EXIF data
        // For now, return nil as we can't extract EXIF from UIImage directly
        return nil
    }

    private func extractDateFromPHPickerResult(_ result: PHPickerResult, completion: @escaping (Date?) -> Void) {
        if let assetIdentifier = result.assetIdentifier {
            let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
            if let asset = fetchResult.firstObject {
                completion(asset.creationDate)
                return
            }
        }
        completion(nil)
    }

    private func extractDateFromVideoURL(_ url: URL) async throws -> Date? {
        let asset = AVURLAsset(url: url)
        let metadata = try await asset.load(.metadata)

        for item in metadata where item.commonKey == .commonKeyCreationDate {
            if let dateValue = try await item.load(.value) as? Date {
                return dateValue
            }
            if let dateString = try await item.load(.value) as? String {
                let formatter = ISO8601DateFormatter()
                let date: Date? = formatter.date(from: dateString)
                return date
            }
        }
        return nil
    }

    private func askUserToUseMediaDate(_ mediaDate: Date, mediaType: String) {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short

        let alert = UIAlertController(
            title: "使用\(mediaType)日期",
            message: "检测到\(mediaType)的拍摄时间是 \(formatter.string(from: mediaDate))，是否使用这个日期作为日记日期？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "使用", style: .default) { [weak self] _ in
            self?.viewModel.selectedDate = mediaDate
            self?.updateDate()
        })

        alert.addAction(UIAlertAction(title: "保持当前日期", style: .cancel))

        present(alert, animated: true)
    }

    // Force update a specific text item from its associated text view
    private func updateTextItemFromView(_ textView: UITextView) {
        if let containerView = textView.superview as? JournalItemContainerView,
           let item = containerView.journalItem {
            // Force update the item's text with current HTML content
            if let currentHTML = textView.attributedText.toHTMLString(), !currentHTML.isEmpty {
                item.text = currentHTML
            }
        }
    }

    // Refresh all text view placeholders to ensure visibility
    private func refreshAllTextViewPlaceholders() {
        for view in contentStackView.arrangedSubviews {
            if let containerView = view as? JournalItemContainerView,
               let textView = containerView.subviews.first as? RSKPlaceholderTextView {
                textView.refreshPlaceholder()
            }
        }
    }
}

// MARK: - UITextViewDelegate
extension JournalNewViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        if let containerView = textView.superview as? JournalItemContainerView,
            let item = containerView.journalItem {
            // Force update with current HTML content, ensuring formatting is captured
            let currentHTML = textView.attributedText.toHTMLString() ?? ""
            item.text = currentHTML
            scrollView.scrollRectToVisible(containerView.frame, animated: true)
        }
    }

    func textViewDidBeginEditing(_ textView: UITextView) {

        fixedToolbarView.isHidden = true

        // Track currently focused text view
        currentlyFocusedTextView = textView as? RSKPlaceholderTextView

        // Save cursor position
        if let selectedRange = textView.selectedTextRange {
            lastCursorPosition = textView.offset(
                from: textView.beginningOfDocument, to: selectedRange.start)
        }

        // Set the floating toolbar as the input accessory view
        if !isUsingFloatingToolbar {
            isUsingFloatingToolbar = true
        }

        // Update both toolbars with the current text view
        floatingToolbar?.textView = textView
        fixedFormattingToolbar.textView = textView

        // Check if there is a selected range and update the toolbars
        let hasSelection: Bool
        if let selectedRange = textView.selectedTextRange {
            hasSelection = textView.offset(from: selectedRange.start, to: selectedRange.end) > 0
        } else {
            hasSelection = false
        }
        floatingToolbar?.updateForTextSelection(isTextSelected: hasSelection)
        fixedFormattingToolbar.updateForTextSelection(isTextSelected: hasSelection)

        // Find the container view that contains the textView
        var containerView: UIView? = textView.superview
        while containerView != nil && containerView?.superview != contentStackView {
            containerView = containerView?.superview
        }

        if let containerView = containerView {
            // Scroll to make the container view visible
            scrollView.scrollRectToVisible(containerView.frame, animated: true)
        }
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        // Clear focus if this text view is losing focus
        if currentlyFocusedTextView === textView {
            currentlyFocusedTextView = nil

            // Check if any text view is still being edited
            let isAnyTextViewEditing = contentStackView.arrangedSubviews.contains { view in
                if let containerView = view as? JournalItemContainerView,
                    let textView = containerView.subviews.first as? RSKPlaceholderTextView {
                    return textView.isFirstResponder
                }
                return false
            }

            // If no text view is being edited, show the fixed toolbar again
            if !isAnyTextViewEditing && isUsingFloatingToolbar {
                isUsingFloatingToolbar = false
            }
        }
    }

    func textView(
        _ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String
    ) -> Bool {
        return true
    }

    func textViewDidChangeSelection(_ textView: UITextView) {
        // Detect if there is a selected range
        let hasSelection: Bool
        if let selectedRange = textView.selectedTextRange {
            hasSelection = textView.offset(from: selectedRange.start, to: selectedRange.end) > 0
        } else {
            hasSelection = false
        }

        // Update both toolbars
        floatingToolbar?.updateForTextSelection(isTextSelected: hasSelection)
        fixedFormattingToolbar.updateForTextSelection(isTextSelected: hasSelection)
    }
}

// MARK: - UIColorPickerViewControllerDelegate
extension JournalNewViewController: UIColorPickerViewControllerDelegate {
    func colorPickerViewControllerDidFinish(_ viewController: UIColorPickerViewController) {
        guard let textView = currentlyFocusedTextView else { return }
        TextFormatter.applyTextColor(viewController.selectedColor, to: textView)
    }

    func colorPickerViewController(
        _ viewController: UIColorPickerViewController, didSelect color: UIColor, continuously: Bool
    ) {
        // Optional: apply color continuously while the user is selecting
        if continuously {
            guard let textView = currentlyFocusedTextView else { return }
            TextFormatter.applyTextColor(color, to: textView)
        }
    }
}

// MARK: - RecordViewControllerDelegate
extension JournalNewViewController: RecordViewControllerDelegate {
    func recordingFinished(url: URL, duration: TimeInterval) {
        addAudioToTable(url: url, duration: duration)
    }
}

// MARK: - UIScrollViewDelegate
extension JournalNewViewController {
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {

    }

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
    }
}

// MARK: - LocationPickerViewControllerDelegate
extension JournalNewViewController: LocationPickerViewControllerDelegate {
    func locationPickerDidSelectLocation(_ location: String, coordinate: CLLocationCoordinate2D?) {
        selectedLocation = location
        updateLocationAndTagsDisplay()
    }

    func locationPickerDidCancel() {
        // Nothing to do
    }
}

// MARK: - TagsPickerViewControllerDelegate
extension JournalNewViewController: TagsPickerViewControllerDelegate {
    func tagsPickerDidUpdateTags(_ tags: [String]) {
        selectedTags = tags
        updateLocationAndTagsDisplay()
    }

    func tagsPickerDidCancel() {
        // Nothing to do
    }
}

// MARK: - MediaSelectionViewControllerDelegate
extension JournalNewViewController: MediaSelectionViewControllerDelegate {
    func mediaSelectionDidSelectPhotoLibrary() {
        presentPhotoPicker()
    }

    func mediaSelectionDidSelectCamera() {
        presentCamera()
    }

    func mediaSelectionDidSelectVideo() {
        presentVideoRecorder()
    }

    func mediaSelectionDidCancel() {
        // Nothing to do
    }

    // MARK: - Widget Integration

    private func handlePrefilledPrompt(_ prompt: String) {
        // Add the prompt as the first text content item
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }

            // Create a text content item with the prompt
            let promptText = "💭 \(prompt)\n\n"

            // Find the first text view and add the prompt
            if let firstTextView = self.contentStackView.arrangedSubviews.first(where: { $0 is RSKPlaceholderTextView }) as? RSKPlaceholderTextView {
                firstTextView.text = promptText
                firstTextView.becomeFirstResponder()

                // Position cursor at the end
                let endPosition = firstTextView.endOfDocument
                firstTextView.selectedTextRange = firstTextView.textRange(from: endPosition, to: endPosition)
            } else {
                // If no text view exists, create one with the prompt
                self.addTextContentItem(with: promptText)
            }
        }
    }

    private func addTextContentItem(with text: String = "") {
        let textView = RSKPlaceholderTextView()
        textView.placeholder = "Start writing..."
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.backgroundColor = .clear
        textView.text = text
        textView.delegate = self

        // Add to content stack
        contentStackView.addArrangedSubview(textView)

        // Set constraints
        textView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(100)
        }

        // Focus the text view
        textView.becomeFirstResponder()
    }
}
