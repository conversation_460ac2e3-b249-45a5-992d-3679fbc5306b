import UIKit

// Helper class for text formatting operations
class TextFormatter {
    // Apply text color on all text or selected (if there are any)
    static func applyTextColor(_ color: UIColor, to textView: UITextView) {
        // First, directly set the textColor property for immediate effect
        textView.textColor = color

        // For selected text or to preserve other attributes, use attributed text
        if let selectedRange = textView.selectedTextRange,
            let selectedText = textView.text(in: selectedRange),
            !selectedText.isEmpty {

            // Get the selected range in NSRange format
            let start = textView.offset(from: textView.beginningOfDocument, to: selectedRange.start)
            let end = textView.offset(from: textView.beginningOfDocument, to: selectedRange.end)
            let range = NSRange(location: start, length: end - start)

            // Create a mutable attributed string
            let attributedText = NSMutableAttributedString(attributedString: textView.attributedText ?? NSAttributedString(string: textView.text))

            // Apply color to the selected range
            attributedText.addAttribute(.foregroundColor, value: color, range: range)

            // Update the text view with the modified attributed text
            textView.attributedText = attributedText
        } else {
            // If no selection, create a new attributed string with the color applied to all text
            let attributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: color,
                .font: textView.font ?? UIFont.systemFont(ofSize: 16)
            ]

            // Create a new attributed string with the color
            let newAttributedText = NSAttributedString(string: textView.text, attributes: attributes)

            // Set the attributed text
            textView.attributedText = newAttributedText
        }

        // Force layout update
        textView.setNeedsDisplay()
    }

    // Apply text alignment
    static func applyTextAlignment(_ alignment: NSTextAlignment, to textView: UITextView) {

        let attributedText = NSMutableAttributedString(
            attributedString: textView.attributedText ?? NSAttributedString(string: textView.text))
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = alignment

        let range: NSRange = NSRange(location: 0, length: attributedText.length)
        attributedText.addAttribute(.paragraphStyle, value: paragraphStyle, range: range)
        textView.attributedText = attributedText

        textView.sizeToFit()
    }

    // Apply font style (bold, italic)
    static func applyFontStyle(
        to textView: UITextView, isBold: Bool = false, isItalic: Bool = false
    ) {
        let attributedText = NSMutableAttributedString(
            attributedString: textView.attributedText ?? NSAttributedString(string: textView.text))

        // Get selected range or use entire text
        let range: NSRange
        if let selectedRange = textView.selectedTextRange,
            let selectedText = textView.text(in: selectedRange),
            !selectedText.isEmpty {
            // Get range of selected text
            let start = textView.offset(from: textView.beginningOfDocument, to: selectedRange.start)
            let end = textView.offset(from: textView.beginningOfDocument, to: selectedRange.end)
            range = NSRange(location: start, length: end - start)
        } else {
            range = NSRange(location: 0, length: attributedText.length)
        }

        if range.length == 0 || attributedText.length == 0 {
            return
        }

        // Determine font
        var font = textView.font ?? UIFont.systemFont(ofSize: 18)

        if isBold {
            // Toggle bold
            if let fontDescriptor = attributedText.attribute(
                .font, at: range.location, effectiveRange: nil) as? UIFont {
                let isBoldAttribute = fontDescriptor.fontDescriptor.symbolicTraits.contains(
                    .traitBold)

                if isBoldAttribute {
                    // Remove bold
                    font = UIFont.systemFont(ofSize: font.pointSize)
                } else {
                    // Add bold
                    font = UIFont.boldSystemFont(ofSize: font.pointSize)
                }
            } else {
                // Default to bold
                font = UIFont.boldSystemFont(ofSize: font.pointSize)
            }
        }

        if isItalic {
            // Toggle italic similarly (simplified implementation)
            if let fontDescriptor = attributedText.attribute(
                .font, at: range.location, effectiveRange: nil) as? UIFont {
                let isItalicAttribute = fontDescriptor.fontDescriptor.symbolicTraits.contains(
                    .traitItalic)

                if isItalicAttribute {
                    // Remove italic
                    font = UIFont.systemFont(ofSize: font.pointSize)
                } else {
                    // Add italic - this is simplified
                    if let italicDescriptor = fontDescriptor.fontDescriptor.withSymbolicTraits(
                        .traitItalic) {
                        font = UIFont(descriptor: italicDescriptor, size: font.pointSize)
                    }
                }
            }
        }

        attributedText.addAttribute(.font, value: font, range: range)
        textView.attributedText = attributedText

        textView.sizeToFit()
    }

    // Apply font size
    static func applyFontSize(_ size: CGFloat, to textView: UITextView) {
        let attributedText = NSMutableAttributedString(
            attributedString: textView.attributedText ?? NSAttributedString(string: textView.text))

        // Get range
        let range: NSRange
        if let selectedRange = textView.selectedTextRange,
            let selectedText = textView.text(in: selectedRange),
            !selectedText.isEmpty {
            let start = textView.offset(from: textView.beginningOfDocument, to: selectedRange.start)
            let end = textView.offset(from: textView.beginningOfDocument, to: selectedRange.end)
            range = NSRange(location: start, length: end - start)
        } else {
            range = NSRange(location: 0, length: attributedText.length)
        }

        if range.length == 0 || attributedText.length == 0 {
            return
        }

        // Get current font
        var font = textView.font ?? UIFont.systemFont(ofSize: size)

        if let existingFont = attributedText.attribute(
            .font, at: range.location, effectiveRange: nil) as? UIFont {
            // Create new font with same traits but new size
            let traits = existingFont.fontDescriptor.symbolicTraits
            if let newFontDescriptor = existingFont.fontDescriptor.withSymbolicTraits(traits) {
                font = UIFont(descriptor: newFontDescriptor, size: size)
            } else {
                font = UIFont(descriptor: existingFont.fontDescriptor, size: size)
            }
        } else {
            font = UIFont.systemFont(ofSize: size)
        }

        attributedText.addAttribute(.font, value: font, range: range)
        textView.attributedText = attributedText

        textView.sizeToFit()
    }

    // Apply line height
    static func applyLineHeight(_ lineHeight: CGFloat, to textView: UITextView) {
        let attributedText = NSMutableAttributedString(
            attributedString: textView.attributedText ?? NSAttributedString(string: textView.text))

        // Get range
        let range: NSRange
        if let selectedRange = textView.selectedTextRange,
            let selectedText = textView.text(in: selectedRange),
            !selectedText.isEmpty {
            let start = textView.offset(from: textView.beginningOfDocument, to: selectedRange.start)
            let end = textView.offset(from: textView.beginningOfDocument, to: selectedRange.end)
            range = NSRange(location: start, length: end - start)
        } else {
            range = NSRange(location: 0, length: attributedText.length)
        }

        if range.length == 0 || attributedText.length == 0 {
            return
        }

        // Create paragraph style with line height
        let paragraphStyle = NSMutableParagraphStyle()

        // Get existing paragraph style if any
        if let existingStyle = attributedText.attribute(
            .paragraphStyle, at: range.location, effectiveRange: nil) as? NSParagraphStyle {
            paragraphStyle.setParagraphStyle(existingStyle)
        }

        paragraphStyle.lineHeightMultiple = lineHeight

        attributedText.addAttribute(.paragraphStyle, value: paragraphStyle, range: range)
        textView.attributedText = attributedText

        // Also update the text view's lineHeightMultiple property if it's an RSKPlaceholderTextView
        if let rskTextView = textView as? RSKPlaceholderTextView {
            rskTextView.lineHeightMultiple = lineHeight
        }

        textView.sizeToFit()
    }

    // Apply hyperlink
    static func applyLink(_ url: URL, to textView: UITextView, range: UITextRange) {
        // Get the range in NSRange format
        let start = textView.offset(from: textView.beginningOfDocument, to: range.start)
        let end = textView.offset(from: textView.beginningOfDocument, to: range.end)
        let nsRange = NSRange(location: start, length: end - start)

        // Apply link attribute
        let attributedText = NSMutableAttributedString(
            attributedString: textView.attributedText ?? NSAttributedString(string: textView.text))
        attributedText.addAttribute(.link, value: url, range: nsRange)

        // Update text view
        textView.attributedText = attributedText

        textView.sizeToFit()
    }

    // Insert formatted list (bullet or numbered)
    static func insertFormattedList(to textView: UITextView, isNumbered: Bool) {
        // Get text and position
        guard let selectedRange = textView.selectedTextRange else { return }

        let cursorPosition = textView.offset(
            from: textView.beginningOfDocument, to: selectedRange.start)

        // Find line start
        let text = textView.text as NSString
        let lineStart = text.lineRange(for: NSRange(location: cursorPosition, length: 0)).location

        // Create list item prefix
        let prefix = isNumbered ? "1. " : "• "

        // Insert at line start
        let insertRange = NSRange(location: lineStart, length: 0)
        textView.textStorage.insert(NSAttributedString(string: prefix), at: insertRange.location)

        // Position cursor after the prefix
        if let newPosition = textView.position(
            from: textView.beginningOfDocument, offset: lineStart + prefix.count) {
            textView.selectedTextRange = textView.textRange(from: newPosition, to: newPosition)
        }
    }

    // Generate a plain text snippet from HTML content
    static func generateContentSnippet(from htmlContent: String, maxLength: Int) -> String {
        // Remove HTML tags
        let stripped = htmlContent.replacingOccurrences(
            of: "<[^>]+>", with: "", options: .regularExpression)

        // Clean up common HTML entities
        let cleaned =
            stripped
            .replacingOccurrences(of: "&nbsp;", with: " ")
            .replacingOccurrences(of: "&amp;", with: "&")
            .replacingOccurrences(of: "&lt;", with: "<")
            .replacingOccurrences(of: "&gt;", with: ">")

        // Limit length
        let snippet = String(cleaned.prefix(maxLength))
        return snippet
    }

    static func applyUnderline(to textView: UITextView) {
        var range: NSRange = .init(location: 0, length: 0)
        if let selectedRange = textView.selectedTextRange,
            let selectedText = textView.text(in: selectedRange),
            !selectedText.isEmpty {
            let start = textView.offset(from: textView.beginningOfDocument, to: selectedRange.start)
            let end = textView.offset(from: textView.beginningOfDocument, to: selectedRange.end)
            range = NSRange(location: start, length: end - start)
        }
        let attributedText = NSMutableAttributedString(attributedString: textView.attributedText ?? NSAttributedString(string: textView.text))
        attributedText.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: range)
        textView.attributedText = attributedText
    }

    static func applyStrikethrough(to textView: UITextView) {
        var range: NSRange = .init(location: 0, length: 0)
        if let selectedRange = textView.selectedTextRange,
            let selectedText = textView.text(in: selectedRange),
            !selectedText.isEmpty {
            let start = textView.offset(from: textView.beginningOfDocument, to: selectedRange.start)
            let end = textView.offset(from: textView.beginningOfDocument, to: selectedRange.end)
            range = NSRange(location: start, length: end - start)
        }
        let attributedText = NSMutableAttributedString(attributedString: textView.attributedText ?? NSAttributedString(string: textView.text))
        attributedText.addAttribute(.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: range)
        textView.attributedText = attributedText
    }
}
