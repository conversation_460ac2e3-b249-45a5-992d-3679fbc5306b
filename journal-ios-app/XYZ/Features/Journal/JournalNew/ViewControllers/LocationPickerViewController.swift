import UIKit
import MapKit
import CoreLocation
import SnapKit

protocol LocationPickerViewControllerDelegate: AnyObject {
    func locationPickerDidSelectLocation(_ location: String, coordinate: CLLocationCoordinate2D?)
    func locationPickerDidCancel()
}

class LocationPickerViewController: UIViewController {

    weak var delegate: LocationPickerViewControllerDelegate?

    private let locationPickerView = LocationPickerView()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)

        locationPickerView.delegate = self
        view.addSubview(locationPickerView)

        locationPickerView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(20)
            make.centerY.equalToSuperview()
            make.height.equalTo(500)
        }

        // Add tap gesture to background
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tapGesture)
    }

    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !locationPickerView.frame.contains(location) {
            delegate?.locationPickerDidCancel()
            dismiss(animated: true)
        }
    }
}

// MARK: - LocationPickerViewDelegate
extension LocationPickerViewController: LocationPickerViewDelegate {
    func locationPickerDidSelectLocation(_ location: String, coordinate: CLLocationCoordinate2D?) {
        delegate?.locationPickerDidSelectLocation(location, coordinate: coordinate)
        dismiss(animated: true)
    }

    func locationPickerDidCancel() {
        delegate?.locationPickerDidCancel()
        dismiss(animated: true)
    }
}
