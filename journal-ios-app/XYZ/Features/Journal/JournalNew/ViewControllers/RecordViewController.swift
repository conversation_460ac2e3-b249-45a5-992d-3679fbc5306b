import AVFoundation
import SnapKit
import UIKit

protocol RecordViewControllerDelegate: AnyObject {
    func recordingFinished(url: URL, duration: TimeInterval)
}

class RecordViewController: PresentViewController, AVAudioRecorderDelegate {
    // MARK: - Properties
    weak var delegate: RecordViewControllerDelegate?

    private var audioRecorder: AVAudioRecorder?
    private var recordingURL: URL?
    private var isRecording = false
    private var recordingStartTime: Date?
    private var recordingDuration: TimeInterval = 0
    private var timer: Timer?
    private var meterUpdateTimer: Timer?

    // Audio visualization
    private var audioLevels: [CGFloat] = Array(repeating: 0.05, count: 30)

    // MARK: - UI Elements
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = Colors.cardBackground.dynamicColor()
        view.layer.cornerRadius = 20
        return view
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "录音"
        label.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        label.textColor = Colors.textPrimary.dynamicColor()
        label.textAlignment = .center
        return label
    }()

    private let timeLabel: UILabel = {
        let label = UILabel()
        label.text = "00:00"
        label.font = UIFont.monospacedDigitSystemFont(ofSize: 48, weight: .medium)
        label.textColor = Colors.themeColor.dynamicColor()
        label.textAlignment = .center
        return label
    }()

    private let visualizationView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private let recordButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "circle.fill"), for: .normal)
        button.tintColor = UIColor.systemRed
        button.backgroundColor = UIColor.systemRed.withAlphaComponent(0.1)
        button.layer.cornerRadius = 35
        return button
    }()

    private let cancelButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("取消", for: .normal)
        button.setTitleColor(Colors.secondaryText.dynamicColor(), for: .normal)
        return button
    }()

    private let doneButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("完成", for: .normal)
        button.setTitleColor(Colors.themeColor.dynamicColor(), for: .normal)
        button.isEnabled = false
        return button
    }()

    private let statusLabel: UILabel = {
        let label = UILabel()
        label.text = "点击按钮开始录音"
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = Colors.secondaryText.dynamicColor()
        label.textAlignment = .center
        return label
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupActions()
        checkPermissions()
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        if isRecording {
            stopRecording()
        }
    }

    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)

        // Add container view
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.85)
            make.height.equalTo(containerView.snp.width).multipliedBy(1.2)
        }

        // Add title label
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.centerX.equalToSuperview()
        }

        // Add time label
        containerView.addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
        }

        // Add visualization view
        containerView.addSubview(visualizationView)
        visualizationView.snp.makeConstraints { make in
            make.top.equalTo(timeLabel.snp.bottom).offset(20)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(100)
        }

        // Add record button
        containerView.addSubview(recordButton)
        recordButton.snp.makeConstraints { make in
            make.top.equalTo(visualizationView.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(70)
        }

        // Add status label
        containerView.addSubview(statusLabel)
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(recordButton.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
        }

        // Add cancel and done buttons
        containerView.addSubview(cancelButton)
        cancelButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.bottom.equalToSuperview().offset(-20)
        }

        containerView.addSubview(doneButton)
        doneButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.bottom.equalToSuperview().offset(-20)
        }

        // Initial visualization bars
        drawVisualization()
    }

    private func setupActions() {
        recordButton.addTarget(self, action: #selector(recordButtonTapped), for: .touchUpInside)
        cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        doneButton.addTarget(self, action: #selector(doneButtonTapped), for: .touchUpInside)
    }

    // MARK: - Permissions
    private func checkPermissions() {
        AVAudioSession.sharedInstance().requestRecordPermission { [weak self] allowed in
            DispatchQueue.main.async {
                if !allowed {
                    self?.statusLabel.text = "请在设置中允许应用访问麦克风"
                    self?.recordButton.isEnabled = false
                }
            }
        }
    }

    // MARK: - Actions
    @objc private func recordButtonTapped() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }

    @objc private func cancelButtonTapped() {
        if isRecording {
            stopRecording()
        }
        dismiss(animated: true)
    }

    @objc private func doneButtonTapped() {
        if isRecording {
            stopRecording()
        }

        if let url = recordingURL, FileManager.default.fileExists(atPath: url.path) {
            delegate?.recordingFinished(url: url, duration: recordingDuration)
        }

        dismiss(animated: true)
    }

    // MARK: - Recording
    private func startRecording() {
        do {
            // Configure audio session
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default)
            try audioSession.setActive(true)

            // Create recording file URL using shared container
            do {
                let fileURL = try SharedFileManager.shared.audioRecordingURL()
                self.recordingURL = fileURL
            } catch {
                XYLog("Failed to create audio recording URL: \(error)")
                return
            }

            guard let fileURL = self.recordingURL else { return }

            // Setup recorder
            let settings: [String: Any] = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100.0,
                AVNumberOfChannelsKey: 2,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
            ]

            audioRecorder = try AVAudioRecorder(url: fileURL, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.isMeteringEnabled = true
            audioRecorder?.record()

            isRecording = true
            recordingStartTime = Date()

            // Update UI
            recordButton.setImage(UIImage(systemName: "stop.fill"), for: .normal)
            recordButton.tintColor = UIColor.systemRed
            statusLabel.text = "正在录音..."

            // Start timers
            timer = Timer.scheduledTimer(
                timeInterval: 0.1, target: self, selector: #selector(updateTime), userInfo: nil,
                repeats: true)
            meterUpdateTimer = Timer.scheduledTimer(
                timeInterval: 0.1, target: self, selector: #selector(updateMeters), userInfo: nil,
                repeats: true)

        } catch {
            statusLabel.text = "录音失败: \(error.localizedDescription)"
        }
    }

    private func stopRecording() {
        audioRecorder?.stop()
        isRecording = false

        // Calculate duration
        if let startTime = recordingStartTime {
            recordingDuration = Date().timeIntervalSince(startTime)
        }

        // Clean up
        let audioSession = AVAudioSession.sharedInstance()
        try? audioSession.setActive(false)

        // Update UI
        recordButton.setImage(UIImage(systemName: "circle.fill"), for: .normal)
        statusLabel.text = "录音已完成"
        doneButton.isEnabled = true

        // Stop timers
        timer?.invalidate()
        meterUpdateTimer?.invalidate()
    }

    // MARK: - Timer Updates
    @objc private func updateTime() {
        guard let startTime = recordingStartTime else { return }

        let elapsed = Date().timeIntervalSince(startTime)
        let minutes = Int(elapsed) / 60
        let seconds = Int(elapsed) % 60

        timeLabel.text = String(format: "%02d:%02d", minutes, seconds)
    }

    @objc private func updateMeters() {
        guard let recorder = audioRecorder, isRecording else { return }

        recorder.updateMeters()

        // Get the average power from the recorder
        let averagePower = recorder.averagePower(forChannel: 0)

        // Convert dB to a value between 0 and 1 (dB is negative, with 0 being loudest)
        // Typical values range from -60 (quiet) to 0 (loud)
        let level = max(0.05, min(1.0, (60.0 + CGFloat(averagePower)) / 60.0))

        // Shift values in the array
        audioLevels.removeFirst()
        audioLevels.append(level)

        // Redraw visualization
        drawVisualization()
    }

    // MARK: - Visualization
    private func drawVisualization() {
        // Remove existing visualization
        visualizationView.subviews.forEach { $0.removeFromSuperview() }

        // GUARD: Ensure audioLevels is not empty. If it is, we've already cleared subviews.
        guard !audioLevels.isEmpty else { return }

        let numbers = CGFloat(audioLevels.count)
        let wTotal = visualizationView.bounds.width
        let ration: CGFloat = 1.0 / 4.0  // Ratio of spacing to barWidth

        let barWidth: CGFloat
        let spacing: CGFloat

        if numbers == 1 {
            barWidth = wTotal
            spacing = 0  // A single bar takes the full width, no spacing needed.
        } else {
            // For N bars and N-1 spaces:
            // Total width = N * barWidth + (N - 1) * spacing
            // spacing = R * barWidth
            // Total width = N * barWidth + (N - 1) * R * barWidth
            // Total width = barWidth * (N + (N - 1) * R)
            // So, barWidth = Total width / (N + (N - 1) * R)
            let denominator = numbers + (numbers - 1) * ration
            if denominator <= 0 {  // Should not happen for N > 1 and R >= 0
                // Fallback: Distribute width equally among bars, no spacing
                barWidth = wTotal / numbers
                spacing = 0
            } else {
                barWidth = wTotal / denominator
                spacing = barWidth * ration
            }
        }

        for (index, level) in audioLevels.enumerated() {
            let bar = UIView()
            bar.backgroundColor = Colors.themeColor.dynamicColor()
            bar.layer.cornerRadius = 2

            visualizationView.addSubview(bar)

            // Calculate height based on level (0.0-1.0)
            let height = visualizationView.bounds.height * level

            bar.frame = CGRect(
                x: CGFloat(index) * (barWidth + spacing),
                y: visualizationView.bounds.height - height,
                width: barWidth,
                height: height
            )
        }
    }

    // MARK: - AVAudioRecorderDelegate
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if !flag {
            statusLabel.text = "录音失败"
        }
    }

    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        if let error = error {
            statusLabel.text = "录音错误: \(error.localizedDescription)"
        } else {
            statusLabel.text = "录音错误"
        }
    }
}
