import UIKit
import SnapKit

protocol TagsPickerViewControllerDelegate: AnyObject {
    func tagsPickerDidUpdateTags(_ tags: [String])
    func tagsPickerDidCancel()
}

class TagsPickerViewController: UIViewController {

    weak var delegate: TagsPickerViewControllerDelegate?

    private let tagsPickerView = TagsPickerView()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    func setTags(_ tags: [String]) {
        tagsPickerView.setTags(tags)
    }

    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)

        tagsPickerView.delegate = self
        view.addSubview(tagsPickerView)

        tagsPickerView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(20)
            make.centerY.equalToSuperview()
            make.height.equalTo(400)
        }

        // Add tap gesture to background
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tapGesture)
    }

    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !tagsPickerView.frame.contains(location) {
            delegate?.tagsPickerDidCancel()
            dismiss(animated: true)
        }
    }
}

// MARK: - TagsPickerViewDelegate
extension TagsPickerViewController: TagsPickerViewDelegate {
    func tagsPickerDidUpdateTags(_ tags: [String]) {
        delegate?.tagsPickerDidUpdateTags(tags)
        dismiss(animated: true)
    }

    func tagsPickerDidCancel() {
        delegate?.tagsPickerDidCancel()
        dismiss(animated: true)
    }
}
