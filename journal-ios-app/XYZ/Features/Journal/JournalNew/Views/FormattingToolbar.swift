import UIKit

// A custom toolbar for text formatting that can be used both as an input accessory view
// and as a fixed toolbar at the bottom of the screen
class FormattingToolbar: UIView {

    // MARK: - Properties
    weak var textView: UITextView?
    weak var viewController: UIViewController?

    // View that contains the popup content
    private var popupContainerView: UIView?

    // Callback for when a formatting option is selected
    var onFormatSelected: ((FormattingOption) -> Void)?

    var onOptionsView: ((UIView, Bool) -> Void)?

    // Formatting options
    enum FormattingOption {
        case alignment(NSTextAlignment)
        case bold
        case italic
        case thin
        case regular
        case underline
        case strikethrough
        case fontSize(CGFloat)
        case lineHeight(CGFloat)
        case textColor(String)
        case emoji(String)
        case list(isNumbered: Bool)
        case link
        case image
        case video
        case gallery
        case audio
        case location
        case tags
        case media
    }

    // UI Elements
    private let contentScrollView = UIScrollView()
    private let stackView = UIStackView()

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        // 确保工具栏可以作为输入附件视图使用
        autoresizingMask = [.flexibleWidth]
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        // 确保工具栏可以作为输入附件视图使用
        autoresizingMask = [.flexibleWidth]
    }

    // MARK: - Layout
    override var intrinsicContentSize: CGSize {
        // Return a flexible height that works well with iOS keyboard system
        return CGSize(width: UIView.noIntrinsicMetric, height: 76)
    }

    // MARK: - UI Setup
    private func setupUI() {
        backgroundColor = .clear

        // Add scroll view for horizontal scrolling of tools
        contentScrollView.contentInset = .init(top: 0, left: 15, bottom: 0, right: 15)
        contentScrollView.backgroundColor = Theme.Colors.tabBarBackground.dynamicColor()

        addSubview(contentScrollView)
        contentScrollView.showsHorizontalScrollIndicator = false
        contentScrollView.snp.makeConstraints { make in
            make.height.equalTo(46)
            make.leading.equalTo(self).offset(15)
            make.trailing.equalTo(self).offset(-15)
            make.centerY.equalToSuperview()
            make.top.greaterThanOrEqualToSuperview().offset(15)
        }
        contentScrollView.layer.cornerRadius = 23
        contentScrollView.layer.masksToBounds = true

        // Add stack view for tools
        contentScrollView.addSubview(stackView)
        stackView.axis = .horizontal
        stackView.spacing = 16
        stackView.distribution = .equalSpacing
        stackView.alignment = .center
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(
                UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16))
            make.height.equalToSuperview()
        }

        // Add formatting buttons
        addFormattingButtons()
    }

    private func addFormattingButtons() {
        // Replace individual text formatting buttons with a single text options button
        addToolbarButton(icon: "text.badge.plus", action: #selector(showTextFormattingOptions))

        // Media (combined photo, video, camera)
        addToolbarButton(icon: "photo.on.rectangle.angled", action: #selector(mediaTapped))

        // Mic
        addToolbarButton(icon: "mic", action: #selector(audioTapped))

        // Location
        addToolbarButton(icon: "location", action: #selector(locationTapped))

        // Tags
        addToolbarButton(icon: "tag", action: #selector(tagsTapped))
    }

    private func addToolbarButton(icon: String, action: Selector) {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: icon), for: .normal)
        button.tintColor = Theme.Colors.secondaryText.dynamicColor()
        button.addTarget(self, action: action, for: .touchUpInside)

        // Add hover effect
        button.addTarget(self, action: #selector(buttonTouchDown(_:)), for: .touchDown)
        button.addTarget(
            self, action: #selector(buttonTouchUp(_:)),
            for: [.touchUpInside, .touchUpOutside, .touchCancel])

        // Set size
        button.snp.makeConstraints { make in
            make.width.height.equalTo(44)
        }

        stackView.addArrangedSubview(button)
    }

    // MARK: - Button Actions
    @objc private func buttonTouchDown(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1) {
            sender.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
            sender.tintColor = Theme.Colors.themeColor.dynamicColor()
        }
    }

    @objc private func buttonTouchUp(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1) {
            sender.transform = .identity
            sender.tintColor = Theme.Colors.secondaryText.dynamicColor()
        }
    }

    // swiftlint:disable:next function_body_length
    @objc private func showTextFormattingOptions() {
        // Create and store the popup view
        popupContainerView = createPopupView(title: "字体")
        guard let containerView = popupContainerView else { return }

        // Get the stack view from the container
        guard
            let stackView = containerView.subviews.first(where: { $0 is UIStackView })
                as? UIStackView
        else {
            popupContainerView = nil
            return
        }

        // Configure the stack view for text formatting options
        stackView.axis = .vertical
        stackView.spacing = 16

        // Create section for alignment options
        let alignmentSection = createSectionView(title: "对齐方式")
        let alignmentStack = createHorizontalStackView()

        // Add alignment options
        let alignments: [(icon: String, alignment: NSTextAlignment)] = [
            ("text.alignleft", .left),
            ("text.aligncenter", .center),
            ("text.alignright", .right)
        ]

        for (icon, alignment) in alignments {
            let button = createPopupButton(icon: icon) { [weak self] in
                self?.onFormatSelected?(.alignment(alignment))
            }
            alignmentStack.addArrangedSubview(button)
        }

        alignmentSection.addSubview(alignmentStack)
        alignmentStack.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(alignmentSection.subviews.first!.snp.bottom).offset(8)
        }

        stackView.addArrangedSubview(alignmentSection)

        // Create section for color and size
        let sizeAndWeightStack = createHorizontalStackView()
        sizeAndWeightStack.distribution = .fillEqually
        sizeAndWeightStack.spacing = 20
        stackView.addArrangedSubview(sizeAndWeightStack)

        // Create section for style options
        let styleSection = createSectionView(title: "样式")
        let styleStack = createHorizontalStackView()

        // Add style options (bold, italic)
        let boldButton = createPopupButton(icon: "bold", title: "粗体") { [weak self] in
            self?.onFormatSelected?(.bold)
        }
        styleStack.addArrangedSubview(boldButton)

        let italicButton = createPopupButton(icon: "italic", title: "斜体") { [weak self] in
            self?.onFormatSelected?(.italic)
        }
        styleStack.addArrangedSubview(italicButton)

        sizeAndWeightStack.addArrangedSubview(styleSection)
        styleSection.addSubview(styleStack)
        styleStack.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(styleSection.subviews.first!.snp.bottom).offset(8)
        }

        let sizeSection = createSectionView(title: "字号")
        let sizeStack = createHorizontalStackView()
        let sizes: [(key: String, value: Int)] = [
            ("H1", 24),
            ("H2", 20),
            ("H3", 18),
            ("H4", 16)
        ]
        for size in sizes {
            let button = createPopupButton(icon: "", title: "\(size.key)") { [weak self] in
                self?.onFormatSelected?(.fontSize(CGFloat(size.value)))
            }
            sizeStack.addArrangedSubview(button)
        }

        sizeAndWeightStack.addArrangedSubview(sizeSection)
        sizeSection.addSubview(sizeStack)
        sizeStack.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(sizeSection.subviews.first!.snp.bottom).offset(8)
        }

        // Create section for line height
        let lineHeightSection = createSectionView(title: "行距")
        let lineHeightStack = createHorizontalStackView()
        let lineHeights: [(key: String, value: CGFloat)] = [
            ("紧密", 1.0),
            ("标准", 1.2),
            ("宽松", 1.5),
            ("很宽", 2.0)
        ]
        for lineHeight in lineHeights {
            let button = createPopupButton(icon: "", title: "\(lineHeight.key)") { [weak self] in
                self?.onFormatSelected?(.lineHeight(lineHeight.value))
            }
            lineHeightStack.addArrangedSubview(button)
        }

        lineHeightSection.addSubview(lineHeightStack)
        lineHeightStack.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(lineHeightSection.subviews.first!.snp.bottom).offset(8)
        }

        stackView.addArrangedSubview(lineHeightSection)

        // Create section for color and size
        let formatSection = createSectionView(title: "颜色")
        let formatStack = createHorizontalStackView()
        formatStack.spacing = 10
        formatStack.distribution = .equalSpacing

        let scrollView = UIScrollView()
        formatSection.addSubview(scrollView)

        scrollView.contentInset = .zero
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.addSubview(formatStack)
        scrollView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(formatSection.subviews.first!.snp.bottom).offset(8)
            make.bottom.equalToSuperview()
        }

        formatStack.snp.makeConstraints { make in
            make.leading.trailing.top.bottom.equalToSuperview()
        }

        let colors: [String] = [
            "#F2F3F5", "#191919",
            "#FF6B6B", "#E71D36", "#FF9671",   // Reds & Corals
            "#FF9F1C", "#FFC75F",              // Oranges
            "#F9F871", "#FFD93D",              // Yellows
            "#6BCB77", "#00C9A7", "#2EC4B6",   // Greens & Teals
            "#4D96FF", "#0081CF",              // Blues
            "#845EC2", "#6A4C93", "#B388EB",   // Purples
            "#D65DB1", "#FFCAD4",              // Pinks
            "#A0E7E5", "#C34A36", "#4B4453"    // Misc / Neutrals
        ]

        for color in colors {
            let button = createRoundPopupButton(icon: "", title: "") { [weak self] in
                self?.onFormatSelected?(.textColor(color))
            }

            // Ensure the color is properly set
            if let uiColor = UIColor(hexString: color) {
                button.backgroundColor = uiColor
                button.accessibilityLabel = color  // Store the hex color for reference

                // Add border for better visibility
                button.layer.borderWidth = 1
                button.layer.borderColor = UIColor.gray.withAlphaComponent(0.3).cgColor
            } else {
                button.backgroundColor = UIColor.gray
            }

            button.layer.masksToBounds = true
            formatStack.addArrangedSubview(button)
        }

        stackView.addArrangedSubview(formatSection)

        self.onOptionsView?(containerView, false)
    }

    @objc private func showEmojiPicker() {

        let containerView = createPopupView()

        // 获取容器中的堆栈视图
        guard
            let stackView = containerView.subviews.first(where: { $0 is UIStackView })
                as? UIStackView
        else {
            return
        }

        // 常用表情符号
        let commonEmojis = ["😀", "😃", "😊", "😎", "🥰", "😴", "🤔", "👍"]

        for emoji in commonEmojis {
            let button = UIButton(type: .system)
            button.setTitle(emoji, for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 24)

            // 添加视觉反馈
            button.backgroundColor = UIColor.clear
            button.layer.cornerRadius = 12

            // 添加触摸效果
            button.addTarget(self, action: #selector(popupButtonTouchDown(_:)), for: .touchDown)
            button.addTarget(
                self, action: #selector(popupButtonTouchUp(_:)),
                for: [.touchUpInside, .touchUpOutside, .touchCancel])

            button.addTarget(self, action: #selector(emojiSelected(_:)), for: .touchUpInside)

            // 存储表情符号和容器视图
            objc_setAssociatedObject(
                button, &AssociatedKeys.emojiKey, emoji, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            objc_setAssociatedObject(
                button, &AssociatedKeys.containerKey, containerView,
                .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

            button.snp.makeConstraints { make in
                make.width.height.equalTo(50)
            }

            stackView.addArrangedSubview(button)
        }

        self.onOptionsView?(containerView, false)
    }

    @objc private func emojiSelected(_ sender: UIButton) {
        if let emoji = objc_getAssociatedObject(sender, &AssociatedKeys.emojiKey) as? String {
            onFormatSelected?(.emoji(emoji))
        }
    }

    @objc private func showListOptions() {

        let containerView = createPopupView()

        // 获取容器中的堆栈视图
        guard
            let stackView = containerView.subviews.first(where: { $0 is UIStackView })
                as? UIStackView
        else {
            return
        }

        // 项目符号列表按钮
        let bulletButton = createPopupButton(icon: "list.bullet", title: "项目符号") {
            [weak self] in
            self?.onFormatSelected?(.list(isNumbered: false))
        }
        stackView.addArrangedSubview(bulletButton)

        // 编号列表按钮
        let numberedButton = createPopupButton(icon: "list.number", title: "编号列表") {
            [weak self] in
            self?.onFormatSelected?(.list(isNumbered: true))
        }
        stackView.addArrangedSubview(numberedButton)
        self.onOptionsView?(containerView, false)
    }

    @objc private func linkTapped() {
        onFormatSelected?(.link)
    }

    @objc private func imageTapped() {
        onFormatSelected?(.gallery)
    }

    @objc private func videoTapped() {
        onFormatSelected?(.video)
    }

    @objc private func cameraTapped() {
        onFormatSelected?(.image)
    }

    @objc private func audioTapped() {
        onFormatSelected?(.audio)
    }

    @objc private func locationTapped() {
        XYLog("Location button tapped")
        onFormatSelected?(.location)
    }

    @objc private func tagsTapped() {
        XYLog("Tags button tapped")
        onFormatSelected?(.tags)
    }

    @objc private func mediaTapped() {
        XYLog("Media button tapped")
        onFormatSelected?(.media)
    }

    @objc private func fontSizeSelected(_ sender: UIButton) {
        if let size = objc_getAssociatedObject(sender, &AssociatedKeys.fontSizeKey) as? CGFloat {
            onFormatSelected?(.fontSize(size))
        }
    }

    // MARK: - Popup Helpers
    // swiftlint:disable:next function_body_length
    private func createPopupView(title: String = "") -> UIView {
        // 创建容器视图
        let containerView = UIView()
        containerView.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        containerView.layer.cornerRadius = 16
        containerView.layer.masksToBounds = true
        containerView.clipsToBounds = true

        // 添加模糊效果
        if !UIAccessibility.isReduceTransparencyEnabled {
            let blurEffect = UIBlurEffect(style: .systemMaterial)
            let blurView = UIVisualEffectView(effect: blurEffect)
            blurView.layer.cornerRadius = 16
            blurView.clipsToBounds = true
            containerView.addSubview(blurView)
            blurView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }

        let headerView = UIView()
        headerView.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        containerView.addSubview(headerView)
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }

        let closeBackView = UIView()
        headerView.addSubview(closeBackView)
        closeBackView.snp.makeConstraints { make in
            make.trailing.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalTo(closeBackView.snp.height)
        }

        let closeButton = UIButton(type: .system)
        closeButton.setImage(UIImage(named: "check-solid"), for: .normal)
        closeButton.tintColor = Theme.Colors.secondaryText.dynamicColor()
        closeButton.addTarget(self, action: #selector(didClickCloseButton(_:)), for: .touchUpInside)
        closeBackView.addSubview(closeButton)
        closeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.width.height.equalTo(22)
            make.top.equalToSuperview().offset(12)
        }

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = Theme.Colors.text.dynamicColor()
        headerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        let lineView = UIView()
        lineView.backgroundColor = Theme.Colors.separator.dynamicColor()
        headerView.addSubview(lineView)
        lineView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(0.5)
            make.bottom.equalToSuperview()
        }

        // 创建内容堆栈视图
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 20
        stackView.distribution = .fillEqually
        stackView.isLayoutMarginsRelativeArrangement = true
        stackView.layoutMargins = UIEdgeInsets(top: 16, left: 20, bottom: 16, right: 20)

        // 添加内容视图
        containerView.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.leading.trailing.bottom.equalToSuperview()
        }

        // 设置边框
        containerView.layer.borderWidth = 0.5
        containerView.layer.borderColor = UIColor.lightGray.withAlphaComponent(0.3).cgColor

        // 添加阴影
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 4)
        containerView.layer.shadowOpacity = 0.15
        containerView.layer.shadowRadius = 12
        containerView.layer.masksToBounds = false

        return containerView
    }

    private func createPopupButton(icon: String, title: String? = nil, action: @escaping () -> Void)
    -> UIButton {
        // 创建更美观的按钮
        let button = UIButton(type: .system)

        // 设置图标
        if let image = UIImage(systemName: icon) {
            let config = UIImage.SymbolConfiguration(pointSize: 18, weight: .medium)
            let scaledImage = image.withConfiguration(config)
            button.setImage(scaledImage, for: .normal)
        }

        // 设置标题
        if let title = title {
            button.setTitle(" " + title, for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        }

        // 设置颜色
        button.tintColor = Theme.Colors.text.dynamicColor()

        // 添加视觉反馈
        button.backgroundColor = UIColor.clear
        button.layer.cornerRadius = 12

        // 添加触摸效果
        button.addTarget(self, action: #selector(popupButtonTouchDown(_:)), for: .touchDown)
        button.addTarget(
            self, action: #selector(popupButtonTouchUp(_:)),
            for: [.touchUpInside, .touchUpOutside, .touchCancel])

        // 存储动作
        let actionWrapper = ActionWrapper(action: action)
        objc_setAssociatedObject(
            button, &AssociatedKeys.actionKey, actionWrapper, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        button.addTarget(self, action: #selector(popupButtonTapped(_:)), for: .touchUpInside)

        // 设置尺寸
        button.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
        return button
    }

    private func createRoundPopupButton(icon: String, title: String? = nil, action: @escaping () -> Void)
    -> UIButton {
        // 创建更美观的按钮
        let button = UIButton(type: .system)

        // 设置图标
        if let image = UIImage(systemName: icon) {
            let config = UIImage.SymbolConfiguration(pointSize: 18, weight: .medium)
            let scaledImage = image.withConfiguration(config)
            button.setImage(scaledImage, for: .normal)
        }

        // 设置标题
        if let title = title {
            button.setTitle(" " + title, for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        }

        // 设置颜色
        button.tintColor = Theme.Colors.text.dynamicColor()

        // 添加视觉反馈
        button.backgroundColor = UIColor.clear
        button.layer.cornerRadius = 15
        button.layer.masksToBounds = true
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.gray.withAlphaComponent(0.3).cgColor

        // 添加触摸效果
        button.addTarget(self, action: #selector(popupButtonTouchDown(_:)), for: .touchDown)
        button.addTarget(
            self, action: #selector(popupButtonTouchUp(_:)),
            for: [.touchUpInside, .touchUpOutside, .touchCancel])

        // 存储动作
        let actionWrapper = ActionWrapper(action: action)
        objc_setAssociatedObject(
            button, &AssociatedKeys.actionKey, actionWrapper, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        button.addTarget(self, action: #selector(popupButtonTapped(_:)), for: .touchUpInside)

        // 设置尺寸
        button.snp.makeConstraints { make in
            make.width.height.equalTo(30)
        }
        return button
    }

    @objc private func popupButtonTouchDown(_ sender: UIButton) {
        UIView.animate(withDuration: 0.15) {
            sender.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }
    }

    @objc private func popupButtonTouchUp(_ sender: UIButton) {
        UIView.animate(withDuration: 0.15) {
            sender.transform = .identity
        }
    }

    @objc private func popupButtonTapped(_ sender: UIButton) {
        if let actionWrapper = objc_getAssociatedObject(sender, &AssociatedKeys.actionKey)
            as? ActionWrapper {
            actionWrapper.action()
        }
    }

    private func presentPopup(_ popupView: UIView) {
        guard let viewController = viewController, let view = viewController.view else { return }

        view.addSubview(popupView)
    }

    @objc private func dismissPopup(_ sender: UITapGestureRecognizer) {
        // 添加消失动画
        if let containerView = sender.view {
            // 找到弹出视图
            let popupView = containerView.subviews.first

            // 执行消失动画
            UIView.animate(
                withDuration: 0.2,
                animations: {
                    popupView?.transform = CGAffineTransform(scaleX: 0.9, y: 0.9).translatedBy(
                        x: 0, y: 10)
                    popupView?.alpha = 0
                    containerView.backgroundColor = UIColor.clear
                },
                completion: { _ in
                    containerView.removeFromSuperview()
                })
        }
    }

    // Create a section view with title
    private func createSectionView(title: String) -> UIView {
        let sectionView = UIView()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = Theme.Colors.secondaryText.dynamicColor()

        sectionView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
        }

        return sectionView
    }

    // Create a horizontal stack view for buttons
    private func createHorizontalStackView() -> UIStackView {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .equalSpacing
        return stackView
    }

    // Call this to update toolbar items based on text selection
    func updateForTextSelection(isTextSelected: Bool) {
        // Remove all arranged subviews
        for view in stackView.arrangedSubviews {
            stackView.removeArrangedSubview(view)
            view.removeFromSuperview()
        }

        if isTextSelected {

            // add underline and strikethrough
            addToolbarButton(icon: "underline", action: #selector(underlineTapped))
            addToolbarButton(icon: "strikethrough", action: #selector(strikethroughTapped))

            // Add color palette button
            addToolbarButton(icon: "paintpalette", action: #selector(showTextFormattingOptions))

            // Add most common colors directly in the toolbar
            let commonColors = [
                "#F2F3F5", "#191919",
                "#FF6B6B", "#E71D36", "#FF9671",   // Reds & Corals
                "#FF9F1C", "#FFC75F",              // Oranges
                "#F9F871", "#FFD93D",              // Yellows
                "#6BCB77", "#00C9A7", "#2EC4B6",   // Greens & Teals
                "#4D96FF", "#0081CF",              // Blues
                "#845EC2", "#6A4C93", "#B388EB",   // Purples
                "#D65DB1", "#FFCAD4",              // Pinks
                "#A0E7E5", "#C34A36", "#4B4453"    // Misc / Neutrals
            ]

            for color in commonColors {
                let colorButton = UIButton(type: .system)

                // Create a more reliable color button
                if let uiColor = UIColor(hexString: color) {
                    colorButton.backgroundColor = uiColor
                    colorButton.layer.cornerRadius = 12
                    colorButton.layer.masksToBounds = true
                    colorButton.layer.borderWidth = 1
                    colorButton.layer.borderColor = UIColor.gray.withAlphaComponent(0.3).cgColor

                    // Store the color string as a tag on the button
                    colorButton.accessibilityLabel = color

                    // Add button to stackView
                    colorButton.snp.makeConstraints { make in
                        make.width.height.equalTo(24)
                    }

                    colorButton.addTarget(self, action: #selector(colorButtonTapped(_:)), for: .touchUpInside)
                    stackView.addArrangedSubview(colorButton)
                }
            }
        } else {
            // Default toolbar
            addFormattingButtons()
        }
    }

    @objc private func boldTapped() {
        onFormatSelected?(.bold)
    }

    @objc private func italicTapped() {
        onFormatSelected?(.italic)
    }

    @objc private func thinTapped() {
        onFormatSelected?(.thin)
    }

    @objc private func h1Tapped() {
        onFormatSelected?(.fontSize(20))
    }

    @objc private func h2Tapped() {
        onFormatSelected?(.fontSize(18))
    }

    @objc private func h3Tapped() {
        onFormatSelected?(.fontSize(16))
    }

    @objc private func colorButtonTapped(_ sender: UIButton) {
        if let color = sender.accessibilityLabel {
            XYLog("Color button tapped with color: \(color)")
            onFormatSelected?(.textColor(color))

            // If this is a color button in the popup, dismiss the popup
            if let containerView = sender.superview?.superview?.superview {
                if containerView != self {
                    dismissPopupView(containerView)
                }
            }
        }
    }

    @objc private func alignLeftTapped() {
        onFormatSelected?(.alignment(.left))
        // Dismiss popup if this was called from a popup button
        if let containerView = popupContainerView {
            dismissPopupView(containerView)
        }
    }

    @objc private func alignCenterTapped() {
        onFormatSelected?(.alignment(.center))
        // Dismiss popup if this was called from a popup button
        if let containerView = popupContainerView {
            dismissPopupView(containerView)
        }
    }

    @objc private func alignRightTapped() {
        onFormatSelected?(.alignment(.right))
        // Dismiss popup if this was called from a popup button
        if let containerView = popupContainerView {
            dismissPopupView(containerView)
        }
    }

    @objc private func alignJustifiedTapped() {
        onFormatSelected?(.alignment(.justified))
        // Dismiss popup if this was called from a popup button
        if let containerView = popupContainerView {
            dismissPopupView(containerView)
        }
    }

    @objc private func underlineTapped() {
        onFormatSelected?(.underline)
    }

    @objc private func strikethroughTapped() {
        onFormatSelected?(.strikethrough)
    }
}

// MARK: - Helper Classes and Extensions
private class ActionWrapper {
    let action: () -> Void

    init(action: @escaping () -> Void) {
        self.action = action
    }
}

private struct AssociatedKeys {
    static var actionKey = "actionKey"
    static var emojiKey = "emojiKey"
    static var fontSizeKey = "fontSizeKey"
    static var containerKey = "containerKey"
}

// 辅助方法：关闭弹出视图
extension FormattingToolbar {
    fileprivate func dismissPopupView(_ containerView: UIView) {
        self.onOptionsView?(containerView, true)
        // Clear the stored reference
        if containerView === popupContainerView {
            popupContainerView = nil
        }
    }

    @objc func didClickCloseButton(_ sender: UIButton) {
        if let containerView = sender.superview?.superview {
            dismissPopupView(containerView)
        }
    }
}
