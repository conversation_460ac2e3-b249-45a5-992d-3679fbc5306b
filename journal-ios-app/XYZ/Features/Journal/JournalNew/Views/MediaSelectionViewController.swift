import UIKit
import SnapKit

protocol MediaSelectionViewControllerDelegate: AnyObject {
    func mediaSelectionDidSelectPhotoLibrary()
    func mediaSelectionDidSelectCamera()
    func mediaSelectionDidSelectVideo()
    func mediaSelectionDidCancel()
}

class MediaSelectionViewController: UIViewController {

    weak var delegate: MediaSelectionViewControllerDelegate?

    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let photoLibraryButton = UIButton(type: .system)
    private let cameraButton = UIButton(type: .system)
    private let videoButton = UIButton(type: .system)
    private let cancelButton = UIButton(type: .system)

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        animateIn()
    }

    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)

        // Container view
        containerView.backgroundColor = Colors.tabBarBackground.dynamicColor()
        containerView.layer.cornerRadius = 16
        containerView.layer.masksToBounds = true

        // Title
        titleLabel.text = "选择媒体类型"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = Colors.text.dynamicColor()
        titleLabel.textAlignment = .center

        // Photo Library Button
        setupButton(photoLibraryButton, title: "相册", icon: "photo.on.rectangle", action: #selector(photoLibraryTapped))

        // Camera Button
        setupButton(cameraButton, title: "拍照", icon: "camera", action: #selector(cameraTapped))

        // Video Button
        setupButton(videoButton, title: "录制视频", icon: "video", action: #selector(videoTapped))

        // Cancel Button
        cancelButton.setTitle("取消", for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        cancelButton.setTitleColor(Colors.secondaryText.dynamicColor(), for: .normal)
        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)

        // Add tap gesture to background
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tapGesture)
    }

    private func setupButton(_ button: UIButton, title: String, icon: String, action: Selector) {
        button.setTitle(title, for: .normal)
        button.setImage(UIImage(systemName: icon), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.setTitleColor(Colors.text.dynamicColor(), for: .normal)
        button.tintColor = Colors.theme.dynamicColor()
        button.backgroundColor = Colors.pageBg.dynamicColor()

        // Image and title layout
        var configuration = UIButton.Configuration.plain()
        configuration.imagePadding = 8
        configuration.imagePlacement = .leading
        configuration.titleAlignment = .center
        button.configuration = configuration

        button.addTarget(self, action: action, for: .touchUpInside)
    }

    private func setupConstraints() {
        view.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(photoLibraryButton)
        containerView.addSubview(cameraButton)
        containerView.addSubview(videoButton)
        containerView.addSubview(cancelButton)

        containerView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(400) // Start off-screen
            make.leading.trailing.equalToSuperview().inset(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        photoLibraryButton.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        cameraButton.snp.makeConstraints { make in
            make.top.equalTo(photoLibraryButton.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        videoButton.snp.makeConstraints { make in
            make.top.equalTo(cameraButton.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        cancelButton.snp.makeConstraints { make in
            make.top.equalTo(videoButton.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
    }

    private func animateIn() {
        containerView.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(-20)
        }

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.view.layoutIfNeeded()
        }
    }

    private func animateOut(completion: @escaping () -> Void) {
        containerView.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(300)
        }

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn, animations: {
            self.view.layoutIfNeeded()
            self.view.backgroundColor = UIColor.clear
        }) { _ in
            completion()
        }
    }

    @objc private func photoLibraryTapped() {
        animateOut { [weak self] in
            self?.dismiss(animated: false)
            self?.delegate?.mediaSelectionDidSelectPhotoLibrary()
        }
    }

    @objc private func cameraTapped() {
        animateOut { [weak self] in
            self?.dismiss(animated: false)
            self?.delegate?.mediaSelectionDidSelectCamera()
        }
    }

    @objc private func videoTapped() {
        animateOut { [weak self] in
            self?.dismiss(animated: false)
            self?.delegate?.mediaSelectionDidSelectVideo()
        }
    }

    @objc private func cancelTapped() {
        animateOut { [weak self] in
            self?.delegate?.mediaSelectionDidCancel()
            self?.dismiss(animated: false)
        }
    }

    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !containerView.frame.contains(location) {
            cancelTapped()
        }
    }
}
