import UIKit
import MapKit
import CoreLocation

protocol LocationPickerViewDelegate: AnyObject {
    func locationPickerDidSelectLocation(_ location: String, coordinate: CLLocationCoordinate2D?)
    func locationPickerDidCancel()
}

class LocationPickerView: BaseView {

    weak var delegate: LocationPickerViewDelegate?

    private let mapView = MKMapView()
    private let searchBar = UISearchBar()
    private let confirmButton = UIButton(type: .system)
    private let cancelButton = UIButton(type: .system)
    private let currentLocationButton = UIButton(type: .system)

    private let locationManager = CLLocationManager()
    private var selectedCoordinate: CLLocationCoordinate2D?
    private var selectedLocationName: String = ""

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupLocationManager()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupLocationManager()
    }

    private func setupUI() {
        backgroundColor = Theme.Colors.tabBarBackground.dynamicColor()
        layer.cornerRadius = 16
        layer.masksToBounds = true

        // Height will be set by the parent view controller

        // Header with title and buttons
        let headerView = UIView()
        headerView.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        addSubview(headerView)

        let titleLabel = UILabel()
        titleLabel.text = "选择位置"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = Theme.Colors.text.dynamicColor()
        titleLabel.textAlignment = .center

        cancelButton.setTitle("取消", for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        cancelButton.setTitleColor(Theme.Colors.secondaryText.dynamicColor(), for: .normal)
        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)

        confirmButton.setTitle("确定", for: .normal)
        confirmButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        confirmButton.setTitleColor(Theme.Colors.theme.dynamicColor(), for: .normal)
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)

        headerView.addSubview(cancelButton)
        headerView.addSubview(titleLabel)
        headerView.addSubview(confirmButton)

        // Search bar
        searchBar.placeholder = "搜索地点"
        searchBar.delegate = self
        searchBar.searchBarStyle = .minimal
        addSubview(searchBar)

        // Current location button
        currentLocationButton.setImage(UIImage(systemName: "location.fill"), for: .normal)
        currentLocationButton.backgroundColor = Theme.Colors.themeColor.dynamicColor()
        currentLocationButton.tintColor = .white
        currentLocationButton.layer.cornerRadius = 25
        currentLocationButton.addTarget(self, action: #selector(currentLocationTapped), for: .touchUpInside)

        // Map view
        mapView.delegate = self
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .none
        addSubview(mapView)
        addSubview(currentLocationButton)

        // Add tap gesture to map
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(mapTapped(_:)))
        mapView.addGestureRecognizer(tapGesture)

        setupConstraints()
    }

    private func setupConstraints() {
        let headerView = subviews.first!

        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(60)
        }

        cancelButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }

        let titleLabel = headerView.subviews[1]
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        confirmButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }

        searchBar.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        mapView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.greaterThanOrEqualTo(300)
        }

        currentLocationButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-100)
            make.width.height.equalTo(50)
        }
    }

    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
    }

    @objc private func cancelTapped() {
        delegate?.locationPickerDidCancel()
    }

    @objc private func confirmTapped() {
        delegate?.locationPickerDidSelectLocation(selectedLocationName, coordinate: selectedCoordinate)
    }

    @objc private func currentLocationTapped() {
        requestLocationPermission()
    }

    @objc private func mapTapped(_ gesture: UITapGestureRecognizer) {
        let point = gesture.location(in: mapView)
        let coordinate = mapView.convert(point, toCoordinateFrom: mapView)
        selectLocation(coordinate: coordinate)
    }

    private func requestLocationPermission() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            showLocationPermissionAlert()
        case .authorizedWhenInUse, .authorizedAlways:
            getCurrentLocation()
        @unknown default:
            break
        }
    }

    private func getCurrentLocation() {
        locationManager.startUpdatingLocation()
    }

    private func showLocationPermissionAlert() {
        // This should be handled by the parent view controller
        // For now, we'll just print a message
        XYLog("Location permission denied")
    }

    private func selectLocation(coordinate: CLLocationCoordinate2D) {
        selectedCoordinate = coordinate

        // Remove existing annotations
        mapView.removeAnnotations(mapView.annotations)

        // Add new annotation
        let annotation = MKPointAnnotation()
        annotation.coordinate = coordinate
        mapView.addAnnotation(annotation)

        // Reverse geocode to get location name
        let geocoder = CLGeocoder()
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

        geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, _ in
            DispatchQueue.main.async {
                if let placemark = placemarks?.first {
                    let locationName = self?.formatLocationName(from: placemark) ?? "未知位置"
                    self?.selectedLocationName = locationName
                    annotation.title = locationName
                } else {
                    self?.selectedLocationName = "未知位置"
                    annotation.title = "未知位置"
                }
            }
        }
    }

    private func formatLocationName(from placemark: CLPlacemark) -> String {
        var components: [String] = []

        if let name = placemark.name {
            components.append(name)
        }
        if let locality = placemark.locality {
            components.append(locality)
        }
        if let country = placemark.country {
            components.append(country)
        }

        return components.joined(separator: ", ")
    }
}

// MARK: - MKMapViewDelegate
extension LocationPickerView: MKMapViewDelegate {
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        guard !(annotation is MKUserLocation) else { return nil }

        let identifier = "LocationPin"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)

        if annotationView == nil {
            annotationView = MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            annotationView?.canShowCallout = true
        } else {
            annotationView?.annotation = annotation
        }

        return annotationView
    }
}

// MARK: - CLLocationManagerDelegate
extension LocationPickerView: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }

        locationManager.stopUpdatingLocation()

        let region = MKCoordinateRegion(
            center: location.coordinate,
            latitudinalMeters: 1000,
            longitudinalMeters: 1000
        )
        mapView.setRegion(region, animated: true)

        selectLocation(coordinate: location.coordinate)
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        XYLog("Location error: \(error.localizedDescription)")
    }

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            getCurrentLocation()
        case .denied, .restricted:
            showLocationPermissionAlert()
        default:
            break
        }
    }
}

// MARK: - UISearchBarDelegate
extension LocationPickerView: UISearchBarDelegate {
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()

        guard let searchText = searchBar.text, !searchText.isEmpty else { return }

        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = searchText
        request.region = mapView.region

        let search = MKLocalSearch(request: request)
        search.start { [weak self] response, error in
            DispatchQueue.main.async {
                guard let response = response, let firstItem = response.mapItems.first else {
                    XYLog("Search error: \(error?.localizedDescription ?? "Unknown error")")
                    return
                }

                let coordinate = firstItem.placemark.coordinate
                self?.selectLocation(coordinate: coordinate)

                let region = MKCoordinateRegion(
                    center: coordinate,
                    latitudinalMeters: 1000,
                    longitudinalMeters: 1000
                )
                self?.mapView.setRegion(region, animated: true)
            }
        }
    }
}
