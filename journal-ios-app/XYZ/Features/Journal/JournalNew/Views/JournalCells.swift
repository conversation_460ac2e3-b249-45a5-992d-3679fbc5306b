import ObjectiveC
import UIKit

protocol FormatProtocol {
    var format: FontFormat { get }
    var disableEditing: Bool { get }
}

// Title Cell
class TitleCell: UITableViewCell {

    var textView: RSKPlaceholderTextView!
    private weak var delegate: UITextViewDelegate?

    private weak var viewModel: JournalNewViewModel?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.selectionStyle = .none

        self.backgroundColor = .clear
        self.contentView.backgroundColor = .clear

        textView = RSKPlaceholderTextView()
        textView.font = UIFont.boldSystemFont(ofSize: 22)
        textView.textColor = Theme.Colors.text.dynamicColor()
        textView.backgroundColor = .clear
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0)
        textView.placeholder = "标题"
        textView.returnKeyType = .next
        textView.tag = 1
        textView.minimumHeight = 40
        textView.isScrollEnabled = false

        contentView.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
    }

    func configure(text: String, delegate: UITextViewDelegate, viewModel: JournalNewViewModel) {
        let attributedText = text.toAttributedString()
        textView.attributedText = attributedText
        textView.delegate = delegate
        self.delegate = delegate
    }

    func getText() -> String {
        return textView.text
    }
}

// Content Cell
class ContentCell: UITableViewCell {
    var textView: RSKPlaceholderTextView!
    private weak var delegate: UITextViewDelegate?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.selectionStyle = .none

        self.backgroundColor = .clear
        self.contentView.backgroundColor = .clear

        textView = RSKPlaceholderTextView()
        textView.font = UIFont.systemFont(ofSize: 18)
        textView.textColor = Theme.Colors.text.dynamicColor()
        textView.backgroundColor = .clear
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0)
        textView.placeholder = "这里写你的想法"
        textView.tag = 2
        textView.minimumHeight = 40
        textView.isScrollEnabled = false

        contentView.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
    }

    func configure(text: String, delegate: UITextViewDelegate?, viewModel: FormatProtocol?) {

        let attributedText = text.toAttributedString()
        textView.attributedText = attributedText

        if let delegate = delegate {
            textView.delegate = delegate
            self.delegate = delegate
        }

        if let viewModel = viewModel {
            let weight = viewModel.format.weight
            let isItalic = viewModel.format.isItalic

            textView.lineHeightMultiple = viewModel.format.lineHeightMultiple

            if isItalic {
                textView.font = UIFont.italicSystemFont(ofSize: CGFloat(viewModel.format.fontSize))
            } else {
                textView.font = UIFont.systemFont(ofSize: CGFloat(viewModel.format.fontSize), weight: weight.toUIFontWeight())
            }

            textView.isEditable = !viewModel.disableEditing
        }
    }

    func getText() -> String {
        return textView.toHTMLString() ?? ""
    }
}

// Media Cell
class MediaCell: UITableViewCell {
    private var mediaContentView: MediaContentView!
    private weak var delegate: MediaContentViewDelegate?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.selectionStyle = .none
        // Set background to be clear
        self.backgroundColor = .clear
        self.contentView.backgroundColor = .clear
    }

    func configure(mediaType: MediaContentView.MediaType, delegate: MediaContentViewDelegate) {
        // Remove previous media view if exists
        mediaContentView?.removeFromSuperview()

        // Create new media view
        mediaContentView = MediaContentView(mediaType: mediaType)
        mediaContentView.delegate = delegate
        self.delegate = delegate

        contentView.addSubview(mediaContentView)
        mediaContentView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
    }

    func remakeConstraints() {

    }
}

// Audio Cell
class AudioCell: UITableViewCell {
    private var audioView: AudioPlayerView!

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.selectionStyle = .none
    }

    func configure(url: URL, duration: TimeInterval, delegate: AudioContentViewDelegate?) {
        // Remove previous audio view if exists
        audioView?.removeFromSuperview()

        // Create new audio view
        audioView = AudioPlayerView(audioURL: url, duration: duration)
        audioView.delegate = delegate

        contentView.addSubview(audioView)
        audioView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
}
