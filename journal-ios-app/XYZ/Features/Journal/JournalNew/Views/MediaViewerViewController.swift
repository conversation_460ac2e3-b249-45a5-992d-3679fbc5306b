import UIKit
import AVFoundation
import SnapKit

class MediaViewerViewController: UIViewController {

    // MARK: - Properties
    private var mediaItems: [MediaItem] = []
    private var currentIndex: Int = 0
    private var initialMediaItem: MediaItem

    // MARK: - UI Elements
    private let scrollView = UIScrollView()
    private let pageControl = UIPageControl()
    private let closeButton = UIButton(type: .system)
    private let backgroundView = UIView()

    // Video player properties
    private var currentPlayer: AVPlayer?
    private var currentPlayerLayer: AVPlayerLayer?

    var isVideoPlaying = false

    // MARK: - Initialization
    init(mediaItem: MediaItem, allMediaItems: [MediaItem] = []) {
        self.initialMediaItem = mediaItem
        self.mediaItems = allMediaItems.isEmpty ? [mediaItem] : allMediaItems

        // Find the index of the initial media item
        if let index = self.mediaItems.firstIndex(where: { $0.id == mediaItem.id }) {
            self.currentIndex = index
        }

        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupMediaPages()
        updateVideoPlayerForCurrentPage()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        scrollToCurrentItem()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        cleanupVideoPlayer()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .black

        // Background view with blur effect
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.95)
        view.addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Close button
        closeButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
        closeButton.tintColor = .white
        closeButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        closeButton.layer.cornerRadius = 20
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        closeButton.isUserInteractionEnabled = true
        closeButton.layer.zPosition = 1000 // Ensure it's on top

        view.addSubview(closeButton)
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(20)
            make.right.equalToSuperview().offset(-20)
            make.width.height.equalTo(40)
        }

        // Scroll view for horizontal paging
        scrollView.isPagingEnabled = true
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.delegate = self
        scrollView.backgroundColor = .clear

        view.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Page control (only show if more than one item)
        if mediaItems.count > 1 {
            pageControl.numberOfPages = mediaItems.count
            pageControl.currentPage = currentIndex
            pageControl.pageIndicatorTintColor = UIColor.white.withAlphaComponent(0.5)
            pageControl.currentPageIndicatorTintColor = .white
            pageControl.addTarget(self, action: #selector(pageControlChanged), for: .valueChanged)

            view.addSubview(pageControl)
            pageControl.snp.makeConstraints { make in
                make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-20)
                make.centerX.equalToSuperview()
            }
        }

        // Bring close button to front to ensure it receives touch events
        view.bringSubviewToFront(closeButton)

        // Add single tap gesture to dismiss (but not on close button area)
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        tapGesture.numberOfTapsRequired = 1
        tapGesture.cancelsTouchesInView = false // Allow other views to receive touches
        view.addGestureRecognizer(tapGesture)
    }

    private func setupMediaPages() {
        // Clean up any existing video players first
        cleanupVideoPlayer()

        // Remove existing page views
        scrollView.subviews.forEach { $0.removeFromSuperview() }

        let screenWidth = view.bounds.width
        let screenHeight = view.bounds.height

        // Ensure we have valid dimensions
        guard screenWidth > 0 && screenHeight > 0 else { return }

        // Set content size for horizontal scrolling
        scrollView.contentSize = CGSize(width: screenWidth * CGFloat(mediaItems.count), height: screenHeight)

        for (index, mediaItem) in mediaItems.enumerated() {
            let pageView = createPageView(for: mediaItem, at: index)
            pageView.tag = index // Add tag for easier identification
            scrollView.addSubview(pageView)

            // Set frame directly instead of using constraints for better performance
            pageView.frame = CGRect(
                x: CGFloat(index) * screenWidth,
                y: 0,
                width: screenWidth,
                height: screenHeight
            )
        }
    }

    private func createPageView(for mediaItem: MediaItem, at index: Int) -> UIView {
        let pageView = UIView()
        pageView.backgroundColor = .clear

        switch mediaItem.type {
        case .image(let url):
            let imageView = createImageView(with: url)
            pageView.addSubview(imageView)

            // Use SnapKit for proper constraint-based layout within safe area
            imageView.snp.makeConstraints { make in
                make.top.equalTo(pageView.safeAreaLayoutGuide.snp.top).offset(60) // Space for close button
                make.bottom.equalTo(pageView.safeAreaLayoutGuide.snp.bottom).offset(-60) // Space for page control
                make.leading.trailing.equalToSuperview() // Full width
            }

        case .video(let url):
            let videoContainer = createVideoView(with: url)
            pageView.addSubview(videoContainer)

            // Use SnapKit for proper constraint-based layout within safe area
            videoContainer.snp.makeConstraints { make in
                // Fill the entire safe area, leaving minimal space for UI elements
                make.top.equalTo(pageView.safeAreaLayoutGuide.snp.top).offset(60) // Space for close button
                make.bottom.equalTo(pageView.safeAreaLayoutGuide.snp.bottom).offset(-60) // Space for page control
                make.leading.trailing.equalToSuperview() // Full width
            }
        }

        return pageView
    }

    private func createImageView(with url: URL) -> UIImageView {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        imageView.isUserInteractionEnabled = true

        // Add pinch gesture for zoom
        let pinchGesture = UIPinchGestureRecognizer(target: self, action: #selector(handlePinchGesture(_:)))
        imageView.addGestureRecognizer(pinchGesture)

        // Add double tap gesture for zoom
        let doubleTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleDoubleTap(_:)))
        doubleTapGesture.numberOfTapsRequired = 2
        imageView.addGestureRecognizer(doubleTapGesture)

        // Load image
        loadImage(from: url, into: imageView)

        return imageView
    }

    private func createVideoView(with url: URL) -> UIView {
        // Use a custom view that will create the player only when needed
        let customContainer = VideoContainerView()
        customContainer.backgroundColor = .black
        customContainer.layer.cornerRadius = 8
        customContainer.clipsToBounds = true
        customContainer.videoURL = url // Store URL for later player creation
        customContainer.loadThumnail()
        return customContainer
    }

    private func loadImage(from url: URL, into imageView: UIImageView) {
        let imageURL = MediaHandler.remapMediaURL(url: url)

        // Add loading indicator
        let activityIndicator = UIActivityIndicatorView(style: .large)
        activityIndicator.color = .white
        imageView.addSubview(activityIndicator)
        activityIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        activityIndicator.startAnimating()

        DispatchQueue.global().async {
            if let data = try? Data(contentsOf: imageURL), let image = UIImage(data: data) {
                DispatchQueue.main.async {
                    imageView.image = image
                    activityIndicator.stopAnimating()
                    activityIndicator.removeFromSuperview()
                }
            } else {
                DispatchQueue.main.async {
                    imageView.image = UIImage(systemName: "exclamationmark.triangle")
                    imageView.tintColor = .white
                    activityIndicator.stopAnimating()
                    activityIndicator.removeFromSuperview()
                }
            }
        }
    }

    private func scrollToCurrentItem() {
        guard view.bounds.width > 0 else { return }
        let xOffset = CGFloat(currentIndex) * view.bounds.width
        scrollView.setContentOffset(CGPoint(x: xOffset, y: 0), animated: false)
        pageControl.currentPage = currentIndex
    }

    private func cleanupVideoPlayer() {
        currentPlayer?.pause()
        currentPlayerLayer?.removeFromSuperlayer()
        currentPlayer = nil
        currentPlayerLayer = nil
        isVideoPlaying = false
    }

    // MARK: - Actions
    @objc private func closeButtonTapped() {
        XYLog("Close button tapped") // Debug log
        cleanupVideoPlayer()
        dismiss(animated: true, completion: nil)
    }

    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)

        // Don't dismiss if tapping on close button area
        let closeButtonFrame = closeButton.frame.insetBy(dx: -10, dy: -10) // Add some padding
        if closeButtonFrame.contains(location) {
            return
        }

        // Don't dismiss if tapping on page control area
        if mediaItems.count > 1 {
            let pageControlFrame = pageControl.frame.insetBy(dx: -20, dy: -20)
            if pageControlFrame.contains(location) {
                return
            }
        }
    }

    @objc private func pageControlChanged() {
        let xOffset = CGFloat(pageControl.currentPage) * view.bounds.width
        scrollView.setContentOffset(CGPoint(x: xOffset, y: 0), animated: true)
    }

    // Video controls are now handled by VideoContainerView

    @objc private func handlePinchGesture(_ gesture: UIPinchGestureRecognizer) {
        guard let imageView = gesture.view as? UIImageView else { return }

        if gesture.state == .began || gesture.state == .changed {
            imageView.transform = imageView.transform.scaledBy(x: gesture.scale, y: gesture.scale)
            gesture.scale = 1.0
        }
    }

    @objc private func handleDoubleTap(_ gesture: UITapGestureRecognizer) {
        guard let imageView = gesture.view as? UIImageView else { return }

        UIView.animate(withDuration: 0.3) {
            if imageView.transform == .identity {
                imageView.transform = CGAffineTransform(scaleX: 2.0, y: 2.0)
            } else {
                imageView.transform = .identity
            }
        }
    }
}

// MARK: - UIScrollViewDelegate
extension MediaViewerViewController: UIScrollViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        updateCurrentPageIndex()
    }

    func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        updateCurrentPageIndex()
    }

    private func updateCurrentPageIndex() {
        guard view.bounds.width > 0 else { return }
        let pageIndex = Int(round(scrollView.contentOffset.x / view.bounds.width))

        // Ensure the page index is within bounds
        let newIndex = max(0, min(pageIndex, mediaItems.count - 1))

        if newIndex != currentIndex {
            currentIndex = newIndex
            pageControl.currentPage = currentIndex

            // Update video player for current page
            updateVideoPlayerForCurrentPage()

            // Reset transforms for all image views to prevent overlap issues
            resetImageTransforms()
        }
    }

    private func resetImageTransforms() {
        for pageView in scrollView.subviews {
            if let imageView = pageView.subviews.first(where: { $0 is UIImageView }) as? UIImageView {
                imageView.transform = .identity
            }
        }
    }

    private func updateVideoPlayerForCurrentPage() {
        // Clean up previous player
        cleanupVideoPlayer()

        // Set up new player if current item is video
        guard currentIndex < mediaItems.count else { return }
        let currentItem = mediaItems[currentIndex]
        if case .video = currentItem.type {
            // Find the video view in current page and create player on demand
            let pageViews = scrollView.subviews.filter { $0.tag >= 0 && $0.tag < mediaItems.count }
            if let pageView = pageViews.first(where: { $0.tag == currentIndex }),
               let videoContainer = pageView.subviews.first(where: { $0 is VideoContainerView }) as? VideoContainerView {

                // Create player only when needed
                let (player, playerLayer) = videoContainer.createPlayerIfNeeded()
                currentPlayer = player
                currentPlayerLayer = playerLayer
            }
        }
    }
}

// MARK: - VideoContainerView
class VideoContainerView: UIView {
    var playerLayer: AVPlayerLayer?
    var videoURL: URL?
    // UI Elements
    private var playButton: UIButton!
    private var timeSlider: UISlider!
    private var currentTimeLabel: UILabel!
    private var remainingTimeLabel: UILabel!
    private var controlsContainerView: UIView!
    private var thumbnailView: UIImageView = UIImageView()
    // Player state
    private var player: AVPlayer?
    private var timeObserver: Any?
    private var isVideoPlaying = false
    //    private weak var parentViewController: MediaViewerViewController?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupControls()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupControls()
    }

    private func setupControls() {
        // Controls container
        controlsContainerView = UIView()
        controlsContainerView.backgroundColor = UIColor.black.withAlphaComponent(0.8) // More opaque
        controlsContainerView.layer.cornerRadius = 8
        controlsContainerView.layer.borderWidth = 1 // Add border for visibility
        controlsContainerView.layer.borderColor = UIColor.white.withAlphaComponent(0.3).cgColor
        controlsContainerView.alpha = 1.0 // Ensure it's visible initially
        addSubview(controlsContainerView)

        // Create thumbnail view first
        thumbnailView.backgroundColor = .black
        thumbnailView.contentMode = .scaleAspectFit
        thumbnailView.clipsToBounds = true
        self.addSubview(thumbnailView)

        // Play button
        playButton = UIButton(type: .custom)
        playButton.imageView?.contentMode = .scaleAspectFit
        playButton.setImage(UIImage(systemName: "play.circle.fill"), for: .normal)
        playButton.tintColor = .white
        playButton.backgroundColor = .clear // More opaque background
        playButton.alpha = 1.0 // Ensure it's visible initially
        playButton.addTarget(self, action: #selector(playButtonTapped), for: .touchUpInside)
        addSubview(playButton)

        // Time labels
        currentTimeLabel = UILabel()
        currentTimeLabel.text = "0:00"
        currentTimeLabel.textColor = .white
        currentTimeLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 12, weight: .medium)
        currentTimeLabel.textAlignment = .left

        remainingTimeLabel = UILabel()
        remainingTimeLabel.text = "-0:00"
        remainingTimeLabel.textColor = .white
        remainingTimeLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 12, weight: .medium)
        remainingTimeLabel.textAlignment = .right

        // Time slider
        timeSlider = UISlider()
        timeSlider.minimumValue = 0.0
        timeSlider.maximumValue = 1.0  // Safe default value
        timeSlider.value = 0.0
        timeSlider.minimumTrackTintColor = .white
        timeSlider.maximumTrackTintColor = UIColor.white.withAlphaComponent(0.3)
        timeSlider.thumbTintColor = .white
        timeSlider.addTarget(self, action: #selector(sliderValueChanged), for: .valueChanged)
        timeSlider.addTarget(self, action: #selector(sliderTouchBegan), for: .touchDown)
        timeSlider.addTarget(self, action: #selector(sliderTouchEnded), for: [.touchUpInside, .touchUpOutside])

        // Add to controls container
        controlsContainerView.addSubview(currentTimeLabel)
        controlsContainerView.addSubview(timeSlider)
        controlsContainerView.addSubview(remainingTimeLabel)

        setupConstraints()

        // Add tap gesture for showing/hiding controls
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(containerTapped))
        tapGesture.cancelsTouchesInView = false
        addGestureRecognizer(tapGesture)

        // Add double tap gesture for toggling video scale mode
        let doubleTapGesture = UITapGestureRecognizer(target: self, action: #selector(containerDoubleTapped))
        doubleTapGesture.numberOfTapsRequired = 2
        doubleTapGesture.cancelsTouchesInView = false
        addGestureRecognizer(doubleTapGesture)

        // Ensure single tap doesn't interfere with double tap
        tapGesture.require(toFail: doubleTapGesture)

        isUserInteractionEnabled = true

        // Ensure controls are initially visible
        showControls()
    }

    func loadThumnail() {
        if let videoURL = videoURL {
            MediaHandler.generateVideoThumbnail(from: videoURL) { image in
                DispatchQueue.main.async {
                    self.thumbnailView.image = image
                }
            }
        }
    }

    private func setupConstraints() {

        thumbnailView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Play button - center of view
        playButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(80) // Increase size for better visibility
        }

        // Controls container - bottom of view
        controlsContainerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(40)
        }

        // Current time label
        currentTimeLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.width.equalTo(50)
            make.centerY.equalToSuperview()
        }

        // Remaining time label
        remainingTimeLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(50)
        }

        // Time slider
        timeSlider.snp.makeConstraints { make in
            make.left.equalTo(currentTimeLabel.snp.right).offset(8)
            make.right.equalTo(remainingTimeLabel.snp.left).offset(-8)
            make.centerY.equalToSuperview()
            make.height.equalTo(30)
        }
    }

    func createPlayerIfNeeded() -> (AVPlayer?, AVPlayerLayer?) {
        guard let url = videoURL else { return (nil, nil) }

        // Clean up existing player
        cleanupPlayer()

        // Create new player
        let player = AVPlayer(url: url)
        let playerLayer = AVPlayerLayer(player: player)

        // Set default video gravity initially
        playerLayer.videoGravity = .resizeAspect
        playerLayer.frame = bounds

        self.player = player
        self.playerLayer = playerLayer
        layer.insertSublayer(playerLayer, at: 0)

        // Reset slider to safe defaults and clear any error state
        timeSlider.maximumValue = 1.0
        timeSlider.value = 0.0
        timeSlider.isEnabled = true
        currentTimeLabel.text = "0:00"
        remainingTimeLabel.text = "--:--"
        playButton.setImage(UIImage(systemName: "play.circle.fill"), for: .normal)

        // Ensure controls are visible and on top
        showControls()
        bringSubviewToFront(playButton)
        bringSubviewToFront(controlsContainerView)

        // Add time observer for updating slider
        let timeInterval = CMTime(seconds: 0.1, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player.addPeriodicTimeObserver(forInterval: timeInterval, queue: .main) { [weak self] time in
            self?.updateTimeDisplay(currentTime: time)
        }

        // Add observer for video completion
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: player.currentItem,
            queue: .main
        ) { [weak self] _ in
            self?.videoDidFinish()
        }

        // Add observer for video loading errors
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemFailedToPlayToEndTime,
            object: player.currentItem,
            queue: .main
        ) { [weak self] _ in
            self?.handleVideoError()
        }

        // Determine optimal video gravity asynchronously after player is set up
        Task { [weak self] in
            await self?.determineOptimalVideoGravity(for: player, playerLayer: playerLayer)
        }

        // Update slider when video is ready (with delay to ensure player item is ready)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            if let currentItem = self?.player?.currentItem {
                self?.obserDuration(item: currentItem)
            }
        }

        return (player, playerLayer)
    }

    private func determineOptimalVideoGravity(for player: AVPlayer, playerLayer: AVPlayerLayer) async {
        // Get container aspect ratio (width/height)
        let containerAspectRatio = bounds.width / bounds.height

        guard let asset = player.currentItem?.asset else {
            // Default to resizeAspectFill if we can't determine video dimensions
            await MainActor.run {
                playerLayer.videoGravity = .resizeAspectFill
            }
            return
        }

        do {
            // Use the new iOS 16+ async API to load tracks
            let tracks = try await asset.loadTracks(withMediaType: .video)
            guard let track = tracks.first else {
                // Default to resizeAspectFill if no video track found
                await MainActor.run {
                    playerLayer.videoGravity = .resizeAspectFill
                }
                return
            }

            // Use the new iOS 16+ async API to load natural size
            let naturalSize = try await track.load(.naturalSize)
            let preferredTransform = try await track.load(.preferredTransform)

            let videoSize = naturalSize.applying(preferredTransform)
            let videoAspectRatio = abs(videoSize.width) / abs(videoSize.height)

            // If video aspect ratio is close to container aspect ratio, use resizeAspectFill
            // Otherwise, use resizeAspect to show the full video without cropping
            let aspectRatioDifference = abs(videoAspectRatio - containerAspectRatio) / containerAspectRatio

            // If the difference is less than 20%, use resizeAspectFill for better screen utilization
            // Otherwise, use resizeAspect to avoid excessive cropping
            let optimalGravity: AVLayerVideoGravity = aspectRatioDifference < 0.2 ? .resizeAspectFill : .resizeAspect

            // Update the player layer on the main thread
            await MainActor.run {
                playerLayer.videoGravity = optimalGravity
            }

        } catch {
            // If loading fails, use default gravity
            XYLog("Failed to determine optimal video gravity: \(error)")
            await MainActor.run {
                playerLayer.videoGravity = .resizeAspectFill
            }
        }
    }

    private func cleanupPlayer() {
        // Remove time observer
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }

        // Remove notification observers
        NotificationCenter.default.removeObserver(self)

        // Clean up player
        player?.pause()
        playerLayer?.removeFromSuperlayer()
        player = nil
        playerLayer = nil
    }

    func obserDuration(item: AVPlayerItem) {
        _ = item.observe(\.duration, options: [.new, .old]) { _, _ in
            self.didPlayDurationUpdate()
        }
    }

    func didPlayDurationUpdate() {
        if let duration = player?.currentItem?.duration,
           duration.isValid,
           !duration.seconds.isNaN,
           !duration.seconds.isInfinite,
           duration.seconds > 0 {
            timeSlider.maximumValue = Float(duration.seconds)
            updateTimeDisplay(currentTime: CMTime.zero)
        } else {
            // Set default values for invalid duration
            timeSlider.maximumValue = 1.0
            currentTimeLabel.text = "0:00"
            remainingTimeLabel.text = "--:--"
        }
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        playerLayer?.frame = bounds
    }

    // MARK: - Actions
    @objc private func playButtonTapped() {
        guard let player = player else { return }

        if isVideoPlaying {
            player.pause()
            playButton.setImage(UIImage(systemName: "play.circle.fill"), for: .normal)
            isVideoPlaying = false
            showThumbnail()
            showControls()
        } else {
            player.play()
            playButton.setImage(UIImage(systemName: "pause.circle.fill"), for: .normal)
            isVideoPlaying = true
            hideThumbnail()
            hideControlsAfterDelay()
        }
    }

    @objc private func sliderValueChanged() {
        guard let player = player else { return }

        let sliderValue = Double(timeSlider.value)
        guard !sliderValue.isNaN && !sliderValue.isInfinite && sliderValue >= 0 else { return }

        guard let duration = player.currentItem?.duration.seconds else {
            return
        }

        let targetTime = CMTime(seconds: sliderValue * duration, preferredTimescale: 600)
        guard targetTime.isValid else { return }
        player.seek(to: targetTime)
    }

    @objc private func sliderTouchBegan() {
        // Pause auto-hide when user is scrubbing
        NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(hideControls), object: nil)
    }

    @objc private func sliderTouchEnded() {
        // Resume auto-hide if video is playing
        if isVideoPlaying {
            hideControlsAfterDelay()
        }
    }

    @objc private func containerTapped(_ gesture: UITapGestureRecognizer) {
        let tapLocation = gesture.location(in: self)

        // Don't handle tap if it's on controls
        if controlsContainerView.frame.contains(tapLocation) || playButton.frame.contains(tapLocation) {
            return
        }

        // Toggle controls visibility
        if controlsContainerView.alpha == 0 || playButton.alpha == 0 {
            showControls()
            if isVideoPlaying {
                hideControlsAfterDelay()
            }
        } else {
            hideControls()
        }
    }

    @objc private func containerDoubleTapped(_ gesture: UITapGestureRecognizer) {
        let tapLocation = gesture.location(in: self)

        // Don't handle double tap if it's on controls
        if controlsContainerView.frame.contains(tapLocation) || playButton.frame.contains(tapLocation) {
            return
        }

        // Toggle video scale mode
        toggleVideoScaleMode()
    }

    private func toggleVideoScaleMode() {
        guard let playerLayer = playerLayer else { return }

        let newGravity: AVLayerVideoGravity
        let feedbackMessage: String

        switch playerLayer.videoGravity {
        case .resizeAspectFill:
            newGravity = .resizeAspect
            feedbackMessage = "Fit to Screen"
        case .resizeAspect:
            newGravity = .resizeAspectFill
            feedbackMessage = "Fill Screen"
        default:
            newGravity = .resizeAspectFill
            feedbackMessage = "Fill Screen"
        }

        UIView.animate(withDuration: 0.3) {
            playerLayer.videoGravity = newGravity
        }

        // Show a brief feedback message
        showScaleModeIndicator(message: feedbackMessage)

        // Show controls briefly to indicate the change
        showControls()
        if isVideoPlaying {
            hideControlsAfterDelay()
        }
    }

    private func showScaleModeIndicator(message: String) {
        // Create a temporary label to show the scale mode change
        let indicatorLabel = UILabel()
        indicatorLabel.text = message
        indicatorLabel.textColor = .white
        indicatorLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        indicatorLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        indicatorLabel.textAlignment = .center
        indicatorLabel.layer.cornerRadius = 8
        indicatorLabel.clipsToBounds = true
        indicatorLabel.alpha = 0

        addSubview(indicatorLabel)
        indicatorLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(40)
        }

        // Animate the indicator
        UIView.animate(withDuration: 0.3, animations: {
            indicatorLabel.alpha = 1
        }) { _ in
            UIView.animate(withDuration: 0.3, delay: 1.0, options: [], animations: {
                indicatorLabel.alpha = 0
            }) { _ in
                indicatorLabel.removeFromSuperview()
            }
        }
    }

    // MARK: - Control Visibility
    private func showControls() {
        UIView.animate(withDuration: 0.3) {
            self.controlsContainerView.alpha = 1
            self.playButton.alpha = 1
        }
    }

    private func hideControlsAfterDelay() {
        NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(hideControls), object: nil)
        perform(#selector(hideControls), with: nil, afterDelay: 3.0)
    }

    @objc private func hideControls() {
        guard isVideoPlaying else { return }

        UIView.animate(withDuration: 0.3) {
            self.controlsContainerView.alpha = 0
            self.playButton.alpha = 0
        }
    }

    // MARK: - Time Display
    private func updateTimeDisplay(currentTime: CMTime) {
        guard let duration = player?.currentItem?.duration,
              duration.isValid,
              !duration.seconds.isNaN,
              !duration.seconds.isInfinite,
              duration.seconds > 0 else {
            // Set default values for invalid duration
            currentTimeLabel.text = "0:00"
            remainingTimeLabel.text = "--:--"
            return
        }

        let currentSeconds = currentTime.seconds
        let totalSeconds = duration.seconds
        let remainingSeconds = totalSeconds - currentSeconds

        // Validate current time
        guard !currentSeconds.isNaN && !currentSeconds.isInfinite && currentSeconds >= 0 else {
            return
        }

        // Update slider (only if user is not scrubbing)
        if !timeSlider.isTracking {
            timeSlider.value = Float(currentSeconds/totalSeconds)
        }

        // Update time labels
        currentTimeLabel.text = formatTime(currentSeconds)
        remainingTimeLabel.text = "-\(formatTime(remainingSeconds))"
    }

    private func formatTime(_ seconds: Double) -> String {
        guard !seconds.isNaN && !seconds.isInfinite else { return "0:00" }

        let totalSeconds = Int(seconds)
        let minutes = totalSeconds / 60
        let seconds = totalSeconds % 60

        if minutes >= 60 {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            return String(format: "%d:%02d:%02d", hours, remainingMinutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }

    // MARK: - Video State
    private func videoDidFinish() {
        player?.seek(to: .zero)
        playButton.setImage(UIImage(systemName: "play.circle.fill"), for: .normal)
        isVideoPlaying = false
        showThumbnail()
        showControls()

        // Reset time display
        timeSlider.value = 0.0
        updateTimeDisplay(currentTime: .zero)

        // Cancel any pending hide operations
        NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(hideControls), object: nil)
    }

    private func handleVideoError() {
        XYLog("Video failed to load or play")
        playButton.setImage(UIImage(systemName: "exclamationmark.triangle"), for: .normal)
        isVideoPlaying = false
        showThumbnail()
        showControls()

        // Set error state for time display
        currentTimeLabel.text = "Error"
        remainingTimeLabel.text = "Error"
        timeSlider.isEnabled = false
    }

    private func showThumbnail() {
        if let thumbnailView = subviews.first(where: { $0 is UIImageView }) as? UIImageView {
            thumbnailView.isHidden = false
        }
    }

    private func hideThumbnail() {
        if let thumbnailView = subviews.first(where: { $0 is UIImageView }) as? UIImageView {
            thumbnailView.isHidden = true
        }
    }

    func findParentViewController() -> MediaViewerViewController? {
        var responder: UIResponder? = self
        while let nextResponder = responder?.next {
            if let viewController = nextResponder as? MediaViewerViewController {
                return viewController
            }
            responder = nextResponder
        }
        return nil
    }

    deinit {
        cleanupPlayer()
    }
}

// MARK: - MediaItem Model
struct MediaItem {
    let id: String
    let type: MediaType

    enum MediaType {
        case image(url: URL)
        case video(url: URL)
    }

    init(id: String = UUID().uuidString, type: MediaType) {
        self.id = id
        self.type = type
    }
}
