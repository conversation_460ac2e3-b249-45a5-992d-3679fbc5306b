import AVFoundation
import AVKit
import SnapKit
import UIKit

protocol MediaContentViewDelegate: AnyObject {
    func didTapDelete(mediaView: MediaContentView, mediaType: MediaContentView.MediaType)
    func didChangeWidthMode(mediaView: MediaContentView, isFullWidth: Bool)
}

protocol AudioContentViewDelegate: AnyObject {
    func didTapDelete(mediaView: AudioPlayerView)
}

class MediaContentView: UIView {

    enum MediaType: Equatable {
        case image(url: URL,  mode: WidthMode, size: CGSize)  // Uses URL instead of just UIImage
        case video(url: URL, thumbnail: UIImage?, mode: WidthMode, size: CGSize )
        case audio(url: URL, duration: TimeInterval)

        static func fromJournalContentItem(_ item: JournalContentItem) -> MediaType? {
            switch item.type {
            case .image:
                if let url = item.url {
                    return .image(url: url, mode: item.widthMode, size: item.size)
                }
                return nil

            case .video:
                if let url = item.url {
                    return .video(url: url, thumbnail: nil, mode: item.widthMode, size: item.size)
                }
                return nil
            default:
                return nil
            }
        }

        // Helper to save image to file
        private func saveImageToFile(_ image: UIImage) -> URL? {
            do {
                return try SharedFileManager.shared.saveImageToShared(image)
            } catch {
                XYLog("Error saving image: \(error)")
                return nil
            }
        }
    }
    // MARK: - Properties

    private let mediaType: MediaType
    private var widthMode: WidthMode = .full {
        didSet {
            updateWidthConstraint()
        }
    }
    private var frameSize: CGSize = .zero
    private var widtHeightRation = 1.0

    weak var delegate: MediaContentViewDelegate?

    // MARK: - UI Elements

    private let containerView = UIView()
    private let mediaContainerView = UIView()

    // Video related
    private var videoThumbnailView: UIImageView?
    private var playButton: UIButton?
    //    private var player: AVPlayer?
    //    private var playerLayer: AVPlayerLayer?
    //    private var isVideoPlaying = false
    private var disableEdit: Bool = false

    // Controls
    private let controlsContainer = UIView()
    private let deleteButton = UIButton(type: .system)
    private let widthToggleButton = UIButton(type: .system)

    var alignment: NSTextAlignment = .left

    // MARK: - Initialization

    init(mediaType: MediaType, disableEdit: Bool = false, alignment: NSTextAlignment = .left) {
        self.mediaType = mediaType
        self.disableEdit = disableEdit
        self.alignment = alignment
        super.init(frame: .zero)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        // Clean up video resources
        NotificationCenter.default.removeObserver(self)
    }

    func updateAlignment() {
        updateWidthConstraint()
    }

    // MARK: - Setup

    private func setupUI() {
        // Container setup
        addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Media container
        containerView.addSubview(mediaContainerView)
        mediaContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.height.equalTo(200) // Set a default height
            make.bottom.equalToSuperview().offset(-8)
        }

        // Style media container
        mediaContainerView.layer.cornerRadius = 8
        mediaContainerView.clipsToBounds = true
        mediaContainerView.backgroundColor = .systemGray6

        // Setup content based on type
        setupMediaContent()

        // Controls
        setupControls()
    }

    private func setupMediaContent() {
        switch mediaType {
        case .image(let url, let mode, let size):
            self.widthMode = mode
            self.frameSize = size
            loadAndSetupImageFromURL(url)
        case .video(let url, let thumbnail, let mode, let size):
            self.widthMode = mode
            self.frameSize = size
            setupVideoContent(url, thumbnail: thumbnail)
        case .audio(let url, let duration):
            setupAudioContent(url, duration: duration)
        }
    }

    private func loadAndSetupImageFromURL(_ url: URL) {

        let imageURL = MediaHandler.remapMediaURL(url: url)

        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true

        // Add loading indicator
        let activityIndicator = UIActivityIndicatorView(style: .medium)
        mediaContainerView.addSubview(activityIndicator)
        activityIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        activityIndicator.startAnimating()

        // Add image view
        mediaContainerView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Only add tap gesture if expansion is enabled
        imageView.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(imageTapped))
        imageView.addGestureRecognizer(tapGesture)

        // Load image
        DispatchQueue.global().async {
            if let data = try? Data(contentsOf: imageURL),
               let image = UIImage(data: data) {
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }

                    imageView.image = image
                    activityIndicator.stopAnimating()
                    activityIndicator.removeFromSuperview()

                    // Defer the constraint update to the next run loop cycle.
                    // This allows any immediate layout effects of setting the image
                    // to propagate before we recalculate constraints.
                    DispatchQueue.main.async { [weak self] in // self is already optional due to the outer [weak self]
                        self?.updateWidthConstraint()
                    }
                }
            } else {
                DispatchQueue.main.async { [weak self] in
                    // Show error image
                    imageView.image = UIImage(systemName: "exclamationmark.triangle")
                    imageView.contentMode = .center
                    imageView.tintColor = .gray
                    activityIndicator.stopAnimating()
                    activityIndicator.removeFromSuperview()
                    // Defer the constraint update for consistency.
                    DispatchQueue.main.async { [weak self] in // self is already optional
                        self?.updateWidthConstraint()
                    }
                }
            }
        }
    }

    @objc private func imageTapped() {
        openMediaViewer()
    }

    private func findViewController() -> UIViewController? {
        var responder: UIResponder? = self
        while let nextResponder = responder?.next {
            if let viewController = nextResponder as? UIViewController {
                return viewController
            }
            responder = nextResponder
        }
        return nil
    }

    private func setupImageContent(_ image: UIImage) {
        let imageView = UIImageView(image: image)
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true

        mediaContainerView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupVideoContent(_ url: URL, thumbnail: UIImage?) {

        let videoURL = MediaHandler.remapMediaURL(url: url)

        // Create thumbnail view
        let thumbnailView = UIImageView()
        thumbnailView.backgroundColor = .black
        thumbnailView.contentMode = .scaleAspectFill
        thumbnailView.clipsToBounds = true
        self.videoThumbnailView = thumbnailView

        // If thumbnail is provided, use it
        if let thumbnail = thumbnail {
            thumbnailView.image = thumbnail
            self.widtHeightRation = thumbnail.size.width/thumbnail.size.height
            self.updateWidthConstraint()
        } else {
            // Generate thumbnail
            generateVideoThumbnail(from: videoURL) { [weak self] image in
                guard let image = image else { return }
                self?.widtHeightRation = image.size.width/image.size.height
                DispatchQueue.main.async {
                    self?.videoThumbnailView?.image = image
                    self?.updateWidthConstraint()
                }
            }
        }

        // Create play button
        let playButton = UIButton(type: .system)
        playButton.setImage(UIImage(systemName: "play.circle.fill"), for: .normal)
        playButton.tintColor = .white
        playButton.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        playButton.layer.cornerRadius = 25
        playButton.addTarget(self, action: #selector(playVideoButtonTapped), for: .touchUpInside)
        self.playButton = playButton

        // Add to container
        mediaContainerView.addSubview(thumbnailView)
        mediaContainerView.addSubview(playButton)

        // Layout
        thumbnailView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        playButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(50)
        }
    }

    private func generateVideoThumbnail(from url: URL, completion: @escaping (UIImage?) -> Void) {
        DispatchQueue.global(qos: .background).async {
            let asset = AVAsset(url: url)
            let imageGenerator = AVAssetImageGenerator(asset: asset)
            imageGenerator.appliesPreferredTrackTransform = true

            // Try to get thumbnail at 1 second
            let time = CMTime(seconds: 1, preferredTimescale: 60)

            do {
                let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                let thumbnail = UIImage(cgImage: cgImage)
                completion(thumbnail)
            } catch {
                XYLog("Error generating thumbnail: \(error.localizedDescription)")
                completion(nil)
            }
        }
    }

    @objc private func playVideoButtonTapped() {
        openMediaViewer()
    }

    private func setupAudioContent(_ url: URL, duration: TimeInterval) {
        // Audio player UI
        let audioView = AudioPlayerView(audioURL: url, duration: duration)
        // Don't set delegate here as MediaContentView doesn't conform to AudioContentViewDelegate
        // The delegate should be set by the parent view controller

        mediaContainerView.addSubview(audioView)
        audioView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupControls() {
        // Only setup controls if either delete or expand is enabled
        guard self.disableEdit == false else {
            return
        }

        // Controls container
        containerView.addSubview(controlsContainer)
        controlsContainer.snp.makeConstraints { make in
            make.top.equalTo(mediaContainerView.snp.top)
            make.trailing.equalTo(mediaContainerView.snp.trailing)
            make.width.equalTo(60)
            make.height.equalTo(30)
        }

        controlsContainer.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        controlsContainer.layer.cornerRadius = 4

        // Delete button
        deleteButton.setImage(UIImage(systemName: "trash"), for: .normal)
        deleteButton.tintColor = .white
        deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
        controlsContainer.addSubview(deleteButton)
        deleteButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }

        updateWidthToggleButtonImage()
        widthToggleButton.tintColor = .white
        widthToggleButton.addTarget(
            self, action: #selector(widthToggleButtonTapped), for: .touchUpInside)
        controlsContainer.addSubview(widthToggleButton)
        widthToggleButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }

        // Adjust container width based on visible buttons
        let buttonCount = 2
        controlsContainer.snp.updateConstraints { make in
            make.width.equalTo(buttonCount * 16 + 30)
        }
    }

    private func updateWidthToggleButtonImage() {
        let imageName =
        widthMode == .half ? "arrow.down.left.and.arrow.up.right.rectangle.fill" : "arrow.down.left.and.arrow.up.right.rectangle"
        widthToggleButton.setImage(UIImage(systemName: imageName), for: .normal)
    }

    // swiftlint:disable:next cyclomatic_complexity
    private func updateWidthConstraint() {
        updateWidthToggleButtonImage()

        var imageForAspectRatio: UIImage?
        if case .image = mediaType {
            if let imageView = mediaContainerView.subviews.compactMap({ $0 as? UIImageView }).first,
               let currentImage = imageView.image, currentImage.size.width > 0, currentImage.size.height > 0 {
                imageForAspectRatio = currentImage
            }
        }

        mediaContainerView.snp.remakeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.bottom.equalToSuperview().offset(-8).priority(.medium) // Lower priority for bottom to allow height to dominate

            // Step 1: Determine mediaContainerView's width based on widthMode and containerView's width
            if widthMode == .half {
                make.width.equalTo(self.containerView.snp.width).multipliedBy(widthMode.multiplier)
                switch self.alignment {
                case .left:
                    make.left.equalToSuperview()
                case .center:
                    make.centerX.equalToSuperview()
                case .right:
                    make.right.equalToSuperview()
                default:
                    break
                }
            } else { // .full width
                make.trailing.equalToSuperview() // Stretches to containerView's right edge
                make.leading.equalToSuperview()
            }

            switch mediaType {
            case .image:
                let imageAspectRatio: CGFloat // Width / Height
                if let image = imageForAspectRatio, image.size.width > 0, image.size.height > 0 {
                    imageAspectRatio = image.size.width / image.size.height
                } else {
                    // Fallback for unloaded/missing image or image with no dimensions (e.g., 16:9 placeholder)
                    imageAspectRatio = 1.0
                }
                // Height = Width * (Height/Width) = Width * (1.0 / AspectRatio)
                make.height.equalTo(self.mediaContainerView.snp.width).multipliedBy(1.0 / imageAspectRatio).priority(.required)
                make.height.greaterThanOrEqualTo(50).priority(.high) // Ensure a minimum visible height

            case .video:
                let videoAspectRatio: CGFloat = self.widtHeightRation // Assuming 16:9 aspect ratio (Width / Height)
                // Height = Width * (Height/Width) = Width * (1.0 / AspectRatio)
                make.height.equalTo(self.mediaContainerView.snp.width).multipliedBy(1.0 / videoAspectRatio).priority(.required)
                make.height.greaterThanOrEqualTo(50).priority(.high) // Ensure a minimum visible height

            case .audio:
                make.height.equalTo(80).priority(.required) // Fixed height for audio player
            }
        }

        self.setNeedsLayout()
    }

    // MARK: - Actions

    @objc private func deleteButtonTapped() {
        delegate?.didTapDelete(mediaView: self, mediaType: mediaType)
    }

    // MARK: - Media Viewer
    private var isOpeningMediaViewer = false

    private func openMediaViewer() {
        guard let viewController = findViewController() else { return }

        // Prevent multiple rapid calls
        guard !isOpeningMediaViewer else { return }
        isOpeningMediaViewer = true

        // Reset flag after timeout as a safety measure
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.isOpeningMediaViewer = false
        }

        let currentMediaItem: MediaItem

        switch mediaType {
        case .image(let url, _, _):
            // Find the corresponding JournalContentItem to get consistent id
            if let journalVC = findViewController() as? JournalNewViewController,
               let item = journalVC.tableData.first(where: { $0.url == url }) {
                currentMediaItem = MediaItem(id: item.toContentItem().id, type: .image(url: url))
            } else {
                currentMediaItem = MediaItem(id: UUID().uuidString, type: .image(url: url))
            }
        case .video(let url, _, _, _):
            // Find the corresponding JournalContentItem to get consistent id
            let videoURL = MediaHandler.remapMediaURL(url: url)
            if let journalVC = findViewController() as? JournalNewViewController,
               let item = journalVC.tableData.first(where: { $0.url == url }) {
                currentMediaItem = MediaItem(id: item.toContentItem().id, type: .video(url: videoURL))
            } else {
                currentMediaItem = MediaItem(id: UUID().uuidString, type: .video(url: videoURL))
            }
        case .audio:
            return // Audio doesn't need media viewer
        }

        // Collect all media items from the parent view controller
        let allMediaItems = collectAllMediaItems(from: viewController)
        XYLog("Collected \(allMediaItems.count) media items for viewer") // Debug log

        // Ensure we have at least the current item
        let finalMediaItems = allMediaItems.isEmpty ? [currentMediaItem] : allMediaItems

        let mediaViewer = MediaViewerViewController(mediaItem: currentMediaItem, allMediaItems: finalMediaItems)
        mediaViewer.modalPresentationStyle = .fullScreen
        viewController.present(mediaViewer, animated: true) { [weak self] in
            // Reset the flag after presentation is complete
            self?.isOpeningMediaViewer = false
        }
    }

    // swiftlint:disable:next cyclomatic_complexity
    private func collectAllMediaItems(from viewController: UIViewController) -> [MediaItem] {
        var mediaItems: [MediaItem] = []

        // Try to find JournalNewViewController and collect media from tableData
        if let journalVC = viewController as? JournalNewViewController {
            mediaItems = journalVC.tableData.compactMap { item in
                switch item.type {
                case .image:
                    if let url = item.url {
                        return MediaItem(id: item.toContentItem().id, type: .image(url: url))
                    }
                case .video:
                    if let url = item.url {
                        return MediaItem(id: item.toContentItem().id, type: .video(url: MediaHandler.remapMediaURL(url: url)))
                    }
                default:
                    return nil
                }
                return nil
            }
        }
        // Try to find JournalDetailViewController and collect media from contentItems
        else if let detailVC = viewController as? JournalDetailViewController {
            // Access the contentItems through the viewModel
            if let journal = detailVC.viewModel.journal {
                mediaItems = journal.contentItems.compactMap { item in
                    switch item.type {
                    case .image:
                        if let url = URL(string: item.content) {
                            return MediaItem(id: item.id, type: .image(url: url))
                        }
                    case .video:
                        if let url = URL(string: item.content) {
                            return MediaItem(id: item.id, type: .video(url: MediaHandler.remapMediaURL(url: url)))
                        }
                    default:
                        return nil
                    }
                    return nil
                }
            }
        }

        return mediaItems
    }

    @objc private func widthToggleButtonTapped() {
        widthMode = widthMode == .half ? .full : .half
        delegate?.didChangeWidthMode(mediaView: self, isFullWidth: widthMode == .full)
    }

    // MARK: - Public Methods

    func setWidthMode(_ mode: WidthMode) {
        widthMode = mode
    }
}
