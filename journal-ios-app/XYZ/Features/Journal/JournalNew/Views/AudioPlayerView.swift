import AVFoundation
import UIKit

class AudioPlayerView: UIView {
    // MARK: - Properties
    private var audioURL: URL
    private var audioDuration: TimeInterval
    private var audioPlayer: AVAudioPlayer?
    private var timer: Timer?
    private var isPlaying = false

    weak var delegate: AudioContentViewDelegate?
    var disableEdit = false

    // MARK: - UI Elements
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = Colors.cardBackground.dynamicColor().withAlphaComponent(0.5)
        view.layer.cornerRadius = 12
        return view
    }()

    private let playButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "play.fill"), for: .normal)
        button.tintColor = Colors.primary.dynamicColor()
        button.backgroundColor = Colors.themeColor.dynamicColor().withAlphaComponent(0.1)
        button.layer.cornerRadius = 20
        return button
    }()

    private let progressSlider: UISlider = {
        let slider = UISlider()
        slider.minimumTrackTintColor = Colors.theme.dynamicColor()
        slider.maximumTrackTintColor = Colors.secondaryText.dynamicColor().withAlphaComponent(0.3)
        slider.setThumbImage(
            UIImage(systemName: "circle.fill")?.withRenderingMode(.alwaysTemplate), for: .normal)
        slider.tintColor = Colors.theme.dynamicColor()
        return slider
    }()

    private let timeLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = Colors.secondaryText.dynamicColor()
        label.textAlignment = .right
        return label
    }()

    private let durationLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = Colors.secondaryText.dynamicColor()
        label.textAlignment = .left
        return label
    }()

    private let deleteButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "trash"), for: .normal)
        button.tintColor = UIColor.systemRed
        return button
    }()

    // MARK: - Initialization
    init(audioURL: URL, duration: TimeInterval, disableEdit: Bool = false) {
        self.audioURL = MediaHandler.remapMediaURL(url: audioURL)
        self.audioDuration = duration
        self.disableEdit = disableEdit
        super.init(frame: .zero)
        setupUI()
        setupAudioPlayer()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        // Add container view
        addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(80)
        }

        // Add play button
        containerView.addSubview(playButton)
        playButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        // Add delete button
        containerView.addSubview(deleteButton)
        deleteButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(30)
        }

        // Add progress slider
        containerView.addSubview(progressSlider)
        progressSlider.snp.makeConstraints { make in
            make.left.equalTo(playButton.snp.right).offset(12)
            make.right.equalTo(deleteButton.snp.left).offset(-12)
            make.centerY.equalToSuperview().offset(-10)
        }

        // Add time labels
        containerView.addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.left.equalTo(playButton.snp.right).offset(12)
            make.top.equalTo(progressSlider.snp.bottom).offset(2)
            make.width.equalTo(40)
        }

        containerView.addSubview(durationLabel)
        durationLabel.snp.makeConstraints { make in
            make.right.equalTo(deleteButton.snp.left).offset(-12)
            make.top.equalTo(progressSlider.snp.bottom).offset(2)
            make.width.equalTo(40)
        }

        // Set initial time labels
        timeLabel.text = "0:00"
        durationLabel.text = formatTime(audioDuration)

        // Add actions
        playButton.addTarget(self, action: #selector(playButtonTapped), for: .touchUpInside)
        progressSlider.addTarget(self, action: #selector(sliderValueChanged), for: .valueChanged)
        deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)

        deleteButton.isHidden = self.disableEdit
    }

    private func setupAudioPlayer() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback)
            try AVAudioSession.sharedInstance().setActive(true)

            audioPlayer = try AVAudioPlayer(contentsOf: audioURL)
            audioPlayer?.prepareToPlay()
            progressSlider.maximumValue = Float(audioDuration)
        } catch {
            XYLog("Error setting up audio player: \(error)")
        }
    }

    // MARK: - Actions
    @objc private func playButtonTapped() {
        if isPlaying {
            pauseAudio()
        } else {
            playAudio()
        }
    }

    @objc private func sliderValueChanged() {
        audioPlayer?.currentTime = TimeInterval(progressSlider.value)
        timeLabel.text = formatTime(TimeInterval(progressSlider.value))
    }

    @objc private func deleteButtonTapped() {
        self.delegate?.didTapDelete(mediaView: self)
    }

    // MARK: - Audio Control
    private func playAudio() {
        audioPlayer?.play()
        isPlaying = true
        playButton.setImage(UIImage(systemName: "pause.fill"), for: .normal)

        // Start timer to update progress
        timer = Timer.scheduledTimer(
            timeInterval: 0.1, target: self, selector: #selector(updateProgress), userInfo: nil,
            repeats: true)
    }

    private func pauseAudio() {
        audioPlayer?.pause()
        isPlaying = false
        playButton.setImage(UIImage(systemName: "play.fill"), for: .normal)
        timer?.invalidate()
    }

    private func stopAudio() {
        audioPlayer?.stop()
        audioPlayer?.currentTime = 0
        isPlaying = false
        playButton.setImage(UIImage(systemName: "play.fill"), for: .normal)
        progressSlider.value = 0
        timeLabel.text = "0:00"
        timer?.invalidate()
    }

    @objc private func updateProgress() {
        guard let player = audioPlayer else { return }
        progressSlider.value = Float(player.currentTime)
        timeLabel.text = formatTime(player.currentTime)

        // Check if audio finished playing
        if !player.isPlaying && isPlaying {
            stopAudio()
        }
    }

    // MARK: - Helpers
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    // MARK: - Cleanup
    deinit {
        timer?.invalidate()
        audioPlayer?.stop()
    }
}
