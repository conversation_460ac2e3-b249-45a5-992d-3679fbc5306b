import UIKit

protocol TagsPickerViewDelegate: AnyObject {
    func tagsPickerDidUpdateTags(_ tags: [String])
    func tagsPickerDidCancel()
}

class TagsPickerView: BaseView {

    weak var delegate: TagsPickerViewDelegate?

    private let scrollView = UIScrollView()
    private let contentStackView = UIStackView()
    private let tagsContainerView = UIView()
    private let tagsStackView = UIStackView()
    private let addTagTextField = UITextField()
    private let addTagButton = UIButton(type: .system)
    private let confirmButton = UIButton(type: .system)
    private let cancelButton = UIButton(type: .system)
    private let inputContainer = UIView()

    private var tags: [String] = []

    // Predefined popular tags
    private let popularTags = ["工作", "生活", "旅行", "美食", "运动", "学习", "家庭", "朋友", "心情", "思考", "计划", "回忆"]

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    func setTags(_ tags: [String]) {
        self.tags = tags
        updateTagsDisplay()
    }

    private func setupUI() {
        backgroundColor = Colors.tabBarBackground.dynamicColor()
        layer.cornerRadius = 16
        layer.masksToBounds = true

        // Height will be set by the parent view controller

        // Header with title and buttons
        let headerView = UIView()
        headerView.backgroundColor = Colors.cardBackground.dynamicColor()
        addSubview(headerView)

        let titleLabel = UILabel()
        titleLabel.text = "标签"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = Colors.text.dynamicColor()
        titleLabel.textAlignment = .center

        cancelButton.setTitle("取消", for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        cancelButton.setTitleColor(Colors.secondaryText.dynamicColor(), for: .normal)
        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)

        confirmButton.setTitle("确定", for: .normal)
        confirmButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        confirmButton.setTitleColor(Colors.theme.dynamicColor(), for: .normal)
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)

        headerView.addSubview(cancelButton)
        headerView.addSubview(titleLabel)
        headerView.addSubview(confirmButton)

        // Scroll view for content
        addSubview(scrollView)
        scrollView.addSubview(contentStackView)

        contentStackView.axis = .vertical
        contentStackView.spacing = 20
        contentStackView.alignment = .fill

        // Add tag input section
        setupAddTagSection()

        // Current tags section
        setupCurrentTagsSection()

        // Popular tags section
        setupPopularTagsSection()

        setupConstraints()
    }

    private func setupAddTagSection() {
        let addTagSection = UIView()

        let sectionTitle = UILabel()
        sectionTitle.text = "添加新标签"
        sectionTitle.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        sectionTitle.textColor = Colors.text.dynamicColor()

        inputContainer.backgroundColor = Colors.tabBarBackground.dynamicColor()
        inputContainer.layer.cornerRadius = 8
        inputContainer.layer.borderWidth = 1
        inputContainer.layer.borderColor = Colors.borderColor.dynamicColor().cgColor

        addTagTextField.placeholder = "输入标签名称"
        addTagTextField.font = UIFont.systemFont(ofSize: 16)
        addTagTextField.textColor = Colors.text.dynamicColor()
        addTagTextField.delegate = self
        addTagTextField.returnKeyType = .done

        addTagButton.setTitle("添加", for: .normal)
        addTagButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        addTagButton.setTitleColor(Colors.themeColor.light, for: .normal)
        addTagButton.addTarget(self, action: #selector(addTagTapped), for: .touchUpInside)

        inputContainer.addSubview(addTagTextField)
        inputContainer.addSubview(addTagButton)

        addTagSection.addSubview(sectionTitle)
        addTagSection.addSubview(inputContainer)

        contentStackView.addArrangedSubview(addTagSection)

        // Constraints for add tag section
        sectionTitle.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        inputContainer.snp.makeConstraints { make in
            make.top.equalTo(sectionTitle.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(44)
        }

        addTagTextField.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.trailing.equalTo(addTagButton.snp.leading).offset(-8)
        }

        addTagButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(50)
        }
    }

    private func setupCurrentTagsSection() {
        let currentTagsSection = UIView()

        let sectionTitle = UILabel()
        sectionTitle.text = "当前标签"
        sectionTitle.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        sectionTitle.textColor = Colors.text.dynamicColor()

        tagsContainerView.backgroundColor = .clear

        tagsStackView.axis = .vertical
        tagsStackView.spacing = 8
        tagsStackView.alignment = .leading

        tagsContainerView.addSubview(tagsStackView)

        currentTagsSection.addSubview(sectionTitle)
        currentTagsSection.addSubview(tagsContainerView)

        contentStackView.addArrangedSubview(currentTagsSection)

        // Constraints for current tags section
        sectionTitle.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        tagsContainerView.snp.makeConstraints { make in
            make.top.equalTo(sectionTitle.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.greaterThanOrEqualTo(40)
        }

        tagsStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupPopularTagsSection() {
        let popularTagsSection = UIView()

        let sectionTitle = UILabel()
        sectionTitle.text = "热门标签"
        sectionTitle.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        sectionTitle.textColor = Colors.text.dynamicColor()

        let popularTagsContainer = UIView()
        let popularTagsStackView = createTagsFlowLayout(tags: popularTags, isPopular: true)

        popularTagsContainer.addSubview(popularTagsStackView)

        popularTagsSection.addSubview(sectionTitle)
        popularTagsSection.addSubview(popularTagsContainer)

        contentStackView.addArrangedSubview(popularTagsSection)

        // Constraints for popular tags section
        sectionTitle.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        popularTagsContainer.snp.makeConstraints { make in
            make.top.equalTo(sectionTitle.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
        }

        popularTagsStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupConstraints() {
        let headerView = subviews.first!

        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(60)
        }

        cancelButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }

        let titleLabel = headerView.subviews[1]
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        confirmButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.leading.trailing.bottom.equalToSuperview()
        }

        contentStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
            make.width.equalToSuperview().offset(-32)
        }
    }

    private func createTagsFlowLayout(tags: [String], isPopular: Bool = false) -> UIStackView {
        let mainStackView = UIStackView()
        mainStackView.axis = .vertical
        mainStackView.spacing = 8
        mainStackView.alignment = .leading

        var currentRowStackView = UIStackView()
        currentRowStackView.axis = .horizontal
        currentRowStackView.spacing = 8
        currentRowStackView.alignment = .center

        var currentRowWidth: CGFloat = 0
        let maxWidth = UIScreen.main.bounds.width - 64 // Account for margins

        for tag in tags {
            let tagButton = createTagButton(tag: tag, isPopular: isPopular)
            let tagWidth = tagButton.intrinsicContentSize.width

            if currentRowWidth + tagWidth + 8 > maxWidth && !currentRowStackView.arrangedSubviews.isEmpty {
                mainStackView.addArrangedSubview(currentRowStackView)
                currentRowStackView = UIStackView()
                currentRowStackView.axis = .horizontal
                currentRowStackView.spacing = 8
                currentRowStackView.alignment = .center
                currentRowWidth = 0
            }

            currentRowStackView.addArrangedSubview(tagButton)
            currentRowWidth += tagWidth + 8
        }

        if !currentRowStackView.arrangedSubviews.isEmpty {
            mainStackView.addArrangedSubview(currentRowStackView)
        }

        return mainStackView
    }

    private func createTagButton(tag: String, isPopular: Bool = false) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(tag, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        if #available(iOS 15.0, *) {
            var configuration = UIButton.Configuration.plain()
            configuration.contentInsets = NSDirectionalEdgeInsets(top: 6, leading: 12, bottom: 6, trailing: 12)
            button.configuration = configuration
        } else {
            button.contentEdgeInsets = UIEdgeInsets(top: 6, left: 12, bottom: 6, right: 12)
        }
        button.layer.cornerRadius = 16

        if isPopular {
            // Popular tag style
            button.setTitleColor(Colors.secondaryText.dynamicColor(), for: .normal)
            button.backgroundColor = Colors.pageBg.dynamicColor()
            button.addTarget(self, action: #selector(popularTagTapped(_:)), for: .touchUpInside)
        } else {
            // Current tag style
            button.setTitleColor(.white, for: .normal)
            button.backgroundColor = Colors.themeColor.dynamicColor()
            button.addTarget(self, action: #selector(currentTagTapped(_:)), for: .touchUpInside)
        }

        return button
    }

    private func updateTagsDisplay() {
        // Clear existing tags
        tagsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        if tags.isEmpty {
            let emptyLabel = UILabel()
            emptyLabel.text = "暂无标签"
            emptyLabel.font = UIFont.systemFont(ofSize: 14)
            emptyLabel.textColor = Colors.secondaryText.dynamicColor()
            tagsStackView.addArrangedSubview(emptyLabel)
        } else {
            let tagsFlowLayout = createTagsFlowLayout(tags: tags, isPopular: false)
            tagsStackView.addArrangedSubview(tagsFlowLayout)
        }
    }

    @objc private func cancelTapped() {
        delegate?.tagsPickerDidCancel()
    }

    @objc private func confirmTapped() {
        delegate?.tagsPickerDidUpdateTags(tags)
    }

    @objc private func addTagTapped() {
        addCurrentTag()
    }

    @objc private func popularTagTapped(_ sender: UIButton) {
        guard let tag = sender.title(for: .normal), !tags.contains(tag) else { return }
        tags.append(tag)
        updateTagsDisplay()
    }

    @objc private func currentTagTapped(_ sender: UIButton) {
        guard let tag = sender.title(for: .normal) else { return }
        tags.removeAll { $0 == tag }
        updateTagsDisplay()
    }

    private func addCurrentTag() {
        guard let text = addTagTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines), !text.isEmpty,
                !tags.contains(text) else { return }

        tags.append(text)
        addTagTextField.text = ""
        updateTagsDisplay()
    }

    override func themeDidChange(interfaceStyle: UIUserInterfaceStyle) {
        super.themeDidChange(interfaceStyle: interfaceStyle)
        inputContainer.layer.borderColor = Colors.borderColor.dynamicColor().cgColor
    }
}

// MARK: - UITextFieldDelegate
extension TagsPickerView: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        addCurrentTag()
        return true
    }
}
