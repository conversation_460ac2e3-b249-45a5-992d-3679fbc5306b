//
//  Untitled.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import UIKit

class JournalNewRouter {

    weak var viewController: UIViewController?

    init(viewController: UIViewController?) {
        self.viewController = viewController
    }

    func navigateBack() {
        // Dismiss the view controller or pop from navigation stack
        if let navigationController = viewController?.navigationController {
            navigationController.dismiss(animated: true)
        }
    }

    func showErrorAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: XYLocalize.XYLocalize("ok"), style: .default))
        viewController?.present(alert, animated: true)
    }

    func showRecording(_ recordViewController: RecordViewController) {
        viewController?.navigationController?.present(recordViewController, animated: true)
    }
}
