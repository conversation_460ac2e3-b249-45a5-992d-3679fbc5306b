import ObjectiveC
import SnapKit
import UIKit

class JournalItemContainerView: UIView {
    weak var journalItem: JournalContentItem?
}

// MARK: - UI Setup
extension JournalNewViewController {
    func setupUI() {
        configureNavigationBar()
        setupToolbar()
        setupHeaderView()
        setupScrollView()
        initializeContent()
    }

    private func configureNavigationBar() {
        view.backgroundColor = Colors.pageBg.dynamicColor()
        showCloseBackButton = true
        title = ""

        // Set the right bar button item
        navigationItem.rightBarButtonItem = saveBarButton()
    }

    private func initializeContent() {
        tableData = [
            JournalContentItem.title(text: ""),
            JournalContentItem.content(text: "")
        ]
        updateContentInScrollView()
        updateDate()
        view.bringSubviewToFront(fixedToolbarView)
    }
}

// MARK: - ScrollView Setup
extension JournalNewViewController {
    func setupScrollView() {
        configureScrollView()
        setupScrollViewConstraints()
    }

    private func configureScrollView() {
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = true
        scrollView.keyboardDismissMode = .onDrag
        scrollView.delegate = self

        // Configure content stack view
        contentStackView.axis = .vertical
        contentStackView.spacing = 0
        contentStackView.alignment = .fill
        contentStackView.distribution = .fill
    }

    private func setupScrollViewConstraints() {
        view.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(fixedToolbarView.snp.top)
        }

        scrollView.addSubview(headerView)
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        scrollView.addSubview(contentStackView)
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.bottom.equalToSuperview()
            make.left.right.equalToSuperview().inset(
                UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0))
            make.width.equalTo(scrollView.frameLayoutGuide)
        }
    }

    // Update content in scroll view based on tableData
    func updateContentInScrollView() {
        // Remove all existing content views
        contentStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Add views based on tableData
        for item in tableData {
            var contentView: UIView

            switch item.type {
            case .title:
                contentView = createTitleView(item: item)

            case .content:
                contentView = createContentView(item: item)

            case .image:
                guard let url = item.url else { continue }
                contentView = createMediaView(
                    item: item,
                    mediaType: .image(
                        url: url, mode: item.widthMode, size: item.size
                    )
                )

            case .video:
                guard let url = item.url else { continue }
                contentView = createMediaView(
                    item: item,
                    mediaType: .video(
                        url: url, thumbnail: item.thumbnail, mode: item.widthMode, size: item.size
                    )
                )

            case .audio:
                guard let url = item.url else { continue }
                contentView = createAudioView(item: item, url: url, duration: item.duration)
            }

            // Store a reference back to the content item
            item.associatedView = contentView

            contentStackView.addArrangedSubview(contentView)
        }

        // Add location and tags footer
        addLocationAndTagsFooter()
    }

    private func addLocationAndTagsFooter() {
        let footerView = createLocationAndTagsFooterView()
        footerView.accessibilityIdentifier = "LocationTagsFooter"
        contentStackView.addArrangedSubview(footerView)

        tagsLocationView = footerView
    }

    private func createLocationAndTagsFooterView() -> UIView {
        let footerView = UIView()
        footerView.backgroundColor = .clear

        let containerView = UIView()
        containerView.backgroundColor = Colors.cardBackground.dynamicColor()
        containerView.layer.cornerRadius = 12
        containerView.layer.masksToBounds = true

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill

        // Location section
        let locationContainer = createLocationDisplayView()
        stackView.addArrangedSubview(locationContainer)

        // Tags section
        let tagsContainer = createTagsDisplayView()
        stackView.addArrangedSubview(tagsContainer)

        containerView.addSubview(stackView)
        footerView.addSubview(containerView)

        // Constraints
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }

        return footerView
    }

    private func createLocationDisplayView() -> UIView {
        let locationView = UIView()

        let iconLabel = UILabel()
        iconLabel.text = "📍"
        iconLabel.font = UIFont.systemFont(ofSize: 16)

        let locationLabel = UILabel()
        locationLabel.text = selectedLocation.isEmpty ? "添加位置" : selectedLocation
        locationLabel.font = UIFont.systemFont(ofSize: 14)
        locationLabel.textColor = selectedLocation.isEmpty ? Colors.secondaryText.dynamicColor() : Colors.text.dynamicColor()
        locationLabel.numberOfLines = 0

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(locationDisplayTapped))
        locationView.addGestureRecognizer(tapGesture)
        locationView.isUserInteractionEnabled = true

        locationView.addSubview(iconLabel)
        locationView.addSubview(locationLabel)

        iconLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.width.equalTo(20)
        }

        locationLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconLabel.snp.trailing).offset(8)
            make.trailing.top.bottom.equalToSuperview()
            make.height.greaterThanOrEqualTo(20)
        }

        return locationView
    }

    private func createTagsDisplayView() -> UIView {
        let tagsView = UIView()

        let iconLabel = UILabel()
        iconLabel.text = "🏷️"
        iconLabel.font = UIFont.systemFont(ofSize: 16)

        let tagsLabel = UILabel()
        tagsLabel.text = selectedTags.isEmpty ? "添加标签" : selectedTags.joined(separator: ", ")
        tagsLabel.font = UIFont.systemFont(ofSize: 14)
        tagsLabel.textColor = selectedTags.isEmpty ? Colors.secondaryText.dynamicColor() : Colors.text.dynamicColor()
        tagsLabel.numberOfLines = 0

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(tagsDisplayTapped))
        tagsView.addGestureRecognizer(tapGesture)
        tagsView.isUserInteractionEnabled = true

        tagsView.addSubview(iconLabel)
        tagsView.addSubview(tagsLabel)

        iconLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.width.equalTo(20)
        }

        tagsLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconLabel.snp.trailing).offset(8)
            make.trailing.top.bottom.equalToSuperview()
            make.height.greaterThanOrEqualTo(20)
        }

        return tagsView
    }

    @objc private func locationDisplayTapped() {
        showLocationPicker()
    }

    @objc private func tagsDisplayTapped() {
        showTagsPicker()
    }

    func updateLocationAndTagsDisplay() {
        // Find and remove the existing footer view by checking for a specific identifier
        for view in contentStackView.arrangedSubviews where view.accessibilityIdentifier == "LocationTagsFooter" {
            view.removeFromSuperview()
            break
        }
        addLocationAndTagsFooter()
    }
}

// MARK: - Header View Setup
extension JournalNewViewController {
    func setupHeaderView() {
        headerView.backgroundColor = .clear
        setupDateContainer()
        setupMoodButton()
    }

    private func setupDateContainer() {
        // Update day label style
        dayLabel.font = UIFont.boldSystemFont(ofSize: 40)
        dayLabel.textColor = Colors.primary.dynamicColor()

        // Update month/year label style
        monthYearLabel.font = UIFont.systemFont(ofSize: 18)
        monthYearLabel.textColor = Colors.secondaryText.dynamicColor()

        dateContainer.addSubview(dayLabel)
        dayLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
        }

        dateContainer.addSubview(monthYearLabel)
        monthYearLabel.snp.makeConstraints { make in
            make.top.equalTo(dayLabel.snp.bottom).offset(4)
            make.left.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(0)
        }

        dateContainer.addSubview(dateArrowButton)
        dateArrowButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(dayLabel.snp.right).offset(8)
            make.width.height.equalTo(24)
        }

        headerView.addSubview(dateContainer)
        dateContainer.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.7)
            make.bottom.equalToSuperview().offset(-10)
        }

        // Add tap gesture to date container
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dateButtonTapped))
        dateContainer.addGestureRecognizer(tapGesture)
        dateContainer.isUserInteractionEnabled = true
    }

    private func setupMoodButton() {
        // Style the mood button
        moodButton.layer.cornerRadius = 25
        moodButton.clipsToBounds = true
        moodButton.backgroundColor = Colors.cardBackground.dynamicColor()
        moodButton.layer.borderWidth = 1
        moodButton.layer.borderColor = Colors.separator.dynamicColor().cgColor

        headerView.addSubview(moodButton)
        moodButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(50)
        }

        moodButton.addTarget(self, action: #selector(moodButtonTapped), for: .touchUpInside)
    }
}

// MARK: - Toolbar Setup
extension JournalNewViewController {
    func setupToolbar() {
        setupFixedToolbar()
        setupFloatingToolbar()
    }

    private func setupFixedToolbar() {
        view.addSubview(fixedToolbarView)
        fixedToolbarView.snp.makeConstraints { make in
            make.left.bottom.right.equalToSuperview()
        }

        fixedToolbarView.addSubview(optionContainerView)
        optionContainerView.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(0)
        }

        fixedFormattingToolbar.viewController = self
        fixedFormattingToolbar.onFormatSelected = { [weak self] option in
            self?.handleFormattingOption(option)
        }
        fixedFormattingToolbar.onOptionsView = { [weak self] view, dismissed in
            self?.handleOptionView(view, dismissed: dismissed)
        }

        fixedToolbarView.addSubview(fixedFormattingToolbar)
        fixedFormattingToolbar.snp.makeConstraints { make in
            make.leading.top.trailing.equalToSuperview()
            make.bottom.equalTo(optionContainerView.snp.top)
        }
    }

    private func setupFloatingToolbar() {
        floatingToolbar = FormattingToolbar()
        floatingToolbar?.translatesAutoresizingMaskIntoConstraints = false
        floatingToolbar?.viewController = self
        floatingToolbar?.onFormatSelected = { [weak self] option in
            self?.handleFormattingOption(option)
        }
        floatingToolbar?.onOptionsView = { [weak self] view, dismissed in
            self?.handleOptionView(view, dismissed: dismissed)
        }
    }

    private func createTitleView(item: JournalContentItem) -> UIView {
        let containerView = JournalItemContainerView()
        containerView.backgroundColor = .clear
        containerView.journalItem = item

        let textView = RSKPlaceholderTextView()
        textView.minimumHeight = 40
        textView.textColor = Colors.text.dynamicColor()
        textView.backgroundColor = .clear
        textView.contentInset = .zero
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0)
        textView.placeholder = "标题"
        textView.returnKeyType = .next
        textView.isScrollEnabled = false
        textView.delegate = self
        textView.inputAccessoryView = floatingToolbar

        if let viewModel = viewModel {
            let weight = viewModel.format.weight
            let isItalic = viewModel.format.isItalic

            textView.lineHeightMultiple = viewModel.format.lineHeightMultiple

            // Title should be 1.2x larger and one weight level bolder
            let titleFontSize = CGFloat(viewModel.format.fontSize) * 1.2
            let titleWeight = getBolderWeight(from: weight)

            if isItalic {
                textView.font = UIFont.italicSystemFont(ofSize: titleFontSize)
            } else {
                textView.font = UIFont.systemFont(ofSize: titleFontSize, weight: titleWeight.toUIFontWeight())
            }
            textView.textAlignment = viewModel.format.alignment
            textView.textColor = UIColor(hexString: viewModel.format.textColor)
            textView.isEditable = !viewModel.disableEditing
        }

        // Set attributed text first
        let attributedText = item.text.toAttributedString()
        textView.attributedText = attributedText

        // Force refresh placeholder to ensure visibility
        textView.refreshPlaceholder()

        containerView.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
        return containerView
    }

    // Helper function to get the next bolder weight
    func getBolderWeight(from weight: FontFormat.Weight) -> FontFormat.Weight {
        switch weight {
        case .thin:
            return .regular
        case .regular:
            return .bold
        case .bold:
            return .bold // Already at the boldest, stay bold
        }
    }

    private func createContentView(item: JournalContentItem) -> UIView {
        let containerView = JournalItemContainerView()
        containerView.backgroundColor = .clear
        containerView.journalItem = item

        let textView = RSKPlaceholderTextView()
        textView.textColor = Colors.text.dynamicColor()
        textView.backgroundColor = .clear
        textView.contentInset = .zero
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0)
        textView.placeholder = "这里写你的想法"
        textView.minimumHeight = 40
        textView.isScrollEnabled = false
        textView.delegate = self
        textView.inputAccessoryView = floatingToolbar

        let attributedText = item.text.toAttributedString()
        textView.attributedText = attributedText

        if let viewModel = viewModel {
            let weight = viewModel.format.weight
            let isItalic = viewModel.format.isItalic

            textView.lineHeightMultiple = viewModel.format.lineHeightMultiple

            if isItalic {
                textView.font = UIFont.italicSystemFont(ofSize: CGFloat(viewModel.format.fontSize))
            } else {
                textView.font = UIFont.systemFont(
                    ofSize: CGFloat(viewModel.format.fontSize), weight: weight.toUIFontWeight())
            }
            textView.textAlignment = viewModel.format.alignment
            textView.textColor = UIColor(hexString: viewModel.format.textColor)
            textView.isEditable = !viewModel.disableEditing
        }

        containerView.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }

        // Force refresh placeholder to ensure visibility
        textView.refreshPlaceholder()

        return containerView
    }

    private func createMediaView(item: JournalContentItem, mediaType: MediaContentView.MediaType)
    -> UIView {
        let containerView = JournalItemContainerView()
        containerView.backgroundColor = .clear
        containerView.journalItem = item

        let mediaView = MediaContentView(mediaType: mediaType, disableEdit: false, alignment: viewModel.format.alignment)
        mediaView.delegate = self

        containerView.addSubview(mediaView)
        mediaView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
        return containerView
    }

    private func createAudioView(item: JournalContentItem, url: URL, duration: TimeInterval)
    -> UIView {

        let containerView = JournalItemContainerView()
        containerView.backgroundColor = .clear
        containerView.journalItem = item

        let audioView = AudioPlayerView(audioURL: url, duration: duration)
        audioView.delegate = self

        containerView.addSubview(audioView)
        audioView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
        return containerView
    }

}
