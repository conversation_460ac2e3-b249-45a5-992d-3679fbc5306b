import ObjectiveC
import UIKit

// Associated objects for view identification
private struct AssociatedKeys {
    static var textViewKey = "textViewKey"
    static var contentTypeKey = "contentTypeKey"
    static var contentItemKey = "contentItemKey"
}

// MARK: - Save Handling
extension JournalNewViewController {
    func saveJournal() {
        XYLog("TableData at save:")
        for (index, item) in tableData.enumerated() {
            XYLog("[\(index)] \(item)")
        }

        let (titleItem, contentItems) = prepareContentItems()
        let contentSnippet = generateContentSnippet(from: contentItems)

        // Debug: print what we're saving
        XYLog("Saving title: \(titleItem.content)")
        XYLog("Saving \(contentItems.count) content items")
        XYLog("Saving text alignment: \(viewModel.format.alignment.rawValue)")
        XYLog("Saving font size: \(viewModel.format.fontSize)")
        XYLog("Saving line height: \(viewModel.format.lineHeightMultiple)")
        XYLog("Saving text color: \(viewModel.format.textColor)")

        let success = viewModel.saveJournalWithContentItems(
            title: titleItem,
            contentItems: contentItems,
            contentSnippet: contentSnippet,
            moodEmoji: selectedMood,
            weather: selectedWeather,
            tags: selectedTags,
            location: selectedLocation,
            fontSize: viewModel.format.fontSize,
            lineHeightMultiple: viewModel.format.lineHeightMultiple
        )

        handleSaveResult(success)
    }

    private func prepareContentItems() -> (ContentItem, [ContentItem]) {
        var orderedContentItems: [ContentItem] = []
        var titleItem = ContentItem(id: "", type: .text, content: "")

        // First, ensure all text views have their latest content captured
        updateAllTextItemsFromViews()

        // Process all items
        for item in tableData {
            switch item.type {
            case .title:
                if !item.text.isEmpty {
                    if let view = item.associatedView as? JournalItemContainerView,
                        let textView = view.subviews.first as? RSKPlaceholderTextView {
                        // Force update the item's text from the current text view content
                        let currentHTML = textView.toHTMLString() ?? ""
                        if !currentHTML.isEmpty {
                            item.text = currentHTML // Update the item's text
                            titleItem = ContentItem(
                                id: UUID().uuidString, type: .text, content: currentHTML)
                        } else if !item.text.isEmpty {
                            // Fallback to plain text if HTML conversion fails
                            titleItem = ContentItem(
                                id: UUID().uuidString, type: .text, content: item.text)
                        }
                    } else {
                        // No view available, use the item's text directly
                        titleItem = ContentItem(
                            id: UUID().uuidString, type: .text, content: item.text)
                    }
                }

            case .content:
                if !item.text.isEmpty {
                    if let view = item.associatedView as? JournalItemContainerView,
                        let textView = view.subviews.first as? RSKPlaceholderTextView {
                        // Force update the item's text from the current text view content
                        let currentHTML = textView.toHTMLString() ?? ""
                        if !currentHTML.isEmpty {
                            item.text = currentHTML // Update the item's text
                            orderedContentItems.append(
                                ContentItem(id: UUID().uuidString, type: .text, content: currentHTML))
                        } else {
                            // Fallback to plain text
                            orderedContentItems.append(
                                ContentItem(id: UUID().uuidString, type: .text, content: item.text))
                        }
                    } else {
                        // No view available, use the item's text directly
                        orderedContentItems.append(
                            ContentItem(id: UUID().uuidString, type: .text, content: item.text))
                    }
                }

            case .image:
                if let url = item.url {
                    orderedContentItems.append(
                        ContentItem(
                            id: UUID().uuidString, type: .image, content: url.absoluteString,
                            metadata: [
                                "mode": item.widthMode.rawValue,
                                "with": "\(item.size.width)",
                                "height": "\(item.size.height)"
                            ]
                        )
                    )
                }

            case .video:
                if let url = item.url {
                    orderedContentItems.append(
                        ContentItem(
                            id: UUID().uuidString, type: .video, content: url.absoluteString,
                            metadata: [
                                "mode": item.widthMode.rawValue,
                                "with": "\(item.size.width)",
                                "height": "\(item.size.height)"
                            ]
                        )
                    )
                }

            case .audio:
                if let url = item.url {
                    orderedContentItems.append(
                        ContentItem(
                            id: UUID().uuidString,
                            type: .audio,
                            content: url.absoluteString,
                            metadata: ["duration": String(item.duration)]
                        ))
                }
            }
        }

        // Debug print
        XYLog("Prepared title: \(titleItem.content)")
        XYLog("Prepared \(orderedContentItems.count) content items")

        return (titleItem, orderedContentItems)
    }

    private func generateContentSnippet(from contentItems: [ContentItem]) -> String {
        let allTextContent =
            contentItems
            .filter { $0.type == .text }
            .map { $0.content }
            .joined(separator: "\n\n")

        return TextFormatter.generateContentSnippet(from: allTextContent, maxLength: 100)
    }

    private func handleSaveResult(_ success: Bool) {
        if success {
            router.navigateBack()
        } else {
            router.showErrorAlert(title: "保存失败", message: "无法保存日记，请稍后再试")
        }
    }

    // Force update all text items from their associated text views
    private func updateAllTextItemsFromViews() {
        for item in tableData {
            if item.type == .title || item.type == .content {
                if let view = item.associatedView as? JournalItemContainerView,
                   let textView = view.subviews.first as? RSKPlaceholderTextView {
                    // Force the text view to update its content
                    if let currentHTML = textView.toHTMLString(), !currentHTML.isEmpty {
                        item.text = currentHTML
                    }
                }
            }
        }
    }
}

// MARK: - Data Management
extension JournalNewViewController {

    func isContentType(_ item: JournalContentItem) -> Bool {
        if case .content = item.type {
            return true
        }
        return false
    }
}
