import AVFoundation
import PhotosUI
import UIKit

// MARK: - Media Handling
extension JournalNewViewController {
    func addImageToTable(url: URL) {
        insertMediaItem(JournalContentItem.image(url: url))
    }

    func addVideoToTable(url: URL, thumbnail: UIImage?) {
        insertMediaItem(JournalContentItem.video(url: url, thumbnail: thumbnail))
    }

    func addAudioToTable(url: URL, duration: TimeInterval) {
        insertMediaItem(JournalContentItem.audio(url: url, duration: duration))
    }

    private func insertMediaItem(_ item: JournalContentItem) {
        let insertIndex = calculateInsertIndex()
        tableData.insert(item, at: insertIndex)
        tableData.insert(JournalContentItem.content(text: ""), at: insertIndex + 1)
        updateContentInScrollView()
        mergeAdjacentTextItems()
        scrollToNewlyAddedItem(item)
    }

    private func calculateInsertIndex() -> Int {
        if let focusedTextView = currentlyFocusedTextView {
            // Find the container view that contains the textView
            var containerView: UIView? = focusedTextView.superview
            while containerView != nil && containerView?.superview != contentStackView {
                containerView = containerView?.superview
            }

            if let containerView = containerView {
                // Find the index of the container view in the stack view
                if let index = contentStackView.arrangedSubviews.firstIndex(of: containerView) {
                    return index + 1
                }
            }
        }
        return tableData.count
    }

    private func scrollToNewlyAddedItem(_ item: JournalContentItem) {
        if let index = tableData.firstIndex(where: { $0.isSameItem(as: item) }) {
            // Find the corresponding view in the stack view
            if index < contentStackView.arrangedSubviews.count {
                let view = contentStackView.arrangedSubviews[index]
                scrollView.scrollRectToVisible(view.frame, animated: true)
            }
        }
    }

    func presentCamera() {
        let imagePicker = UIImagePickerController()
        imagePicker.sourceType = .camera
        imagePicker.delegate = self
        present(imagePicker, animated: true)
    }

    func presentVideoRecorder() {
        let imagePicker = UIImagePickerController()
        imagePicker.sourceType = .camera
        imagePicker.mediaTypes = ["public.movie"]
        imagePicker.delegate = self
        present(imagePicker, animated: true)
    }

    func presentPhotoPicker() {
        var configuration = PHPickerConfiguration()
        configuration.filter = .any(of: [.images, .videos])
        configuration.selectionLimit = 5

        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = self
        present(picker, animated: true)
    }

    func saveImageToLocal(_ image: UIImage) -> URL? {
        do {
            return try SharedFileManager.shared.saveImageToShared(image)
        } catch {
            XYLog("Error saving image: \(error)")
            return nil
        }
    }

    func mergeAdjacentTextItems() {
        guard tableData.count > 1 else { return }
        var newTableData: [JournalContentItem] = []
        var index = 0
        while index < tableData.count {
            if index == 0 {
                // Always keep the title as is
                newTableData.append(tableData[index])
                index += 1
            } else if case .content = tableData[index].type, index + 1 < tableData.count,
                case .content = tableData[index + 1].type {
                // Merge with next content
                let text1 = tableData[index].text
                let text2 = tableData[index+1].text
                let mergedText = text1 + "\n" + text2
                newTableData.append(.content(text: mergedText))
                index += 2 // Skip the next one, as it's merged
            } else {
                newTableData.append(tableData[index])
                index += 1
            }
        }
        tableData = newTableData
    }
}
