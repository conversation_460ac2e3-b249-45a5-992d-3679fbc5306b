//
//  JournalListRouter.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import UIKit

class JournalListRouter {
    weak var viewController: JournalListViewController?

    init(viewController: JournalListViewController) {
        self.viewController = viewController
    }

    func navigateToJournalDetail(with journalId: String) {
        let detailVC = JournalDetailViewConfigurator.configureViewController(journalId: journalId)
        viewController?.navigationController?.pushViewController(detailVC, animated: true)
    }

    func navigateToCalendarView() {
        let calendarVC = JournalCalendarViewController()
        calendarVC.viewModel = JournalCalendarViewModel()
        calendarVC.router = JournalCalendarRouter(viewController: calendarVC)
        viewController?.navigationController?.pushViewController(calendarVC, animated: true)
    }

    func navigateToNewJournal() {
        let alert = UIAlertController(title: "新建日记", message: "即将创建新日记", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        viewController?.present(alert, animated: true)
    }
}
