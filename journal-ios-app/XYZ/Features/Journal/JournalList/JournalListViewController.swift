//
//  JournalListViewController.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import Combine
import SnapKit
import UIKit

class JournalListViewController: BaseViewController {

    var viewModel: JournalListViewModel!
    var router: JournalListRouter!
    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI Elements
    private let searchBar: UISearchBar = {
        let searchBar = UISearchBar()
        searchBar.placeholder = "搜索日记..."
        searchBar.searchBarStyle = .minimal
        searchBar.backgroundImage = UIImage()
        return searchBar
    }()

    private let filterScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        return scrollView
    }()

    private let filterStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 10
        stackView.distribution = .fillProportionally
        return stackView
    }()

    private let tableView: UITableView = {
        let tableView = UITableView()
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        tableView.showsVerticalScrollIndicator = false
        tableView.keyboardDismissMode = .interactive
        tableView.register(JournalEntryCell.self, forCellReuseIdentifier: "JournalEntryCell")
        return tableView
    }()

    private let emptyStateView: UIView = {
        let view = UIView()
        view.isHidden = true
        return view
    }()

    private let emptyStateLabel: UILabel = {
        let label = UILabel()
        label.text = "暂无日记记录"
        label.textAlignment = .center
        label.textColor = Theme.Colors.secondaryText.dynamicColor()
        label.font = UIFont.systemFont(ofSize: 16)
        return label
    }()

    private let loadingView: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.hidesWhenStopped = true
        return indicator
    }()

    // MARK: - Lifecycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        setupActions()

        viewModel.loadJournals()
    }

    // MARK: - Setup Methods
    private func setupUI() {
        // Add search bar
        view.addSubview(searchBar)
        searchBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        // Add filter scroll view
        view.addSubview(filterScrollView)
        filterScrollView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
            make.height.equalTo(40)
        }

        filterScrollView.addSubview(filterStackView)
        filterStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(
                UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16))
            make.height.equalToSuperview()
        }

        // Add filter tags
        addFilterTags()

        // Add table view
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(filterScrollView.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }

        // Add empty state view
        view.addSubview(emptyStateView)
        emptyStateView.snp.makeConstraints { make in
            make.center.equalTo(tableView)
            make.width.equalToSuperview().multipliedBy(0.8)
        }

        emptyStateView.addSubview(emptyStateLabel)
        emptyStateLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Add loading view
        view.addSubview(loadingView)
        loadingView.snp.makeConstraints { make in
            make.center.equalTo(tableView)
        }

        // Setup table view
        tableView.dataSource = self
        tableView.delegate = self
    }

    private func addFilterTags() {
        let filterTags = ["全部", "本周", "本月", "😊 开心", "☀️ 晴天"]

        for tag in filterTags {
            let tagButton = createFilterTagButton(title: tag)
            filterStackView.addArrangedSubview(tagButton)
        }
    }

    private func createFilterTagButton(title: String) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.setTitleColor(Theme.Colors.text.dynamicColor(), for: .normal)
        button.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.layer.cornerRadius = 15
        button.contentEdgeInsets = UIEdgeInsets(top: 5, left: 12, bottom: 5, right: 12)
        return button
    }

    private func setupBindings() {
        viewModel.$journalEntries
            .receive(on: DispatchQueue.main)
            .sink { [weak self] entries in
                self?.tableView.reloadData()
                self?.emptyStateView.isHidden = !entries.isEmpty
            }
            .store(in: &cancellables)

        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                if isLoading {
                    self?.loadingView.startAnimating()
                } else {
                    self?.loadingView.stopAnimating()
                }
            }
            .store(in: &cancellables)
    }

    private func setupActions() {
        searchBar.delegate = self
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "错误", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension JournalListViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.journalEntries.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard
            let cell = tableView.dequeueReusableCell(
                withIdentifier: "JournalEntryCell", for: indexPath)
                as? JournalEntryCell
        else {
            return UITableViewCell()
        }

        let entry = viewModel.journalEntries[indexPath.row]
        cell.configure(with: entry)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension JournalListViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 100
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let entry = viewModel.journalEntries[indexPath.row]
        router.navigateToJournalDetail(with: entry.id)
    }
}

// MARK: - UISearchBarDelegate
extension JournalListViewController: UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
        viewModel.searchJournalEntries(with: searchText)
    }

    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
    }
}
