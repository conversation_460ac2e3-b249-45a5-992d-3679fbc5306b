//
//  JournalListViewModel.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import Combine
import Foundation

class JournalListViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var journalEntries: [JournalEntry] = []
    @Published var isLoading: Bool = false
    @Published var errorWrapper: ErrorWrapper?

    // MARK: - Private Properties
    private let journalRepository: JournalRepositoryProtocol

    // MARK: - Initialization
    init(journalRepository: JournalRepositoryProtocol = JournalRepository()) {
        self.journalRepository = journalRepository
    }

    // MARK: - Public Methods
    func loadJournals() {
        isLoading = true
        Task {
            do {
                let journalEntries = try await journalRepository.searchJournalEntries("")
                self.journalEntries = journalEntries
                self.isLoading = false
            } catch {
                self.errorWrapper = ErrorWrapper(error: error, message: "Please try again later")
                self.isLoading = false
            }
        }
    }

    func searchJournalEntries(with: String) {
        isLoading = true

        Task {
            do {
                let journalEntries = try await journalRepository.searchJournalEntries(with)
                self.journalEntries = journalEntries
                self.isLoading = false
            } catch {
                self.errorWrapper = ErrorWrapper(error: error, message: "Please try again later")
                self.isLoading = false
            }
        }
    }
}
