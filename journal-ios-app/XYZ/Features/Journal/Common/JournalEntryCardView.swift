import SnapKit
import UIKit
import SwifterSwift

protocol JournalEntryCardDelegate: AnyObject {
    func didTapJournalEntryCard(_ entry: JournalEntry)
}

class JournalEntryCardView: UIView {
    // MARK: - Properties
    private var journalEntry: JournalEntry?
    weak var delegate: JournalEntryCardDelegate?

    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(red: 0.15, green: 0.25, blue: 0.45, alpha: 0.9)
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.3).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        view.layer.shadowOpacity = 1
        return view
    }()

    private let dateLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = UIColor.white.withAlphaComponent(0.7)
        return label
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textColor = UIColor.white
        label.numberOfLines = 2
        return label
    }()

    private let moodEmojiLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 32)
        return label
    }()

    private let contentPreviewLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor.white.withAlphaComponent(0.8)
        label.numberOfLines = 3
        return label
    }()

    private let arrowIndicator: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = UIColor.white.withAlphaComponent(0.6)
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupGestureRecognizer()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupGestureRecognizer()
    }

    // MARK: - Setup
    private func setupUI() {
        addSubview(containerView)

        containerView.addSubview(dateLabel)
        containerView.addSubview(titleLabel)
        containerView.addSubview(moodEmojiLabel)
        containerView.addSubview(contentPreviewLabel)
        containerView.addSubview(arrowIndicator)

        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        dateLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(20)
            make.trailing.equalTo(moodEmojiLabel.snp.leading).offset(-8)
        }

        moodEmojiLabel.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview().inset(20)
            make.width.height.equalTo(40)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(dateLabel.snp.bottom).offset(12)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
        }

        contentPreviewLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.bottom.equalToSuperview().offset(-20)
        }

        arrowIndicator.snp.makeConstraints { make in
            make.centerY.equalTo(dateLabel)
            make.trailing.equalTo(moodEmojiLabel.snp.leading).offset(-8)
            make.width.height.equalTo(16)
        }
    }

    private func setupGestureRecognizer() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(cardTapped))
        containerView.addGestureRecognizer(tapGesture)
        containerView.isUserInteractionEnabled = true
    }

    // MARK: - Actions
    @objc private func cardTapped() {
        if let entry = journalEntry {
            delegate?.didTapJournalEntryCard(entry)
        }
    }

    // MARK: - Configuration
    func configure(with entry: JournalEntry) {
        self.journalEntry = entry

        // Format date
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        dateLabel.text = dateFormatter.string(from: entry.date)

        // Set title or default text
        titleLabel.text = entry.title.content.isEmpty ? entry.date.dateWithWeek() : entry.title.content

        // Set mood emoji
        moodEmojiLabel.text = entry.moodEmoji

        // Set content preview
        contentPreviewLabel.attributedText = entry.content.resetColorAccordingToTheme()
    }

    // Add a gentle animation when tapped
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        animateCardPress(isPressed: true)
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesEnded(touches, with: event)
        animateCardPress(isPressed: false)
    }

    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesCancelled(touches, with: event)
        animateCardPress(isPressed: false)
    }

    private func animateCardPress(isPressed: Bool) {
        UIView.animate(withDuration: 0.1) {
            self.containerView.transform =
            isPressed ? CGAffineTransform(scaleX: 0.98, y: 0.98) : .identity
            self.containerView.alpha = isPressed ? 0.9 : 1.0
        }
    }
}
