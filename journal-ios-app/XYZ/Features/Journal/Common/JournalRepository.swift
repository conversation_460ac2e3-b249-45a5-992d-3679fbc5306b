//
//  JournalRepository.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/28.
//

import Alamofire

protocol JournalRepositoryProtocol {
    // List operations
    func searchJournalEntries(_ with: String) async throws -> [JournalEntry]
    func fetchJournal(byMonth: String) async throws -> [JournalEntry]
    func fetchJournal(byId: String) async throws -> JournalEntry
    func deleteJournal(byId: String) async throws -> Alamofire.Empty

    // CRUD operations
    func saveJournal(_ journal: JournalEntry) -> Bool
}

class JournalRepository: JournalRepositoryProtocol {
    // MARK: - List operations
    func searchJournalEntries(_ with: String) async throws -> [JournalEntry] {
        let journalEntries: [JournalEntry] = try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: "/journal/search",
                    parameters: [
                        "query": with
                    ],
                    method: .get
                )
        )
        return journalEntries
    }

    func fetchJournal(byId: String) async throws -> JournalEntry {
        let journalEntry: JournalEntry = try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: "/journal/:id".replacingOccurrences(
                        of: ":id", with: byId),
                    parameters: [:],
                    method: .get
                )
        )
        return journalEntry
    }

    func deleteJournal(byId: String) async throws -> Alamofire.Empty {
        return try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: "/journal/:id".replacingOccurrences(
                        of: ":id", with: byId),
                    parameters: [:],
                    method: .delete
                )
        )
    }

    func fetchJournal(byMonth: String) async throws -> [JournalEntry] {
        let journalEntries: [JournalEntry] = try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: "/journal/month",
                    parameters: [
                        "month": byMonth
                    ],
                    method: .get
                )
        )
        return journalEntries
    }

    // MARK: - CRUD operations
    func saveJournal(_ journal: JournalEntry) -> Bool {
        do {
            return try DB.shared.saveJournalEntry(journal)
        } catch {
            return false
        }
    }
}
