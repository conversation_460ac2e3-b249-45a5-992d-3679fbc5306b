//
//  JournalCalendarViewModel.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import Combine
import Foundation

class JournalCalendarViewModel: ObservableObject {
    @Published var currentMonthString: String = ""
    @Published var calendarDays: [CalendarDay] = []
    @Published var selectedDayEntries: [JournalEntry] = []
    @Published var isLoading: Bool = false
    @Published var error: Error?

    private var currentDate = Date()
    private var selectedDate = Date()
    private var allEntries: [JournalEntry] = []
    private var cancellables = Set<AnyCancellable>()
    private var repositry: JournalRepositoryProtocol

    struct CalendarDay {
        let day: Int
        let date: Date
        let isCurrentMonth: Bool
        let hasEntries: Bool
        let isSelected: Bool
    }

    init(repositry: JournalRepositoryProtocol = JournalRepository()) {
        self.repositry = repositry
    }

    func setupCalendar() {
        isLoading = true

        fetchJournalEntries()
    }

    func fetchJournalEntries() {

        self.updateCalendarDays()
        Task {
            do {
                self.allEntries = try await self.repositry.fetchJournal(byMonth: currentDate.dateYearAndMonth())
                self.allEntries = self.createMockJournalEntries()
                self.updateSelectedDayEntries()
                self.isLoading = false
            } catch {
                self.error = error
            }
        }
    }

    func goToPreviousMonth() {
        let calendar = Calendar.current
        if let newDate = calendar.date(byAdding: .month, value: -1, to: currentDate) {
            currentDate = newDate
            fetchJournalEntries()
        }
    }

    func goToNextMonth() {
        let calendar = Calendar.current
        if let newDate = calendar.date(byAdding: .month, value: 1, to: currentDate) {
            currentDate = newDate
            fetchJournalEntries()
        }
    }

    func selectDay(at index: Int) {
        guard index >= 0 && index < calendarDays.count else { return }

        let selectedDay = calendarDays[index]
        if selectedDay.isCurrentMonth {
            selectedDate = selectedDay.date
            updateCalendarDays()
            updateSelectedDayEntries()
        }
    }
    // swiftlint:disable:next function_body_length
    private func updateCalendarDays() {
        let calendar = Calendar.current

        // 获取当前月的第一天
        var components = calendar.dateComponents([.year, .month], from: currentDate)
        components.day = 1
        guard let firstDayOfMonth = calendar.date(from: components) else { return }

        // 获取当前月的天数
        let range = calendar.range(of: .day, in: .month, for: firstDayOfMonth)!
        let numberOfDaysInMonth = range.count

        // 获取第一天是星期几（0是星期日，1是星期一，以此类推）
        let firstWeekday = calendar.component(.weekday, from: firstDayOfMonth)

        // 计算上个月需要显示的天数
        let numberOfDaysFromPreviousMonth = firstWeekday - 1

        // 获取上个月的最后一天
        guard
            let lastDayOfPreviousMonth = calendar.date(
                byAdding: .day, value: -1, to: firstDayOfMonth)
        else { return }

        // 获取上个月的天数
        let previousMonthRange = calendar.range(of: .day, in: .month, for: lastDayOfPreviousMonth)!
        let numberOfDaysInPreviousMonth = previousMonthRange.count

        var days: [CalendarDay] = []

        // 添加上个月的日期
        for index in 0..<numberOfDaysFromPreviousMonth {
            let day = numberOfDaysInPreviousMonth - numberOfDaysFromPreviousMonth + index + 1
            if let date = calendar.date(
                byAdding: .day, value: -numberOfDaysFromPreviousMonth + index, to: firstDayOfMonth) {
                let hasEntries = hasEntriesForDate(date)
                let isSelected = calendar.isDate(date, inSameDayAs: selectedDate)
                days.append(
                    CalendarDay(
                        day: day, date: date, isCurrentMonth: false, hasEntries: hasEntries,
                        isSelected: isSelected))
            }
        }

        // 添加当前月的日期
        for day in 1...numberOfDaysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstDayOfMonth) {
                let hasEntries = hasEntriesForDate(date)
                let isSelected = calendar.isDate(date, inSameDayAs: selectedDate)
                days.append(
                    CalendarDay(
                        day: day, date: date, isCurrentMonth: true, hasEntries: hasEntries,
                        isSelected: isSelected))
            }
        }

        // 计算下个月需要显示的天数（总共显示42天，即6周）
        let numberOfDaysFromNextMonth = 42 - days.count

        // 添加下个月的日期
        guard
            let firstDayOfNextMonth = calendar.date(byAdding: .month, value: 1, to: firstDayOfMonth)
        else { return }

        for day in 1...numberOfDaysFromNextMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstDayOfNextMonth) {
                let hasEntries = hasEntriesForDate(date)
                let isSelected = calendar.isDate(date, inSameDayAs: selectedDate)
                days.append(
                    CalendarDay(
                        day: day, date: date, isCurrentMonth: false, hasEntries: hasEntries,
                        isSelected: isSelected))
            }
        }

        // 更新月份标题
        let dateFormatter = DateFormatter()
        dateFormatter.locale = Locale(identifier: "zh_CN")
        dateFormatter.dateFormat = "yyyy年 M月"
        currentMonthString = dateFormatter.string(from: currentDate)

        // 更新日历天数
        calendarDays = days
    }

    private func updateSelectedDayEntries() {
        let calendar = Calendar.current
        selectedDayEntries = allEntries.filter {
            calendar.isDate($0.date, inSameDayAs: selectedDate)
        }
    }

    private func hasEntriesForDate(_ date: Date) -> Bool {
        let calendar = Calendar.current
        return allEntries.contains { calendar.isDate($0.date, inSameDayAs: date) }
    }

    // 创建模拟数据
    private func createMockJournalEntries() -> [JournalEntry] {
        let calendar = Calendar.current
        let today = Date()

        // 创建过去30天的日记条目
        var entries: [JournalEntry] = []

        for index in 0..<30 {
            // 只为某些日期创建条目，以模拟真实情况
            if index % 3 == 0 {  // 每隔3天创建一个条目
                if calendar.date(byAdding: .day, value: -index, to: today) != nil {
                    // 随机选择心情和天气
                    let moods = ["😊", "😌", "😢", "😡", "😲"]
                    let weathers = ["☀️", "⛅", "☁️", "🌧️", "❄️"]

                    let randomMood = moods[Int.random(in: 0..<moods.count)]
                    let randomWeather = weathers[Int.random(in: 0..<weathers.count)]
                    let moodEmoji = "\(randomMood)"

                    // 创建随机内容
                    let contents = [
                        "今天是个阳光明媚的日子，我决定去公园散步，感受春天的气息。看到了盛开的樱花、嬉戏的小孩、遛狗的老人。真是美好的一天。",
                        "今天完成了一个重要的项目，感觉非常满足。下午和朋友一起去了咖啡厅，聊了很多有趣的话题。",
                        "今天下雨了，心情有点低落。在家看了一部电影，思考了很多事情。希望明天会好起来。",
                        "今天是个阴晴不定的日子，心情还算平静。早上起来，决定去公园散步，感受春天的气息。",
                        "今天参加了一个有趣的活动，认识了很多新朋友。晚上回家的路上，看到了美丽的夕阳。"
                    ]

                    let randomContent = contents[Int.random(in: 0..<contents.count)]
                    let title = "日记 \(index + 1)"
                    let snippet =
                        String(randomContent.prefix(50)) + (randomContent.count > 50 ? "..." : "")

                    // 创建日记条目
                    let entry = JournalEntry(
                        id: "entry_\(index)",
                        title: ContentItem(id: "", type: .text, content: title),
                        contentItems: [
                            ContentItem(
                                id: UUID().uuidString, type: .text, content: "Hiee"
                            )
                        ],
                        contentSnippet: snippet,
                        moodEmoji: moodEmoji,
                        weather: randomWeather,
                        tags: [],
                        location: "",
                        fontSize: 16,
                        lineHeightMultiple: 1.3
                    )

                    entries.append(entry)
                }
            }
        }

        // 按日期降序排序
        return entries.sorted { $0.date > $1.date }
    }
}
