//
//  JournalCalendarViewController.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import Combine
import SnapKit
import UIKit

class JournalCalendarViewController: BaseViewController {

    var viewModel: JournalCalendarViewModel!
    var router: JournalCalendarRouter!
    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI Elements

    private let calendarView: CalendarView = {
        let view = CalendarView()
        view.backgroundColor = .clear
        return view
    }()

    // Calendar UI elements are now handled by the CalendarView class

    private let entriesTableView: UITableView = {
        let tableView = UITableView()
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        tableView.keyboardDismissMode = .onDrag
        tableView.showsVerticalScrollIndicator = false
        tableView.register(JournalEntryCell.self, forCellReuseIdentifier: "JournalEntryCell")
        return tableView
    }()

    private let loadingView: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.hidesWhenStopped = true
        return indicator
    }()

    private var lastContentOffset: CGFloat = 0
    private var isCalendarExpanded = true
    private let calendarExpandedHeight: CGFloat = 240
    private let calendarCollapsedHeight: CGFloat = 60

    // MARK: - Lifecycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()

        viewModel.setupCalendar()
        entriesTableView.setBasicContentInsets()
        entriesTableView.delegate = self
        entriesTableView.dataSource = self
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.prefersLargeTitles = true
    }

    // MARK: - Setup Methods
    private func setupUI() {
        // Add calendar view - now using the CalendarView class
        view.addSubview(calendarView)
        calendarView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(8)
            make.left.right.equalToSuperview().inset(16)
        }

        // Add entries table view
        view.addSubview(entriesTableView)
        entriesTableView.snp.makeConstraints { make in
            make.top.equalTo(calendarView.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalTo(view.snp.bottom)
        }

        // Add loading view
        view.addSubview(loadingView)
        loadingView.snp.makeConstraints { make in
            make.center.equalTo(entriesTableView)
        }

        // Setup table view
        entriesTableView.dataSource = self
        entriesTableView.delegate = self
    }

    // Calendar setup methods are now handled by the CalendarView class

    private func setupBindings() {
        // Set calendar view delegate
        calendarView.delegate = self

        viewModel.$currentMonthString
            .receive(on: DispatchQueue.main)
            .sink { [weak self] monthString in
                self?.calendarView.setMonthYearText(monthString)
            }
            .store(in: &cancellables)

        viewModel.$calendarDays
            .receive(on: DispatchQueue.main)
            .sink { [weak self] days in
                self?.calendarView.updateCalendarView(with: days)
            }
            .store(in: &cancellables)

        viewModel.$error
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.error = error
            }
            .store(in: &cancellables)

        viewModel.$selectedDayEntries
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.entriesTableView.reloadData()
            }
            .store(in: &cancellables)

        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                if isLoading {
                    self?.loadingView.startAnimating()
                } else {
                    self?.loadingView.stopAnimating()
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - UITableViewDataSource
extension JournalCalendarViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.selectedDayEntries.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard
            let cell = tableView.dequeueReusableCell(
                withIdentifier: "JournalEntryCell", for: indexPath)
                as? JournalEntryCell
        else {
            return UITableViewCell()
        }

        let entry = viewModel.selectedDayEntries[indexPath.row]
        cell.configure(with: entry)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension JournalCalendarViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        if viewModel.selectedDayEntries.isEmpty {
            return 0
        }
        return 30
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        if viewModel.selectedDayEntries.isEmpty {
            return nil
        }

        let headerView = UIView()
        headerView.backgroundColor = Colors.pageBg.dynamicColor()
        let label = UILabel()
        label.text = "共\(viewModel.selectedDayEntries.count)篇日记"
        label.textColor = Colors.textPrimary.dynamicColor()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        headerView.addSubview(label)
        label.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(16)
        }
        return headerView
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let entry = viewModel.selectedDayEntries[indexPath.row]
        router.navigateToJournalDetail(with: entry.id)
    }

    func scrollViewDidScroll(_ scrollView: UIScrollView) {

        let currentOffset = scrollView.contentOffset.y
        let scrollDiff = currentOffset - lastContentOffset

        // Determine scroll direction
        let isScrollingDown = scrollDiff > 0
        let isScrollingUp = scrollDiff < 0

        // Collapse calendar when scrolling down
        if isScrollingDown && isCalendarExpanded && currentOffset >= 60 {
            isCalendarExpanded = false
            calendarView.collapseToSelectedWeek(animated: false)
        } else if !isCalendarExpanded && isScrollingUp && currentOffset < 60 {
            isCalendarExpanded = true
            calendarView.expandToFullCalendar(animated: false)
        }
        lastContentOffset = currentOffset
    }
}

// MARK: - CalendarViewDelegate
extension JournalCalendarViewController: CalendarViewDelegate {
    func calendarView(_ calendarView: CalendarView, didSelectDayAt index: Int) {
        viewModel.selectDay(at: index)
    }

    func calendarView(_ calendarView: CalendarView, didChangeToPreviousMonth: Void) {
        viewModel.goToPreviousMonth()
    }

    func calendarView(_ calendarView: CalendarView, didChangeToNextMonth: Void) {
        viewModel.goToNextMonth()
    }
}
