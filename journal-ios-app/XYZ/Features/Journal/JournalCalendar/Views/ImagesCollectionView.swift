//
//  ImagesCollectionView.swift
//  XYZ
//
//  Created by <PERSON> on 2025/5/7.
//

import UIKit

class ImagesCollectionView: UIView, UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {

    static func calculateHeight(width: CGFloat, imagesCount: Int) -> CGFloat {
        if imagesCount <= 0 {
            return 0
        }

        var itemWithHeight = ceil((width - 8 * 2)/3.0)
        if imagesCount == 1 {
            itemWithHeight = ceil((width - 8 * 2)/2.0)
        }

        let lines = ceil(Double(imagesCount)/3.0)
        return itemWithHeight * lines + (lines - 1) * 8
    }

    private var images: [String] = []

    private let collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 8
        layout.minimumInteritemSpacing = 8

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.isScrollEnabled = false
        collectionView.register(ImageCell.self, forCellWithReuseIdentifier: "ImageCell")
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        return collectionView
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupCollectionView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCollectionView()
    }

    private func setupCollectionView() {
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.collectionViewLayout.invalidateLayout()
        addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    func configure(with images: [String]) {
        self.images = images
        collectionView.reloadData()
    }

    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
        -> Int {
        return images.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath)
        -> UICollectionViewCell {
        let cell =
            collectionView.dequeueReusableCell(withReuseIdentifier: "ImageCell", for: indexPath)
            as! ImageCell
        cell.configure(with: images[indexPath.row])
        return cell
    }

    func collectionView(
        _ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout,
        sizeForItemAt indexPath: IndexPath
    ) -> CGSize {
        let width = collectionView.frame.width
        var itemWithHeight = ceil((width - 8 * 2)/3.0)
        if self.images.count == 1 {
            itemWithHeight = ceil((width - 8 * 2)/2.0)
        }
        return CGSize(width: itemWithHeight, height: itemWithHeight)
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        XYLog("Selected item at index: \(indexPath.item)")
    }
}

// MARK: - Image Cell
class ImageCell: UICollectionViewCell {
    private let imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.masksToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        return imageView
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)

        contentView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func configure(with image: String) {
        if let url = URL(string: image) {
            let imageURL = MediaHandler.remapMediaURL(url: url)
            imageView.kf.setImage(with: imageURL)
        }
    }

    func configurePlaceholder() {
        imageView.image = UIImage(systemName: "photo")
        imageView.tintColor = Theme.Colors.secondaryText.dynamicColor()
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
    }
}
