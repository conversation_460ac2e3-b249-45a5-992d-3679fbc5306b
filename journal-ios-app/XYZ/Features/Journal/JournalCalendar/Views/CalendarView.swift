//
//  CalendarView.swift
//  XYZ
//
//  Created by Trae AI on 2025/4/25.
//

import Combine
import SnapKit
import UIKit

protocol CalendarViewDelegate: AnyObject {
	func calendarView(_ calendarView: CalendarView, didSelectDayAt index: Int)
	func calendarView(_ calendarView: CalendarView, didChangeToPreviousMonth: Void)
	func calendarView(_ calendarView: CalendarView, didChangeToNextMonth: Void)
}

class CalendarView: UIView {

	// MARK: - Properties
	weak var delegate: CalendarViewDelegate?
	private var cancellables = Set<AnyCancellable>()

	private var isCollapsed = false
	private var selectedWeekIndex = 0
	private var fullCalendarHeight: CGFloat = 0
	private var weekHeight: CGFloat = 0
	private var collapsedHeight: CGFloat = 40
	private var days: [JournalCalendarViewModel.CalendarDay] = []
    private var collapsedDays: [JournalCalendarViewModel.CalendarDay] = []

	// MARK: - UI Elements
	private let calendarHeaderView: UIView = {
		let view = UIView()
		view.backgroundColor = .clear
		return view
	}()

	private let previousMonthButton: UIButton = {
		let button = UIButton(type: .system)
		button.setImage(
			UIImage(systemName: "chevron.left")?.withTintColor(
				Colors.themeColor.dynamicColor()),
			for: .normal)
		return button
	}()

	private let nextMonthButton: UIButton = {
		let button = UIButton(type: .system)
		button.setImage(
			UIImage(systemName: "chevron.right")?.withTintColor(
				Colors.themeColor.dynamicColor()),
			for: .normal)
		return button
	}()

	private let monthYearLabel: UILabel = {
		let label = UILabel()
		label.font = UIFont.boldSystemFont(ofSize: 18)
		label.textColor = Colors.text.dynamicColor()
		label.textAlignment = .center
		return label
	}()

	private let weekdayStackView: UIStackView = {
		let stackView = UIStackView()
		stackView.axis = .horizontal
		stackView.distribution = .fillEqually
		stackView.spacing = 0
		return stackView
	}()

	private lazy var collectionView: UICollectionView = {
		let layout = UICollectionViewFlowLayout()
		layout.minimumLineSpacing = 0
		layout.minimumInteritemSpacing = 0

		let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
		collectionView.backgroundColor = .clear
		collectionView.delegate = self
		collectionView.dataSource = self
		collectionView.register(CalendarDayCell.self, forCellWithReuseIdentifier: "CalendarDayCell")
		collectionView.isScrollEnabled = false
		return collectionView
	}()

	// MARK: - Initialization
	override init(frame: CGRect) {
		super.init(frame: frame)
		setupUI()
		setupActions()
	}

	required init?(coder: NSCoder) {
		super.init(coder: coder)
		setupUI()
		setupActions()
	}

	// MARK: - Setup Methods
	private func setupUI() {
		// Add calendar header view
		addSubview(calendarHeaderView)
		calendarHeaderView.snp.makeConstraints { make in
			make.top.equalToSuperview().offset(8)
			make.left.right.equalToSuperview()
			make.height.equalTo(40)
		}

		calendarHeaderView.addSubview(previousMonthButton)
		previousMonthButton.snp.makeConstraints { make in
			make.left.centerY.equalToSuperview()
			make.width.height.equalTo(40)
		}

		calendarHeaderView.addSubview(nextMonthButton)
		nextMonthButton.snp.makeConstraints { make in
			make.right.centerY.equalToSuperview()
			make.width.height.equalTo(40)
		}

		calendarHeaderView.addSubview(monthYearLabel)
		monthYearLabel.snp.makeConstraints { make in
			make.center.equalToSuperview()
			make.left.greaterThanOrEqualTo(previousMonthButton.snp.right).offset(8)
			make.right.lessThanOrEqualTo(nextMonthButton.snp.left).offset(-8)
		}

		// Add weekday labels
		addSubview(weekdayStackView)
		weekdayStackView.snp.makeConstraints { make in
			make.top.equalTo(calendarHeaderView.snp.bottom).offset(8)
			make.left.right.equalToSuperview()
			make.height.equalTo(30)
		}

		setupWeekdayLabels()

		// Add collection view
		addSubview(collectionView)
		collectionView.snp.makeConstraints { make in
			make.top.equalTo(weekdayStackView.snp.bottom).offset(8)
			make.left.right.equalToSuperview()
			make.height.equalTo(240)
			make.bottom.equalToSuperview()
		}

		fullCalendarHeight = 40 + 8 + 30 + 8 + 240
		weekHeight = 240 / 6
	}

	private func setupWeekdayLabels() {
		let weekdays = ["日", "一", "二", "三", "四", "五", "六"]

		for weekday in weekdays {
			let label = UILabel()
			label.text = weekday
			label.textAlignment = .center
			label.font = UIFont.systemFont(ofSize: 14)
			label.textColor = Colors.secondaryText.dynamicColor()
			weekdayStackView.addArrangedSubview(label)
		}
	}

	private func setupActions() {
		previousMonthButton.addTarget(
			self, action: #selector(previousMonthTapped), for: .touchUpInside)
		nextMonthButton.addTarget(self, action: #selector(nextMonthTapped), for: .touchUpInside)
	}

	// MARK: - Public Methods
	func setMonthYearText(_ text: String) {
		monthYearLabel.text = text
	}

	func updateCalendarView(with days: [JournalCalendarViewModel.CalendarDay]) {
		self.days = days
        self.collapsedDays = days
        collectionView.reloadData()
	}

	func collapseToSelectedWeek(animated: Bool = true) {
		guard !isCollapsed else { return }

		isCollapsed = true

		let newHeight = collapsedHeight
		if animated {
			UIView.animate(withDuration: 0.3) {
				self.collectionView.snp.updateConstraints { make in
					make.height.equalTo(newHeight)
				}
				self.layoutIfNeeded()
			}
		} else {
			collectionView.snp.updateConstraints { make in
				make.height.equalTo(newHeight)
			}
			layoutIfNeeded()
		}
	}

	func expandToFullCalendar(animated: Bool = true) {
		guard isCollapsed else { return }

		isCollapsed = false

		if animated {
			UIView.animate(withDuration: 0.3) {
				self.collectionView.snp.updateConstraints { make in
					make.height.equalTo(240)
				}
				self.layoutIfNeeded()
			}
		} else {
			collectionView.snp.updateConstraints { make in
				make.height.equalTo(240)
			}
			layoutIfNeeded()
		}
	}

	// MARK: - Actions
	@objc private func previousMonthTapped() {
		delegate?.calendarView(self, didChangeToPreviousMonth: ())
	}

	@objc private func nextMonthTapped() {
		delegate?.calendarView(self, didChangeToNextMonth: ())
	}
}

// MARK: - UICollectionViewDataSource
extension CalendarView: UICollectionViewDataSource {
	func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
		-> Int {
            return self.collapsedDays.count
	}

	func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath)
		-> UICollectionViewCell {
		guard
			let cell = collectionView.dequeueReusableCell(
				withReuseIdentifier: "CalendarDayCell",
				for: indexPath) as? CalendarDayCell
		else {
			return UICollectionViewCell()
		}

		let day = self.collapsedDays[indexPath.item]
		cell.configure(with: day)
		return cell
	}
}

// MARK: - UICollectionViewDelegate
extension CalendarView: UICollectionViewDelegate {
	func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
		delegate?.calendarView(self, didSelectDayAt: indexPath.item)
	}
}

// MARK: - UICollectionViewDelegateFlowLayout
extension CalendarView: UICollectionViewDelegateFlowLayout {
	func collectionView(
		_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout,
		sizeForItemAt indexPath: IndexPath
	) -> CGSize {
		let width = collectionView.frame.width / 7
        let height = 40.0
		return CGSize(width: width, height: height)
	}
}

// MARK: - CalendarDayCell
class CalendarDayCell: UICollectionViewCell {
	private let dayLabel: UILabel = {
		let label = UILabel()
		label.textAlignment = .center
		label.font = UIFont.systemFont(ofSize: 16)
		return label
	}()

	private let dotView: UIView = {
		let view = UIView()
		view.backgroundColor = Colors.theme.dynamicColor()
		view.layer.cornerRadius = 3
		view.isHidden = true
		return view
	}()

	override init(frame: CGRect) {
		super.init(frame: frame)
		setupUI()
	}

	required init?(coder: NSCoder) {
		super.init(coder: coder)
		setupUI()
	}

	private func setupUI() {

        contentView.layer.cornerRadius = 5
        contentView.layer.masksToBounds = true

		contentView.addSubview(dayLabel)
		dayLabel.snp.makeConstraints { make in
			make.center.equalToSuperview()
		}

		contentView.addSubview(dotView)
		dotView.snp.makeConstraints { make in
			make.centerX.equalToSuperview()
			make.bottom.equalToSuperview().offset(-4)
			make.width.height.equalTo(6)
		}
	}

	func configure(with day: JournalCalendarViewModel.CalendarDay) {
		dayLabel.text = String(day.day)

		if day.isCurrentMonth {
			dayLabel.textColor = Colors.text.dynamicColor()
			dayLabel.font = UIFont.systemFont(ofSize: 16)

			if day.isSelected {
				contentView.backgroundColor = Colors.theme.dynamicColor()
				dayLabel.textColor = .white
				dayLabel.font = UIFont.boldSystemFont(ofSize: 16)
			} else {
				contentView.backgroundColor = .clear
			}

			dotView.isHidden = !day.hasEntries
		} else {
			dayLabel.textColor = Colors.secondaryText.dynamicColor().withAlphaComponent(0.5)
			dayLabel.font = UIFont.systemFont(ofSize: 16)
			contentView.backgroundColor = .clear
			dotView.isHidden = true
		}
	}

	override func prepareForReuse() {
		super.prepareForReuse()
		contentView.backgroundColor = .clear
		dayLabel.text = nil
		dotView.isHidden = true
	}
}
