//
//  JournalEntryCell.swift
//  XYZ
//
//  Created by Trae AI on 2025/4/25.
//

import SnapKit
import UIKit

class JournalEntryCell: UITableViewCell {

    // MARK: - UI Elements
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = Colors.themeColor.light.withAlphaComponent(0.2)
        view.layer.cornerRadius = 24
        view.clipsToBounds = true
        return view
    }()

    private let dayLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 32)
        label.textColor = Colors.theme.dynamicColor()
        return label
    }()

    private let monthLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = Colors.secondaryText.dynamicColor()
        return label
    }()

    private let mediaIconsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 4
        stackView.alignment = .center
        return stackView
    }()

    private let imageIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "photo")
        imageView.tintColor = Colors.secondaryText.dynamicColor()
        imageView.contentMode = .scaleAspectFit
        imageView.isHidden = true
        return imageView
    }()

    private let videoIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "video")
        imageView.tintColor = Colors.secondaryText.dynamicColor()
        imageView.contentMode = .scaleAspectFit
        imageView.isHidden = true
        return imageView
    }()

    private let audioIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "waveform")
        imageView.tintColor = Colors.secondaryText.dynamicColor()
        imageView.contentMode = .scaleAspectFit
        imageView.isHidden = true
        return imageView
    }()

    private let moodCircle: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear
        view.layer.cornerRadius = 18
        view.clipsToBounds = true
        return view
    }()
    private let moodLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 24)
        label.textAlignment = .center
        return label
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 20)
        label.textColor = Colors.text.dynamicColor()
        label.numberOfLines = 1
        return label
    }()

    private let contentPreviewLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = Colors.text.dynamicColor()
        label.numberOfLines = 2
        label.lineBreakMode = .byTruncatingTail
        return label
    }()

    private let imagesCollectionView: ImagesCollectionView = {
        return ImagesCollectionView(frame: .init(x: 0, y: 0, width: 100, height: 100))
    }()

    private let entryImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 8
        imageView.clipsToBounds = true
        imageView.isHidden = true
        return imageView
    }()

    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(
                UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12))
        }

        // Date
        containerView.addSubview(dayLabel)
        dayLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(18)
            make.left.equalToSuperview().offset(18)
        }

        containerView.addSubview(monthLabel)
        monthLabel.snp.makeConstraints { make in
            make.bottom.equalTo(dayLabel.snp.bottom).offset(-6)
            make.left.equalTo(dayLabel.snp.right).offset(6)
        }

        // Media Icons
        containerView.addSubview(mediaIconsStackView)
        mediaIconsStackView.addArrangedSubview(imageIcon)
        mediaIconsStackView.addArrangedSubview(videoIcon)
        mediaIconsStackView.addArrangedSubview(audioIcon)
        mediaIconsStackView.snp.makeConstraints { make in
            make.left.equalTo(monthLabel.snp.right).offset(6)
            make.bottom.equalTo(monthLabel)
        }

        // Mood
        containerView.addSubview(moodCircle)
        moodCircle.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(18)
            make.right.equalToSuperview().offset(-18)
            make.width.height.equalTo(36)
        }
        moodCircle.addSubview(moodLabel)
        moodLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        // Title
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(dayLabel.snp.bottom).offset(6)
            make.left.equalToSuperview().offset(18)
            make.right.equalTo(moodCircle.snp.left).offset(-12)
        }
        // Content
        containerView.addSubview(contentPreviewLabel)
        contentPreviewLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.left.equalToSuperview().offset(18)
            make.right.equalToSuperview().offset(-18)
            make.bottom.lessThanOrEqualToSuperview().offset(-18)
        }
    }

    // MARK: - Configuration
    func configure(with entry: JournalEntry) {
        // Show normal layout, hide empty state
        contentPreviewLabel.isHidden = false
        entryImageView.isHidden = entry.images.first?.isEmpty ?? true

        // Date
        let dayFormatter = DateFormatter()
        dayFormatter.dateFormat = "dd"
        dayLabel.text = dayFormatter.string(from: entry.date)
        let monthFormatter = DateFormatter()
        monthFormatter.dateFormat = "MMM"
        monthLabel.text = monthFormatter.string(from: entry.date)

        // Media Icons
        imageIcon.isHidden = entry.images.isEmpty
        videoIcon.isHidden = entry.videos.isEmpty
        audioIcon.isHidden = entry.audios.isEmpty

        // Mood
        moodLabel.text = entry.moodEmoji
        moodCircle.backgroundColor = UIColor.clear
        // Title
        titleLabel.attributedText = entry.title.content.toAttributedString()?.toNSMutableAttributedString()
            .setTextStyle(
                lineHeight: 1.3,
                alignment: .left,
                textColor: Colors.textPrimary.dynamicColor(),
                font: UIFont.boldSystemFont(ofSize: 18)
            )

        // Content
        contentPreviewLabel.attributedText = NSMutableAttributedString(
            attributedString: entry.content
        ).setTextStyle(
            lineHeight: 1.2,
            alignment: .left,
            textColor: Colors.textPrimary.dynamicColor(),
            font: UIFont.systemFont(ofSize: 16)
        )
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        dayLabel.text = nil
        monthLabel.text = nil
        titleLabel.text = nil
        contentPreviewLabel.text = nil
        moodLabel.text = nil
        entryImageView.image = nil
        entryImageView.isHidden = true
        imageIcon.isHidden = true
        videoIcon.isHidden = true
        audioIcon.isHidden = true
    }
}
