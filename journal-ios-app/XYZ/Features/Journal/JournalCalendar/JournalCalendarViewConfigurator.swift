//
//  JournalCalendarViewConfigurator.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import UIKit

class JournalCalendarViewConfigurator {
    static func configureViewController() -> UIViewController {
        let viewController = JournalCalendarViewController()
        let viewModel = JournalCalendarViewModel()
        let router = JournalCalendarRouter(viewController: viewController)

        viewController.viewModel = viewModel
        viewController.router = router

        return viewController
    }
}
