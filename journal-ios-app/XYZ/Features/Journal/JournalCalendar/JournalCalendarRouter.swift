//
//  JournalCalendarRouter.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import UIKit

class JournalCalendarRouter {
    weak var viewController: JournalCalendarViewController?

    init(viewController: JournalCalendarViewController) {
        self.viewController = viewController
    }

    func navigateToJournalDetail(with journalId: String) {
        let detailVC = JournalDetailViewConfigurator.configureViewController(journalId: journalId)
        viewController?.navigationController?.pushViewController(detailVC, animated: true)
    }

    func navigateToNewJournal() {
        let newJournalVC = JournalNewViewController()
        newJournalVC.viewModel = JournalNewViewModel()
        newJournalVC.router = JournalNewRouter(viewController: newJournalVC)
        viewController?.navigationController?.pushViewController(newJournalVC, animated: true)
    }

    func navigateToJournalList() {
        let listVC = JournalListViewController()
        listVC.viewModel = JournalListViewModel()
        listVC.router = JournalListRouter(viewController: listVC)
        viewController?.navigationController?.pushViewController(listVC, animated: true)
    }
}
