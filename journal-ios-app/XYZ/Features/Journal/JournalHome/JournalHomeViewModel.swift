//
//  JournalHomeViewModel.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/24.
//

import Combine
import Foundation
import UIKit  // Import UIKit for UIImage if JournalEntry uses it

// MARK: - Data Models for Data-Driven Approach
enum JournalHomeItem {
    case journalEntry(JournalEntry)
    case advertisement
}

struct YearSection {
    let year: Int
    var items: [JournalHomeItem] // Changed from entries to items

    // Computed property for backward compatibility
    var entries: [JournalEntry] {
        return items.compactMap { item in
            if case .journalEntry(let entry) = item {
                return entry
            }
            return nil
        }
    }
}

enum FilterOption: Equatable {
    case all
    case year(Int)
    case month(Int, Int) // year, month
}

class JournalHomeViewModel: ObservableObject, BaseService {
    @Published var motivationalQuote: String = ""
    @Published var currentDateString: String = ""
    @Published var isLoading: Bool = false
    @Published var isLoadingMore: Bool = false
    @Published var errorMessage: String?
    @Published var userName: String = ""
    @Published var avatar: String = ""
    @Published var yearSections: [YearSection] = []
    @Published var availableYears: [Int] = []
    @Published var availableMonths: [(year: Int, month: Int, name: String)] = []

    private var cancellables = Set<AnyCancellable>()
    private let journalRepository: JournalRepositoryProtocol
    private var allEntries: [JournalEntry] = []
    private var currentFilter: FilterOption = .all

    // Pagination properties
    private let pageSize = 500 // Load 500 entries at a time
    private var currentOffset = 0
    private var hasMoreEntries = true
    private var totalEntriesCount: Int?

    // Ad configuration
    private let adFrequency = 2 // Show ad after every 5 journal entries
    private var hasShownAd = false // Track if we've already shown an ad

    init(journalRepository: JournalRepositoryProtocol = JournalRepository()) {
        self.journalRepository = journalRepository
        fetchInitialData()
        setupDateFormatter()
        loadJournalEntries()
    }

    func fetchInitialData() {
        isLoading = true
        errorMessage = nil
        // Simulate fetching data
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            guard let self = self else { return }
            self.loadMotivationalQuote()
            self.isLoading = false
        }
    }

    func retrieveUser() {
        self.userName = App.shared.userName()
        self.avatar = App.shared.instance?.user?.picture ?? ""
    }

    private func setupDateFormatter() {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")  // Use Chinese locale
        formatter.dateFormat = "yyyy年M月d日 EEEE"  // Format: 2025年4月24日 星期四
        currentDateString = formatter.string(from: Date())
    }

    private func loadMotivationalQuote() {
        motivationalQuote = XYLocalize.XYLocalize("journal_home_quote_placeholder")
    }

    // Function to be called when the 'Start Journaling' button is tapped
    func startJournalingTapped(router: JournalHomeRouter) {
        router.navigateToJournalNew()
    }

    // Load journal entries from database with pagination
    private func loadJournalEntries() {
        // Reset pagination state
        currentOffset = 0
        hasMoreEntries = true
        allEntries.removeAll()

        // Get total count for better user feedback
        totalEntriesCount = DB.shared.getJournalEntriesCount()

        // Load first batch
        loadMoreJournalEntries()
    }

    // Load more journal entries (for pagination)
    func loadMoreJournalEntries() {
        guard hasMoreEntries && !isLoadingMore else { return }

        isLoadingMore = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let newEntries = DB.shared.getJournalEntries(limit: self.pageSize, offset: self.currentOffset)

            DispatchQueue.main.async {
                self.isLoadingMore = false

                // Debug logging
                XYLog("📖 Loaded \(newEntries.count) entries (offset: \(self.currentOffset), total loaded: \(self.allEntries.count + newEntries.count))")

                if newEntries.isEmpty {
                    self.hasMoreEntries = false
                    XYLog("📖 No more entries to load")
                } else {
                    self.allEntries.append(contentsOf: newEntries)
                    self.currentOffset += newEntries.count

                    // If we got fewer entries than requested, we've reached the end
                    if newEntries.count < self.pageSize {
                        self.hasMoreEntries = false
                        XYLog("📖 Reached end of entries (got \(newEntries.count) < \(self.pageSize))")
                    }

                    // Also check against total count if available
                    if let totalCount = self.totalEntriesCount, self.allEntries.count >= totalCount {
                        self.hasMoreEntries = false
                        XYLog("📖 Loaded all entries (\(self.allEntries.count)/\(totalCount))")
                    }
                }

                self.updateAvailableFilters()
                self.applyCurrentFilter()
            }
        }
    }

    // Check if we should load more entries (call this when user scrolls near bottom)
    func shouldLoadMoreEntries(for indexPath: IndexPath) -> Bool {
        guard hasMoreEntries && !isLoadingMore else { return false }

        // Calculate total visible items across all sections
        let totalItems = yearSections.reduce(0) { $0 + $1.items.count }
        let currentItemIndex = yearSections.prefix(indexPath.section).reduce(0) { $0 + $1.items.count } + indexPath.row

        // Load more when user is within 10 items of the end
        return currentItemIndex >= totalItems - 10
    }

    // Refresh journal entries
    func refreshJournalEntries() {
        hasShownAd = false // Reset ad state when refreshing
        loadJournalEntries()
    }

    // Navigate to journal detail
    func navigateToJournalDetail(_ entry: JournalEntry, router: JournalHomeRouter) {
        router.navigateToJournalDetail(journalId: entry.id)
    }

    // MARK: - Search Functionality
    func searchEntries(with searchText: String) {
        if searchText.isEmpty {
            applyCurrentFilter()
        } else {
            let filteredEntries = getFilteredEntries().filter { entry in
                entry.title.content.localizedCaseInsensitiveContains(searchText) ||
                entry.contentSnippet.localizedCaseInsensitiveContains(searchText) ||
                entry.content.string.localizedCaseInsensitiveContains(searchText)
            }
            yearSections = groupEntriesByYear(filteredEntries)
        }
    }

    func clearSearch() {
        applyCurrentFilter()
    }

    // MARK: - Filter Functionality
    func applyFilter(_ filter: FilterOption) {
        currentFilter = filter
        applyCurrentFilter()
    }

    func getCurrentFilter() -> FilterOption {
        return currentFilter
    }

    private func applyCurrentFilter() {
        let filteredEntries = getFilteredEntries()
        yearSections = groupEntriesByYear(filteredEntries)
    }

    private func getFilteredEntries() -> [JournalEntry] {
        let calendar = Calendar.current

        switch currentFilter {
        case .all:
            return allEntries
        case .year(let year):
            return allEntries.filter { calendar.component(.year, from: $0.date) == year }
        case .month(let year, let month):
            return allEntries.filter {
                calendar.component(.year, from: $0.date) == year &&
                calendar.component(.month, from: $0.date) == month
            }
        }
    }

    private func groupEntriesByYear(_ entries: [JournalEntry]) -> [YearSection] {
        let calendar = Calendar.current
        let groupedByYear = Dictionary(grouping: entries) { entry in
            calendar.component(.year, from: entry.date)
        }

        return groupedByYear.map { year, entries in
            let sortedEntries = entries.sorted { $0.date > $1.date } // Sort by date descending within year
            let itemsWithAds = insertAdsIntoItems(entries: sortedEntries, isFirstSection: year == availableYears.first)

            return YearSection(
                year: year,
                items: itemsWithAds
            )
        }.sorted { $0.year > $1.year } // Sort years descending (newest first)
    }

    // MARK: - Ad Insertion Logic
    private func insertAdsIntoItems(entries: [JournalEntry], isFirstSection: Bool) -> [JournalHomeItem] {
        var items: [JournalHomeItem] = []

        for (index, entry) in entries.enumerated() {
            items.append(.journalEntry(entry))

            // Insert ad after every adFrequency entries, only in the first section, and only once
            if !hasShownAd && isFirstSection && (index + 1) % adFrequency == 0 && entries.count >= adFrequency {
                items.append(.advertisement)
                hasShownAd = true
            }
        }

        return items
    }

    private func updateAvailableFilters() {
        let calendar = Calendar.current

        // Get available years
        let years = Set(allEntries.map { calendar.component(.year, from: $0.date) })
        availableYears = Array(years).sorted(by: >)

        // Get available months
        let monthFormatter = DateFormatter()
        monthFormatter.locale = Locale(identifier: "zh_CN")

        var monthsSet: Set<String> = []
        var monthsArray: [(year: Int, month: Int, name: String)] = []

        for entry in allEntries {
            let year = calendar.component(.year, from: entry.date)
            let month = calendar.component(.month, from: entry.date)
            let key = "\(year)-\(month)"

            if !monthsSet.contains(key) {
                monthsSet.insert(key)
                monthFormatter.dateFormat = "MM月"
                let monthName = monthFormatter.string(from: entry.date)
                monthsArray.append((year: year, month: month, name: monthName))
            }
        }

        availableMonths = monthsArray.sorted { first, second in
            if first.year != second.year {
                return first.year > second.year
            }
            return first.month > second.month
        }
    }

    func deleteJournal(id: String) {
        DB.shared.deleteJournalEntry(id: id)

        // remove from allEntries
        allEntries = allEntries.filter { $0.id != id }

        // Update total count
        if let totalCount = totalEntriesCount {
            totalEntriesCount = totalCount - 1
        }

        // If we have fewer entries than expected and there might be more to load
        if allEntries.count < pageSize && hasMoreEntries {
            // Try to load one more entry to fill the gap
            loadMoreJournalEntries()
        }
    }

    // MARK: - Pagination Status
    var canLoadMore: Bool {
        return hasMoreEntries && !isLoadingMore
    }

    var loadedEntriesCount: Int {
        return allEntries.count
    }

    var isEmpty: Bool {
        return allEntries.isEmpty
    }
}
