//
//  FilterPopupView.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/24.
//

import SnapKit
import UIKit

protocol FilterPopupViewDelegate: AnyObject {
    func filterPopupView(_ view: FilterPopupView, didSelectFilter filter: FilterOption)
    func filterPopupViewDidCancel(_ view: FilterPopupView)
}

class FilterPopupView: UIView {

    weak var delegate: FilterPopupViewDelegate?
    private var availableYears: [Int] = []
    private var availableMonths: [(year: Int, month: Int, name: String)] = []
    private var currentFilter: FilterOption = .all
    private var groupedMonths: [Int: [(year: Int, month: Int, name: String)]] = [:]

    // MARK: - UI Components
    private let backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        return view
    }()

    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemBackground
        view.layer.cornerRadius = 20
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        return view
    }()

    private let headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "选择时间范围"
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textColor = .label
        label.textAlignment = .center
        return label
    }()

    private let closeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "xmark"), for: .normal)
        button.tintColor = .systemGray
        return button
    }()

    private let collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 12
        layout.minimumLineSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 16, left: 20, bottom: 16, right: 20)

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.register(MonthCell.self, forCellWithReuseIdentifier: "MonthCell")
        collectionView.register(AllOptionCell.self, forCellWithReuseIdentifier: "AllOptionCell")
        collectionView.register(YearHeaderView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: "YearHeaderView")
        return collectionView
    }()

    private let handleView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemGray4
        view.layer.cornerRadius = 2.5
        return view
    }()

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupActions()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)

        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        containerView.addSubview(handleView)
        containerView.addSubview(headerView)
        containerView.addSubview(collectionView)

        handleView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.centerX.equalToSuperview()
            make.width.equalTo(40)
            make.height.equalTo(5)
        }

        headerView.snp.makeConstraints { make in
            make.top.equalTo(handleView.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(60)
        }

        headerView.addSubview(titleLabel)
        headerView.addSubview(closeButton)

        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        closeButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-20)
            make.width.height.equalTo(30)
        }

        collectionView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        collectionView.setContentInsets()

        containerView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(500)
        }

        collectionView.delegate = self
        collectionView.dataSource = self
    }

    private func setupActions() {
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)
    }

    // MARK: - Public Methods
    func configure(years: [Int], months: [(year: Int, month: Int, name: String)], currentFilter: FilterOption) {
        self.availableYears = years.sorted(by: >)  // Sort years in descending order
        self.availableMonths = months
        self.currentFilter = currentFilter

        // Group months by year
        groupedMonths = Dictionary(grouping: months) { $0.year }

        collectionView.reloadData()
    }

    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Initial position (off-screen)
        containerView.transform = CGAffineTransform(translationX: 0, y: 500)
        backgroundView.alpha = 0

        // Animate in
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
            self.backgroundView.alpha = 1
        }
    }

    func hide() {
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseIn) {
            self.containerView.transform = CGAffineTransform(translationX: 0, y: 500)
            self.backgroundView.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }

    // MARK: - Actions
    @objc private func closeButtonTapped() {
        delegate?.filterPopupViewDidCancel(self)
        hide()
    }

    @objc private func backgroundTapped() {
        delegate?.filterPopupViewDidCancel(self)
        hide()
    }
}

// MARK: - UICollectionViewDataSource
extension FilterPopupView: UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return availableYears.count + 1  // +1 for "All" section
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if section == 0 {
            return 1  // "All" option
        } else {
            let year = availableYears[section - 1]
            return groupedMonths[year]?.count ?? 0
        }
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.section == 0 {
            // "All" section
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "AllOptionCell", for: indexPath) as! AllOptionCell
            let isSelected = currentFilter == .all
            cell.configure(isSelected: isSelected)
            return cell
        } else {
            // Month sections
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "MonthCell", for: indexPath) as! MonthCell
            let year = availableYears[indexPath.section - 1]
            let monthsForYear = groupedMonths[year] ?? []
            let month = monthsForYear[indexPath.item]

            let isSelected: Bool
            if case .month(let selectedYear, let selectedMonth) = currentFilter {
                isSelected = selectedYear == month.year && selectedMonth == month.month
            } else {
                isSelected = false
            }

            cell.configure(title: month.name, isSelected: isSelected)
            return cell
        }
    }

    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        if kind == UICollectionView.elementKindSectionHeader {
            let headerView = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: "YearHeaderView", for: indexPath) as! YearHeaderView

            if indexPath.section == 0 {
                headerView.configure(title: "全部")
            } else {
                let year = availableYears[indexPath.section - 1]
                headerView.configure(title: "\(year)年")
            }

            return headerView
        }

        return UICollectionReusableView()
    }
}

// MARK: - UICollectionViewDelegate
extension FilterPopupView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.section == 0 {
            // "All" option selected
            delegate?.filterPopupView(self, didSelectFilter: .all)
            hide()
        } else {
            // Month selected
            let year = availableYears[indexPath.section - 1]
            let monthsForYear = groupedMonths[year] ?? []
            let month = monthsForYear[indexPath.item]
            let filter = FilterOption.month(month.year, month.month)

            delegate?.filterPopupView(self, didSelectFilter: filter)
            hide()
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension FilterPopupView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if indexPath.section == 0 {
            // "All" option - full width
            let width = collectionView.frame.width - 40  // 40 for section insets
            return CGSize(width: width, height: 50)
        } else {
            // Month cells - 4 per row
            let numberOfItemsPerRow: CGFloat = 4
            let spacing: CGFloat = 12
            let totalSpacing = (numberOfItemsPerRow - 1) * spacing + 40 // 40 for section insets
            let width = (collectionView.frame.width - totalSpacing) / numberOfItemsPerRow
            return CGSize(width: width, height: 50)
        }
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        return CGSize(width: collectionView.frame.width, height: 40)
    }
}

// MARK: - YearHeaderView
class YearHeaderView: UICollectionReusableView {
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 16)
        label.textColor = .label
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
        }
    }

    func configure(title: String) {
        titleLabel.text = title
    }
}

// MARK: - AllOptionCell
class AllOptionCell: UICollectionViewCell {
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "全部"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textAlignment = .center
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        layer.cornerRadius = 12
        layer.borderWidth = 1

        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(8)
        }
    }

    func configure(isSelected: Bool) {
        if isSelected {
            backgroundColor = UIColor.systemBlue
            titleLabel.textColor = .white
            layer.borderColor = UIColor.systemBlue.cgColor
        } else {
            backgroundColor = UIColor.systemGray6
            titleLabel.textColor = .label
            layer.borderColor = UIColor.systemGray4.cgColor
        }
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        backgroundColor = UIColor.systemGray6
        titleLabel.textColor = .label
        layer.borderColor = UIColor.systemGray4.cgColor
    }
}

// MARK: - MonthCell
class MonthCell: UICollectionViewCell {
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textAlignment = .center
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        layer.cornerRadius = 12
        layer.borderWidth = 1

        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(8)
        }
    }

    func configure(title: String, isSelected: Bool) {
        titleLabel.text = title

        if isSelected {
            backgroundColor = UIColor.systemBlue
            titleLabel.textColor = .white
            layer.borderColor = UIColor.systemBlue.cgColor
        } else {
            backgroundColor = UIColor.systemGray6
            titleLabel.textColor = .label
            layer.borderColor = UIColor.systemGray4.cgColor
        }
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        backgroundColor = UIColor.systemGray6
        titleLabel.textColor = .label
        layer.borderColor = UIColor.systemGray4.cgColor
    }
}
