import UIKit
import Kingfisher
import SwifterSwift

protocol JournalHomeHeaderProtocol: AnyObject {
    func didUserAvatarTapped()
}

class JournalHomeHeaderView: UIView {

	private let stackView = UIStackView()

    weak var delegate: JournalHomeHeaderProtocol?

	lazy private var titleLabel: UILabel = {
		let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
		label.textColor = Theme.Colors.text.dynamicColor()
		return label
	}()

	lazy private var avatarImageView = {
		let imageView = UIImageView()
		imageView.layer.cornerRadius = 15
		imageView.clipsToBounds = true
		imageView.contentMode = .scaleAspectFill
		return imageView
	}()

	override init(frame: CGRect) {
		super.init(frame: frame)
		setupUI()
        setupActions()
	}

	required init?(coder: NSCoder) {
		fatalError("init(coder:) has not been implemented")
	}

	private func setupUI() {
        stackView.axis = .horizontal
		stackView.distribution = .equalSpacing
        stackView.alignment = .center
		addSubview(stackView)

		titleLabel.textColor = Theme.Colors.text.dynamicColor()
		titleLabel.text = "Hello, You"

		stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(avatarImageView)

		avatarImageView.snp.makeConstraints { make in
            make.width.height.equalTo(30).priority(.required)
		}

		stackView.snp.makeConstraints { make in
			make.top.equalToSuperview().offset(16)
			make.leading.equalToSuperview().offset(0)
			make.trailing.equalToSuperview().offset(0)
			make.bottom.equalToSuperview().offset(-16)
		}
	}

    func setupActions() {
        let tap = UITapGestureRecognizer()
        tap.numberOfTapsRequired = 1
        tap.numberOfTouchesRequired = 1
        tap.addTarget(self, action: #selector(didUserAvatarTapped))
        self.avatarImageView.isUserInteractionEnabled = true
        self.avatarImageView.addGestureRecognizer(tap)
    }

	func configure(with viewModel: JournalHomeViewModel) {
        self.titleLabel.text = "Hi, \(viewModel.userName)"
        self.avatarImageView.kf.setImage(
            with: URL(string: viewModel.avatar),
            placeholder: UIImage(named: "avatar")?.withTintColor(Theme.Colors.avatarTint.dynamicColor())
        )
	}

    @objc func didUserAvatarTapped() {
        self.delegate?.didUserAvatarTapped()
    }
}
