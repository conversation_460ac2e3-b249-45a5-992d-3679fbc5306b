//
//  JournalEmptyStateView.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/24.
//

import UIKit
import SnapKit

protocol JournalEmptyStateViewDelegate: AnyObject {
    func didTapStartWriting()
}

class JournalEmptyStateView: UIView {

    weak var delegate: JournalEmptyStateViewDelegate?

    // MARK: - UI Elements
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private let illustrationImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = Theme.Colors.themeColor.light
        // Try to use a more specific journal-related icon, fallback to book.pages
        if let journalIcon = UIImage(systemName: "book.closed") {
            imageView.image = journalIcon
        } else {
            imageView.image = UIImage(systemName: "book.pages")
        }
        return imageView
    }()

    private let illustrationBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = Theme.Colors.themeColor.light.withAlphaComponent(0.1)
        view.layer.cornerRadius = 75
        return view
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "开始你的日记之旅"
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textColor = Theme.Colors.textPrimary.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private let subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "记录生活的美好瞬间\n让每一天都值得回忆"
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.textColor = Theme.Colors.secondaryText.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        return label
    }()

    private let startWritingButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("✍️ 开始写日记", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        button.backgroundColor = Theme.Colors.themeColor.light
        button.layer.cornerRadius = 25
        button.layer.shadowColor = Theme.Colors.themeColor.light.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 4)
        button.layer.shadowRadius = 12
        button.layer.shadowOpacity = 0.3
        return button
    }()

    private let featuresStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.distribution = .fillEqually
        return stackView
    }()

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupActions()
        startAnimations()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear

        addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(32)
        }

        // Illustration
        containerView.addSubview(illustrationBackgroundView)
        illustrationBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(150)
        }

        containerView.addSubview(illustrationImageView)
        illustrationImageView.snp.makeConstraints { make in
            make.center.equalTo(illustrationBackgroundView)
            make.width.height.equalTo(80)
        }

        // Title
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(illustrationBackgroundView.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview()
        }

        // Subtitle
        containerView.addSubview(subtitleLabel)
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview()
        }

        // Features
        setupFeatures()
        containerView.addSubview(featuresStackView)
        featuresStackView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(32)
            make.leading.trailing.equalToSuperview()
        }

        // Start Writing Button
        containerView.addSubview(startWritingButton)
        startWritingButton.snp.makeConstraints { make in
            make.top.equalTo(featuresStackView.snp.bottom).offset(32)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(50)
            make.bottom.equalToSuperview()
        }
    }

    private func setupFeatures() {
        let features = [
            ("📝", "记录想法", "捕捉灵感的瞬间"),
            ("📸", "添加照片", "让回忆更加生动"),
            ("🎨", "自定义样式", "打造专属的日记风格")
        ]

        for feature in features {
            let featureView = createFeatureView(
                icon: feature.0,
                title: feature.1,
                description: feature.2
            )
            featuresStackView.addArrangedSubview(featureView)
        }
    }

    private func createFeatureView(icon: String, title: String, description: String) -> UIView {
        let containerView = UIView()

        let iconLabel = UILabel()
        iconLabel.text = icon
        iconLabel.font = UIFont.systemFont(ofSize: 24)
        iconLabel.textAlignment = .center

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = Theme.Colors.textPrimary.dynamicColor()

        let descriptionLabel = UILabel()
        descriptionLabel.text = description
        descriptionLabel.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        descriptionLabel.textColor = Theme.Colors.secondaryText.dynamicColor()

        let textStackView = UIStackView(arrangedSubviews: [titleLabel, descriptionLabel])
        textStackView.axis = .vertical
        textStackView.spacing = 2

        containerView.addSubview(iconLabel)
        containerView.addSubview(textStackView)

        iconLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(40)
        }

        textStackView.snp.makeConstraints { make in
            make.leading.equalTo(iconLabel.snp.trailing).offset(16)
            make.trailing.equalToSuperview()
            make.centerY.equalToSuperview()
        }

        containerView.snp.makeConstraints { make in
            make.height.equalTo(50)
        }

        return containerView
    }

    private func setupActions() {
        startWritingButton.addTarget(self, action: #selector(startWritingTapped), for: .touchUpInside)
    }

    // MARK: - Actions
    @objc private func startWritingTapped() {
        // Add button press animation
        UIView.animate(withDuration: 0.1, animations: {
            self.startWritingButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.startWritingButton.transform = .identity
            }
        }

        delegate?.didTapStartWriting()
    }

    // MARK: - Animations
    private func startAnimations() {
        // Initial state - hidden
        illustrationBackgroundView.alpha = 0
        illustrationImageView.alpha = 0
        titleLabel.alpha = 0
        subtitleLabel.alpha = 0
        featuresStackView.alpha = 0
        startWritingButton.alpha = 0

        illustrationBackgroundView.transform = CGAffineTransform(scaleX: 0.3, y: 0.3)
        illustrationImageView.transform = CGAffineTransform(scaleX: 0.5, y: 0.5)
        titleLabel.transform = CGAffineTransform(translationX: 0, y: 20)
        subtitleLabel.transform = CGAffineTransform(translationX: 0, y: 20)
        featuresStackView.transform = CGAffineTransform(translationX: 0, y: 20)
        startWritingButton.transform = CGAffineTransform(translationX: 0, y: 20)

        // Animate in sequence
        UIView.animate(withDuration: 0.8, delay: 0.2, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.5, options: .curveEaseOut) {
            self.illustrationBackgroundView.alpha = 1
            self.illustrationBackgroundView.transform = .identity
        }

        UIView.animate(withDuration: 0.6, delay: 0.4, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.5, options: .curveEaseOut) {
            self.illustrationImageView.alpha = 1
            self.illustrationImageView.transform = .identity
        }

        UIView.animate(withDuration: 0.5, delay: 0.6, options: .curveEaseOut) {
            self.titleLabel.alpha = 1
            self.titleLabel.transform = .identity
        }

        UIView.animate(withDuration: 0.5, delay: 0.8, options: .curveEaseOut) {
            self.subtitleLabel.alpha = 1
            self.subtitleLabel.transform = .identity
        }

        UIView.animate(withDuration: 0.5, delay: 1.0, options: .curveEaseOut) {
            self.featuresStackView.alpha = 1
            self.featuresStackView.transform = .identity
        }

        UIView.animate(withDuration: 0.5, delay: 1.2, options: .curveEaseOut) {
            self.startWritingButton.alpha = 1
            self.startWritingButton.transform = .identity
        }

        // Start floating animation for illustration
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.startFloatingAnimation()
        }
    }

    private func startFloatingAnimation() {
        UIView.animate(withDuration: 3.0, delay: 0, options: [.repeat, .autoreverse, .curveEaseInOut]) {
            self.illustrationBackgroundView.transform = CGAffineTransform(translationX: 0, y: -8)
        }
    }

    // MARK: - Public Methods
    func show(animated: Bool = true) {
        isHidden = false
        if animated {
            startAnimations()
        }
    }

    func hide(animated: Bool = true) {
        if animated {
            UIView.animate(withDuration: 0.3) {
                self.alpha = 0
            } completion: { _ in
                self.isHidden = true
                self.alpha = 1
            }
        } else {
            isHidden = true
        }
    }
}
