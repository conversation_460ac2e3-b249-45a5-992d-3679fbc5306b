import UIKit

class JournalHomeRouter {

    let viewController: UIViewController

    init(viewController: UIViewController) {
        self.viewController = viewController
    }

    func navigateToJournalNew() {
        let journalNewVC = JournalNewViewConfigurator().configure()
        journalNewVC.isModalInPresentation = true
        viewController.present(journalNewVC, animated: true)
    }

    func navigateToPersonal() {
        let profile = JournalHomeConfigurator.configureProfile()
        let navigationController = NavigationViewController(rootViewController: profile)
        navigationController.isModalInPresentation = true
        navigationController.modalPresentationStyle = .fullScreen
        viewController.navigationController?.present(navigationController, animated: true)
    }
}

// Add this extension to provide the missing method in router if needed
extension JournalHomeRouter {
    func navigateToJournalDetail(journalId: String) {
        let detailVC = JournalDetailViewConfigurator.configureViewController(journalId: journalId)
        viewController.navigationController?.pushViewController(detailVC, animated: true)
    }
}
