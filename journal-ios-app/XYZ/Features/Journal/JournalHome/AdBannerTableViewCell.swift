//
//  AdBannerTableViewCell.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/15.
//

import UIKit

class AdBannerTableViewCell: UITableViewCell {

    // MARK: - Properties
    private let adBannerView = AdMobBannerView()

    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        contentView.addSubview(adBannerView)

        adBannerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            adBannerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            adBannerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            adBannerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            adBannerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8)
        ])
    }

    // MARK: - Public Methods
    func loadAd(from viewController: UIViewController) {
        adBannerView.loadAd(from: viewController)
    }

    func refreshAd() {
        adBannerView.refreshAd()
    }
}
