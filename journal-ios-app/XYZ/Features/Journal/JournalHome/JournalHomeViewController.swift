//
//  JournalHomeViewController.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/24.
//

import Combine
import SnapKit
import UIKit

// Add import for JournalEntryCardView file if needed
// If it's in the same module, no need for additional import

class JournalHomeViewController: BaseViewController {

    var viewModel: JournalHomeViewModel!
    var router: JournalHomeRouter!
    private var cancellables = Set<AnyCancellable>()

    // Ad configuration
    private let adFrequency = 5 // Show ad after every 5 journal entries
    private var hasShownAd = false // Track if we've already shown an ad

    private let tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.contentInsetAdjustmentBehavior = .never
        tableView.register(JournalEntryCell.self, forCellReuseIdentifier: "JournalEntryCell")
        tableView.register(JournalHomeHeaderTableViewCell.self, forCellReuseIdentifier: "HeaderCell")
        tableView.register(AdBannerTableViewCell.self, forCellReuseIdentifier: "AdBannerCell")
        return tableView
    }()

    private let headerContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private let proButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("👑 PRO", for: .normal)
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .bold)
        button.backgroundColor = UIColor(red: 1.0, green: 0.8, blue: 0.4, alpha: 1.0)
        button.layer.cornerRadius = 15
        var configuration = UIButton.Configuration.plain()
        configuration.contentInsets = NSDirectionalEdgeInsets(top: 6, leading: 12, bottom: 6, trailing: 12)
        button.configuration = configuration
        return button
    }()

    private let searchButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "magnifyingglass"), for: .normal)
        button.tintColor = Colors.themeColor.light
        button.layer.cornerRadius = 20
        return button
    }()

    private let filterButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "line.3.horizontal.decrease.circle"), for: .normal)
        button.tintColor = Colors.themeColor.light
        button.layer.cornerRadius = 20
        return button
    }()

    // Search functionality
    private let searchController: UISearchController = {
        let controller = UISearchController(searchResultsController: nil)
        controller.searchBar.placeholder = "搜索日记..."
        controller.searchBar.searchBarStyle = .minimal
        controller.obscuresBackgroundDuringPresentation = false
        return controller
    }()

    private lazy var refreshControl: UIRefreshControl = {
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(handleRefresh), for: .valueChanged)
        return refreshControl
    }()

    private lazy var emptyStateView: JournalEmptyStateView = {
        let view = JournalEmptyStateView()
        view.delegate = self
        view.isHidden = true
        return view
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        hideNavigationBar = true
        setupUI()
        setupBindings()
        setupActions()
        viewModel.retrieveUser()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        viewModel.refreshJournalEntries()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        if searchController.isActive {
            searchController.isActive = false
        }
    }

    // MARK: - Setup
    private func setupUI() {
        // Add header container
        view.addSubview(headerContainerView)
        headerContainerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }

        // Add top bar to header container
        setupTopBar()

        setupTableView()

        // Add table view
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(headerContainerView.snp.bottom)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        // Add empty state view
        view.addSubview(emptyStateView)
        emptyStateView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(0 - view.safeBottomInsets())
        }

        view.layoutIfNeeded()
    }

    private func setupTopBar() {
        // headerContainerView.addSubview(proButton)
        headerContainerView.addSubview(searchButton)
        headerContainerView.addSubview(filterButton)

        // proButton.snp.makeConstraints { make in
        //  make.centerY.equalToSuperview()
        //  make.centerX.equalToSuperview()
        // }
        searchButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().offset(16)
            make.width.height.equalTo(40)
        }

        filterButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-16)
            make.width.height.equalTo(40)
        }
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.setContentInsets()

        // Add pull-to-refresh
        tableView.refreshControl = refreshControl
    }

    private func setupBindings() {
        viewModel.$yearSections
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.resetAdState()
                self?.tableView.reloadData()
                self?.updateEmptyState()
            }
            .store(in: &cancellables)

        viewModel.$userName
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)

        // Observe loading more state
        viewModel.$isLoadingMore
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoadingMore in
                // You can show/hide a loading indicator here if needed
                if isLoadingMore {
                    // Show loading indicator at bottom
                    self?.showLoadingIndicator()
                } else {
                    // Hide loading indicator
                    self?.hideLoadingIndicator()
                }
                self?.updateEmptyState()
            }
            .store(in: &cancellables)

        // Observe loading state
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateEmptyState()
            }
            .store(in: &cancellables)

        // Setup search functionality
        searchController.searchResultsUpdater = self
        searchController.delegate = self

        // Listen for registration prompts
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(showRegistrationPrompt(_:)),
            name: NSNotification.Name("ShowRegistrationPrompt"),
            object: nil
        )
    }

    private func setupActions() {
        searchButton.addTarget(self, action: #selector(searchButtonTapped), for: .touchUpInside)
        filterButton.addTarget(self, action: #selector(filterButtonTapped), for: .touchUpInside)
        proButton.addTarget(self, action: #selector(proButtonTapped), for: .touchUpInside)
    }

    @objc private func searchButtonTapped() {
        // Present search interface
        present(searchController, animated: true)

        UIView.animate(withDuration: 0.3) {
            self.headerContainerView.alpha = 0
            self.headerContainerView.isHidden = true
        }
    }

    @objc private func filterButtonTapped() {
        showFilterPopup()
    }

    private func showFilterPopup() {
        let filterPopup = FilterPopupView()
        filterPopup.delegate = self

        // Get current filter from view model
        let currentFilter = viewModel.getCurrentFilter()
        filterPopup.configure(
            years: viewModel.availableYears,
            months: viewModel.availableMonths,
            currentFilter: currentFilter
        )

        filterPopup.show(in: view)
    }

    @objc private func proButtonTapped() {
        // Show pro features
        let alertController = UIAlertController(title: "👑 升级到 PRO", message: "解锁更多强大功能", preferredStyle: .alert)

        alertController.addAction(UIAlertAction(title: "查看功能", style: .default) { [weak self] _ in
            self?.showProFeatures()
        })

        alertController.addAction(UIAlertAction(title: "稍后再说", style: .cancel))

        present(alertController, animated: true)
    }

    private func showProFeatures() {
        let alertController = UIAlertController(title: "PRO 功能", message: nil, preferredStyle: .alert)

        let message = """
        🔒 无限制日记数量
        🎨 更多主题和字体
        📊 数据统计和分析
        ☁️ 云端同步
        🔐 密码保护
        📤 导出功能
        🎯 高级搜索
        """

        alertController.message = message

        alertController.addAction(UIAlertAction(title: "立即升级", style: .default) { _ in
            // Navigate to purchase screen
            XYLog("Navigate to purchase screen")
        })

        alertController.addAction(UIAlertAction(title: "关闭", style: .cancel))

        present(alertController, animated: true)
    }

    @objc private func showRegistrationPrompt(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let journalCount = userInfo["journalCount"] as? Int else { return }

        let alertController = UIAlertController(
            title: "🎉 太棒了！",
            message: "您已经写了 \(journalCount) 篇日记！注册账户可以：\n\n☁️ 云端同步，永不丢失\n📱 多设备访问\n🔒 数据安全备份",
            preferredStyle: .alert
        )

        alertController.addAction(UIAlertAction(title: "立即注册", style: .default) { [weak self] _ in
            self?.router.navigateToPersonal() // This will show login screen for guest users
        })

        alertController.addAction(UIAlertAction(title: "稍后再说", style: .cancel))

        // Show after a small delay to ensure smooth UX
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.present(alertController, animated: true)
        }
    }

    // MARK: - Ad Management
    private func resetAdState() {
        hasShownAd = false
    }

    // MARK: - Loading Indicator Methods
    private func showLoadingIndicator() {
        // Create a simple loading footer view
        let footerView = UIView(frame: CGRect(x: 0, y: 0, width: tableView.frame.width, height: 50))
        let activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.center = footerView.center
        activityIndicator.startAnimating()
        footerView.addSubview(activityIndicator)

        let loadingLabel = UILabel()
        loadingLabel.text = "加载更多..."
        loadingLabel.font = UIFont.systemFont(ofSize: 14)
        loadingLabel.textColor = .gray
        loadingLabel.textAlignment = .center
        loadingLabel.frame = CGRect(x: 0, y: 25, width: footerView.frame.width, height: 20)
        footerView.addSubview(loadingLabel)

        tableView.tableFooterView = footerView
    }

    private func hideLoadingIndicator() {
        let footerHeight: CGFloat = App.shared.isInGuestMode() ? 60 : 30

        if viewModel.canLoadMore {
            // Keep a minimal footer if there are more entries to load
            let footerView = UIView(frame: CGRect(x: 0, y: 0, width: tableView.frame.width, height: footerHeight))
            let loadMoreLabel = UILabel()
            loadMoreLabel.text = "向上滑动加载更多 (已加载 \(viewModel.loadedEntriesCount) 篇)"
            loadMoreLabel.font = UIFont.systemFont(ofSize: 12)
            loadMoreLabel.textColor = .lightGray
            loadMoreLabel.textAlignment = .center
            loadMoreLabel.frame = CGRect(x: 0, y: 0, width: footerView.frame.width, height: 20)
            footerView.addSubview(loadMoreLabel)

            // Add guest mode info if applicable
            if App.shared.isInGuestMode() {
                let guestLabel = UILabel()
                guestLabel.text = "📱 访客模式"
                guestLabel.font = UIFont.systemFont(ofSize: 11)
                guestLabel.textColor = .systemBlue
                guestLabel.textAlignment = .center
                guestLabel.frame = CGRect(x: 0, y: 25, width: footerView.frame.width, height: 30)
                footerView.addSubview(guestLabel)
            }

            tableView.tableFooterView = footerView
        } else {
            // No more entries to load
            let footerView = UIView(frame: CGRect(x: 0, y: 0, width: tableView.frame.width, height: footerHeight))
            let endLabel = UILabel()
            endLabel.text = "已加载全部日记 (\(viewModel.loadedEntriesCount) 篇)"
            endLabel.font = UIFont.systemFont(ofSize: 12)
            endLabel.textColor = .lightGray
            endLabel.textAlignment = .center
            endLabel.frame = CGRect(x: 0, y: 0, width: footerView.frame.width, height: 20)
            footerView.addSubview(endLabel)

            // Add guest mode info if applicable
            if App.shared.isInGuestMode() {
                let guestLabel = UILabel()
                guestLabel.text = "📱 访客模式"
                guestLabel.font = UIFont.systemFont(ofSize: 11)
                guestLabel.textColor = .systemBlue
                guestLabel.textAlignment = .center
                guestLabel.frame = CGRect(x: 0, y: 25, width: footerView.frame.width, height: 30)
                footerView.addSubview(guestLabel)
            }

            tableView.tableFooterView = footerView
        }
    }

    @objc private func handleRefresh() {
        viewModel.refreshJournalEntries()

        // End refreshing after a short delay to ensure smooth animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.refreshControl.endRefreshing()
        }
    }

    private func updateEmptyState() {
        let shouldShowEmptyState = viewModel.isEmpty

        if shouldShowEmptyState && emptyStateView.isHidden {
            emptyStateView.show(animated: true)
            tableView.isHidden = true
            headerContainerView.isHidden = true
        } else if !shouldShowEmptyState && !emptyStateView.isHidden {
            emptyStateView.hide(animated: true)
            tableView.isHidden = false
            headerContainerView.isHidden = false
        }
    }
}

// MARK: - UITableViewDataSource
extension JournalHomeViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel.yearSections.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard section < viewModel.yearSections.count else { return 0 }
        return viewModel.yearSections[section].items.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard indexPath.section < viewModel.yearSections.count else {
            return UITableViewCell() // Fallback cell
        }

        let yearSection = viewModel.yearSections[indexPath.section]

        guard indexPath.row < yearSection.items.count else {
            return UITableViewCell() // Fallback cell
        }

        let item = yearSection.items[indexPath.row]

        switch item {
        case .journalEntry(let entry):
            let cell = tableView.dequeueReusableCell(withIdentifier: "JournalEntryCell", for: indexPath) as! JournalEntryCell
            cell.configure(with: entry)
            return cell

        case .advertisement:
            let cell = tableView.dequeueReusableCell(withIdentifier: "AdBannerCell", for: indexPath) as! AdBannerTableViewCell
            cell.loadAd(from: self)
            return cell
        }
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        guard section < viewModel.yearSections.count else { return nil }
        return "\(viewModel.yearSections[section].year)年"
    }
}

// MARK: - UITableViewDelegate
extension JournalHomeViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 44
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        guard indexPath.section < viewModel.yearSections.count else { return }

        let yearSection = viewModel.yearSections[indexPath.section]

        guard indexPath.row < yearSection.items.count else { return }

        let item = yearSection.items[indexPath.row]

        // Only handle selection for journal entries, not ads
        if case .journalEntry(let entry) = item {
            router.navigateToJournalDetail(journalId: entry.id)
        }
        // For .advertisement case, do nothing (no navigation)
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard section < viewModel.yearSections.count else { return nil }

        let headerView = UIView()
        headerView.backgroundColor = Colors.pageBg.dynamicColor()

        let label = UILabel()
        let yearSection = viewModel.yearSections[section]
        label.text = "\(yearSection.year)年 (\(yearSection.entries.count)篇)"
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = Colors.textPrimary.dynamicColor()

        headerView.addSubview(label)
        label.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }

        return headerView
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 44
    }

    // MARK: - Table View Delegate
    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {

        // 1. Remove item from data source calculate the index of the item to delete
        guard indexPath.section < viewModel.yearSections.count else { return nil }

        // Validate index
        guard indexPath.row < viewModel.yearSections[indexPath.section].items.count else { return nil }

        let journal = viewModel.yearSections[indexPath.section].items[indexPath.row]
        if case .advertisement = journal {
            return nil
        }

        let deleteAction = UIContextualAction(style: .destructive, title: XYLocalize.XYLocalize("delete_journal_title") ) { [weak self] (_, _, completionHandler) in
            self?.handleDelete(at: indexPath)
            completionHandler(true)
        }

        return UISwipeActionsConfiguration(actions: [deleteAction])
    }

    private func handleDelete(at indexPath: IndexPath) {
        // 1. Remove item from data source calculate the index of the item to delete
        guard indexPath.section < viewModel.yearSections.count else { return }

        // Validate index
        guard indexPath.row < viewModel.yearSections[indexPath.section].items.count else { return }

        let journal = viewModel.yearSections[indexPath.section].items[indexPath.row]
        if case .journalEntry(let entry) = journal {
            // 2. Delete row from table view
            viewModel.deleteJournal(id: entry.id)
            viewModel.yearSections[indexPath.section].items.remove(at: indexPath.row)
            tableView.performBatchUpdates {
                tableView.deleteRows(at: [indexPath], with: .automatic)
            }
        }
    }

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        // Check if we should load more entries when user scrolls near bottom
        if viewModel.shouldLoadMoreEntries(for: indexPath) {
            viewModel.loadMoreJournalEntries()
        }
    }
}

// MARK: - UISearchResultsUpdating
extension JournalHomeViewController: UISearchResultsUpdating {
    func updateSearchResults(for searchController: UISearchController) {
        guard let searchText = searchController.searchBar.text else { return }
        viewModel.searchEntries(with: searchText)
    }
}

// MARK: - UISearchControllerDelegate
extension JournalHomeViewController: UISearchControllerDelegate {
    func didDismissSearchController(_ searchController: UISearchController) {
        viewModel.clearSearch()
        UIView.animate(withDuration: 0.3) {
            self.headerContainerView.alpha = 1
            self.headerContainerView.isHidden = false
        }
    }
}

// MARK: - JournalHomeHeaderProtocol
extension JournalHomeViewController: JournalHomeHeaderProtocol {
    func didUserAvatarTapped() {
        router.navigateToPersonal()
    }
}

// Collection view methods removed - using popup instead

// MARK: - JournalEntryCardDelegate
extension JournalHomeViewController: JournalEntryCardDelegate {
    func didTapJournalEntryCard(_ entry: JournalEntry) {
        viewModel.navigateToJournalDetail(entry, router: router)
    }
}

// MARK: - FilterPopupViewDelegate
extension JournalHomeViewController: FilterPopupViewDelegate {
    func filterPopupView(_ view: FilterPopupView, didSelectFilter filter: FilterOption) {
        viewModel.applyFilter(filter)
    }

    func filterPopupViewDidCancel(_ view: FilterPopupView) {
        // Handle cancel if needed
    }
}

// MARK: - JournalEmptyStateViewDelegate
extension JournalHomeViewController: JournalEmptyStateViewDelegate {
    func didTapStartWriting() {
        viewModel.startJournalingTapped(router: router)
    }
}

// MARK: - Custom Table View Cells

class JournalHomeHeaderTableViewCell: UITableViewCell {
    let headerView = JournalHomeHeaderView() // Make it public
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        contentView.addSubview(headerView)

        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-10)
        }
    }

    func configure(with viewModel: JournalHomeViewModel) {
        headerView.configure(with: viewModel)
        headerView.delegate = nil // Will be set by the view controller
    }
}

class JournalEntryTableViewCell: UITableViewCell {
    private let cardView = JournalEntryCardView()
    weak var delegate: JournalEntryCardDelegate?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        contentView.addSubview(cardView)
        cardView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-8)
        }
    }

    func configure(with entry: JournalEntry) {
        cardView.configure(with: entry)
        cardView.delegate = delegate
    }
}
