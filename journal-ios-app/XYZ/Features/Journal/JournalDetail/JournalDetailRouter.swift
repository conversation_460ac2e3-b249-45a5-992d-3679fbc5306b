import UIKit

class JournalDetailRouter {

    weak var viewController: UIViewController?
    init(viewController: UIViewController) {
        self.viewController = viewController
    }

    func dismiss() {
        viewController?.dismiss(animated: true)
    }

    func navigateToEditJournal(journalEntry: JournalEntry) {
        // Create and configure the edit journal view controller
        let configurator = JournalNewViewConfigurator()
        let editVC = configurator.configureForEditing(journalEntry: journalEntry)

        viewController?.present(editVC, animated: true)
    }
}
