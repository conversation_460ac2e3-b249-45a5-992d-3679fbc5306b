import Combine
import UIKit

class JournalDetailViewController: BaseViewController {
    // MARK: - Properties
    var viewModel: JournalDetailViewModel!
    var router: JournalDetailRouter!

    private var cancellables = Set<AnyCancellable>()

    // Add properties to track content items and scrollable content
    private var contentItems: [ContentItem] = []
    private var journalContentItems: [JournalContentItem] = []
    private let scrollView = UIScrollView()
    private lazy var dateContainer: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var dayLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 40)
        label.textColor = Theme.Colors.themeColorBlue.dynamicColor()
        return label
    }()

    private lazy var monthYearLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 18)
        label.textColor = Theme.Colors.secondaryText.dynamicColor()
        return label
    }()

    private lazy var moodLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 28)
        label.textColor = Theme.Colors.secondaryText.dynamicColor()
        return label
    }()

    private let contentStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()

    private var tags: [String] = []  // 新增：存储标签
    private var location: String?  // 新增：存储位置
    private var activityVC: UIActivityViewController?

    // Single action menu button
    private lazy var menuButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "ellipsis.circle"), for: .normal)
        button.tintColor = Theme.Colors.theme.dynamicColor()
        button.addTarget(self, action: #selector(menuButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        setupBindings()
    }

    private func setupDateDisplay() {

        contentStackView.addArrangedSubview(dateContainer)
        dateContainer.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }

        dateContainer.addSubview(dayLabel)
        dayLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.leading.equalToSuperview().offset(16)
        }

        dateContainer.addSubview(monthYearLabel)
        monthYearLabel.snp.makeConstraints { make in
            make.top.equalTo(dayLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(-8)
        }

        dateContainer.addSubview(moodLabel)
        moodLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalTo(dayLabel)
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadJournalContent()
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)

        // Clean up activity controller if still present
        if let activityVC = self.activityVC {
            activityVC.dismiss(animated: false, completion: nil)
            self.activityVC = nil
        }
    }

    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = Theme.Colors.pageBg.dynamicColor()
        showCloseBackButton = true

        // Configure navigation bar with single menu button
        title = ""
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: menuButton)

        // Setup ScrollView
        view.addSubview(scrollView)
        scrollView.setBasicContentInsets()
        scrollView.alwaysBounceVertical = true
        scrollView.showsVerticalScrollIndicator = false
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.bottom.equalToSuperview()
        }

        // Setup Content StackView
        scrollView.addSubview(contentStackView)
        contentStackView.axis = .vertical
        contentStackView.spacing = 0  // Adjust spacing as needed
        contentStackView.alignment = .fill
        contentStackView.distribution = .fill
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(scrollView.contentLayoutGuide.snp.top)
            make.leading.trailing.bottom.equalToSuperview()
            make.width.equalToSuperview()  // Important for vertical scrolling
        }
    }

    private func setupBindings() {
        viewModel.$isDeleted
            .receive(on: RunLoop.main)
            .sink { [weak self] isDeleted in
                if isDeleted {
                    self?.didJournalDeleted()
                }
            }.store(in: &cancellables)

        viewModel.$journal
            .receive(on: RunLoop.main)
            .compactMap({ $0 })
            .sink { [weak self] journal in
                self?.updateJournal(journal)
            }.store(in: &cancellables)
    }

    // Load journal content from the view model
    private func loadJournalContent() {
        viewModel.loadJournal()
    }

    private func updateJournal(_ journal: JournalEntry) {
        contentItems = journal.contentItems
        if journal.title.content != "" {
            contentItems.insert(journal.title, at: 0)
        }

        tags = journal.tags
        location = journal.location
        populateContentStackView()
        updateDate()
    }

    private func didJournalDeleted() {
        self.navigationController?.popViewController(animated: true)
    }

    private func updateDate() {
        let date = viewModel.journal?.date ?? Date()
        let dayFormatter = DateFormatter()
        dayFormatter.dateFormat = "dd"
        dayLabel.text = dayFormatter.string(from: date)

        let monthYearFormatter = DateFormatter()
        monthYearFormatter.dateFormat = "MMMM, yyyy"
        monthYearLabel.text = monthYearFormatter.string(from: date).uppercased()

        moodLabel.text = viewModel.journal?.moodEmoji ?? ""
    }

    // swiftlint:disable:next cyclomatic_complexity function_body_length
    private func populateContentStackView() {
        // Clear existing views
        contentStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        setupDateDisplay()

        // Add other content items
        let otherContentItems = contentItems
        for item in otherContentItems {
            var contentView: UIView?

            switch item.type {
            case .text:
                let textLabel = UILabel()
                textLabel.attributedText = item.content.toAttributedString()?
                    .toNSMutableAttributedString()
                    .setTextStyle(
                        lineHeight: viewModel.journal?.lineHeightMultiple ?? 1.5,
                        alignment: NSTextAlignment(
                            rawValue: viewModel.journal?.textAlignment ?? NSTextAlignment.left.rawValue) ?? .left,
                        textColor: nil
                    )
                textLabel.numberOfLines = 0

                // Create container view with insets
                let containerView = UIView()
                containerView.addSubview(textLabel)
                textLabel.snp.makeConstraints { make in
                    make.edges.equalToSuperview().inset(
                        UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
                }

                contentView = containerView
            case .image:
                if let url = URL(string: item.content) {
                    contentView = createMediaView(
                        mediaType: .image(url: url, mode: item.mode, size: item.size)
                    )
                }
            case .video:
                if let url = URL(string: item.content) {
                    // Assuming thumbnail is not directly available in ContentItem for detail view
                    // You might need to adjust how thumbnails are handled or fetched here
                    contentView = createMediaView(
                        mediaType: .video(url: url, thumbnail: nil, mode: item.mode, size: item.size )
                    )
                }
            case .audio:
                if let url = URL(string: item.content),
                    let durationStr = item.metadata?["duration"],
                    let duration = TimeInterval(durationStr) {
                    contentView = createAudioView(url: url, duration: duration)
                } else if let url = URL(string: item.content) {
                    // Fallback if duration is not available
                    contentView = createAudioView(url: url, duration: 0)
                }
            }

            if let viewToAdd = contentView {
                contentStackView.addArrangedSubview(viewToAdd)
            }
        }

        // Add location and tags footer if either exists
        if (location != nil && !location!.isEmpty) || !tags.isEmpty {
            let footerView = createLocationAndTagsFooterView()
            contentStackView.addArrangedSubview(footerView)
        }

        // Add a spacer view at the bottom if needed to push content up when it's short
        let spacerView = UIView()
        spacerView.setContentHuggingPriority(.defaultLow, for: .vertical)
        contentStackView.addArrangedSubview(spacerView)
    }

    private func createMediaView(mediaType: MediaContentView.MediaType) -> UIView {
        let containerView = JournalItemContainerView()
        containerView.backgroundColor = .clear

        let mediaView = MediaContentView(mediaType: mediaType, disableEdit: true, alignment: NSTextAlignment(rawValue: (viewModel.journal?.textAlignment ?? 0))  ?? .left )
        mediaView.delegate = self

        containerView.addSubview(mediaView)
        mediaView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        return containerView
    }

    private func createAudioView(url: URL, duration: TimeInterval) -> UIView {
        let containerView = JournalItemContainerView()
        containerView.backgroundColor = .clear

        let audioView = AudioPlayerView(audioURL: url, duration: duration, disableEdit: true)
        audioView.delegate = self

        containerView.addSubview(audioView)
        audioView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        return containerView
    }

    private func createLocationAndTagsFooterView() -> UIView {
        let footerView = UIView()
        footerView.backgroundColor = .clear

        let containerView = UIView()
        containerView.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        containerView.layer.cornerRadius = 12
        containerView.layer.masksToBounds = true

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill

        // Location section
        if let loc = location, !loc.isEmpty {
            let locationContainer = createLocationDisplayView(location: loc)
            stackView.addArrangedSubview(locationContainer)
        }

        // Tags section
        if !tags.isEmpty {
            let tagsContainer = createTagsDisplayView(tags: tags)
            stackView.addArrangedSubview(tagsContainer)
        }

        containerView.addSubview(stackView)
        footerView.addSubview(containerView)

        // Constraints
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }

        return footerView
    }

    private func createLocationDisplayView(location: String) -> UIView {
        let locationView = UIView()

        let iconLabel = UILabel()
        iconLabel.text = "📍"
        iconLabel.font = UIFont.systemFont(ofSize: 16)

        let locationLabel = UILabel()
        locationLabel.text = location
        locationLabel.font = UIFont.systemFont(ofSize: 14)
        locationLabel.textColor = Theme.Colors.text.dynamicColor()
        locationLabel.numberOfLines = 0

        locationView.addSubview(iconLabel)
        locationView.addSubview(locationLabel)

        iconLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.width.equalTo(20)
        }

        locationLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconLabel.snp.trailing).offset(8)
            make.trailing.top.bottom.equalToSuperview()
            make.height.greaterThanOrEqualTo(20)
        }

        return locationView
    }

    private func createTagsDisplayView(tags: [String]) -> UIView {
        let tagsView = UIView()

        let iconLabel = UILabel()
        iconLabel.text = "🏷️"
        iconLabel.font = UIFont.systemFont(ofSize: 16)

        let tagsLabel = UILabel()
        tagsLabel.text = tags.joined(separator: ", ")
        tagsLabel.font = UIFont.systemFont(ofSize: 14)
        tagsLabel.textColor = Theme.Colors.text.dynamicColor()
        tagsLabel.numberOfLines = 0

        tagsView.addSubview(iconLabel)
        tagsView.addSubview(tagsLabel)

        iconLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.width.equalTo(20)
        }

        tagsLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconLabel.snp.trailing).offset(8)
            make.trailing.top.bottom.equalToSuperview()
            make.height.greaterThanOrEqualTo(20)
        }

        return tagsView
    }

    @objc private func menuButtonTapped() {
        let actionMenuVC = JournalActionMenuViewController()
        actionMenuVC.delegate = self
        actionMenuVC.modalPresentationStyle = .overFullScreen
        actionMenuVC.modalTransitionStyle = .crossDissolve
        present(actionMenuVC, animated: false)
    }

    @objc private func editButtonTapped() {
        if let journal = viewModel.journal {
            router.navigateToEditJournal(journalEntry: journal)
        }
    }

    @objc private func deleteButtonTapped() {
        let alert = UIAlertController(
            title: XYLocalize.XYLocalize("delete_journal_title"),
            message: XYLocalize.XYLocalize("delete_journal_message"),
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: XYLocalize.XYLocalize("cancel"), style: .cancel))
        alert.addAction(
            UIAlertAction(
                title: XYLocalize.XYLocalize("ok"),
                style: .destructive
            ) { [weak self] _ in
                self?.viewModel.deleteJournal()
            })
        present(alert, animated: true)
    }

    private func shareJournal() {
        let shareFormatVC = ShareFormatSelectionViewController()
        shareFormatVC.delegate = self
        shareFormatVC.modalPresentationStyle = .overFullScreen
        shareFormatVC.modalTransitionStyle = .crossDissolve
        present(shareFormatVC, animated: false)
    }

    private func shareAsImage() {
        guard let journal = viewModel.journal else { return }

        self.isLoadingParts = true

        // Create the share generator
        let shareGenerator = JournalShareGenerator(
            journal: journal,
            contentItems: contentItems,
            tags: tags,
            location: location
        )

        // Generate the image
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let shareImage = shareGenerator.generateShareImage() else {
                DispatchQueue.main.async {
                    self?.isLoadingParts = false
                    self?.showErrorAlert(message: "生成图片失败，请重试")
                }
                return
            }

            // Compress and optimize image for sharing
            let compressedImage = self?.optimizeImageForSharing(shareImage) ?? shareImage

            DispatchQueue.main.async {
                self?.isLoadingParts = false
                self?.presentShareActivity(with: [compressedImage])
            }
        }
    }

    private func shareAsPDF() {
        guard let journal = viewModel.journal else { return }

        self.isLoadingParts = true

        // Create the share generator
        let shareGenerator = JournalShareGenerator(
            journal: journal,
            contentItems: contentItems,
            tags: tags,
            location: location
        )

        // Generate the PDF
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let pdfData = shareGenerator.generateSharePDF() else {
                DispatchQueue.main.async {
                    self?.isLoadingParts = false
                    self?.showErrorAlert(message: "生成PDF失败，请重试")
                }
                return
            }

            // Save PDF to temporary file
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("journal_\(Date().timeIntervalSince1970).pdf")

            do {
                try pdfData.write(to: tempURL)
                DispatchQueue.main.async {
                    self?.isLoadingParts = false
                    self?.presentShareActivity(with: [tempURL])
                }
            } catch {
                DispatchQueue.main.async {
                    self?.isLoadingParts = false
                    self?.showErrorAlert(message: "保存PDF失败，请重试")
                }
            }
        }
    }

    private func presentShareActivity(with items: [Any]) {
        // Clean up any existing activity controller
        if let existingActivityVC = self.activityVC {
            existingActivityVC.dismiss(animated: false, completion: nil)
            self.activityVC = nil
        }

        let activityVC = UIActivityViewController(activityItems: items, applicationActivities: nil)

        // Configure popover for iPad
        if let popoverController = activityVC.popoverPresentationController {
            popoverController.sourceView = self.view
            popoverController.sourceRect = CGRect(x: self.view.bounds.midX, y: self.view.bounds.midY, width: 0, height: 0)
            popoverController.permittedArrowDirections = []
        }

        // Set completion handler to clean up
        activityVC.completionWithItemsHandler = { [weak self] _, _, _, _ in
            self?.activityVC = nil
        }

        present(activityVC, animated: true) { [weak self] in
            self?.activityVC = activityVC
        }
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "错误", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func optimizeImageForSharing(_ image: UIImage) -> UIImage {
        // WeChat has limitations on image size and memory usage
        let maxDimension: CGFloat = 1920 // Max width or height
        let maxFileSize: Int = 10 * 1024 * 1024 // 10MB max file size

        var optimizedImage = image

        // Resize if image is too large
        let size = image.size
        if size.width > maxDimension || size.height > maxDimension {
            let scale = min(maxDimension / size.width, maxDimension / size.height)
            let newSize = CGSize(width: size.width * scale, height: size.height * scale)

            let renderer = UIGraphicsImageRenderer(size: newSize)
            optimizedImage = renderer.image { _ in
                image.draw(in: CGRect(origin: .zero, size: newSize))
            }
        }

        // Compress to reduce file size if needed
        var compressionQuality: CGFloat = 0.9
        var imageData = optimizedImage.jpegData(compressionQuality: compressionQuality)

        // Reduce quality until file size is acceptable
        while let data = imageData, data.count > maxFileSize && compressionQuality > 0.1 {
            compressionQuality -= 0.1
            imageData = optimizedImage.jpegData(compressionQuality: compressionQuality)
        }

        // Return optimized image
        if let data = imageData, let finalImage = UIImage(data: data) {
            return finalImage
        }

        return optimizedImage
    }
}

// MARK: - MediaContentViewDelegate
extension JournalDetailViewController: MediaContentViewDelegate {
    func didTapDelete(mediaView: MediaContentView, mediaType: MediaContentView.MediaType) {
        // No-op in detail view
    }

    func didChangeWidthMode(mediaView: MediaContentView, isFullWidth: Bool) {
        // No-op in detail view
    }
}

// MARK: - AudioContentViewDelegate
extension JournalDetailViewController: AudioContentViewDelegate {
    func didTapDelete(mediaView: AudioPlayerView) {
        // No-op in detail view
    }
}

// MARK: - JournalActionMenuViewControllerDelegate
extension JournalDetailViewController: JournalActionMenuViewControllerDelegate {
    func journalActionMenuDidSelectEdit() {
        editButtonTapped()
    }

    func journalActionMenuDidSelectDelete() {
        deleteButtonTapped()
    }

    func journalActionMenuDidSelectShare() {
        shareJournal()
    }

    func journalActionMenuDidCancel() {
        // Nothing to do
    }
}

// MARK: - ShareFormatSelectionDelegate
extension JournalDetailViewController: ShareFormatSelectionDelegate {
    func shareFormatSelectionDidSelectImage() {
        shareAsImage()
    }

    func shareFormatSelectionDidSelectPDF() {
        shareAsPDF()
    }

    func shareFormatSelectionDidCancel() {
        // Nothing to do
    }
}
