import UIKit
import PDFKit
import AVFoundation

class JournalShareGenerator {

    // MARK: - Properties
    private let journal: JournalEntry
    private let contentItems: [ContentItem]
    private let tags: [String]
    private let location: String?
    private let maxHeightForImage: CGFloat = 2000

    // MARK: - Initialization
    init(journal: JournalEntry, contentItems: [ContentItem], tags: [String], location: String?) {
        self.journal = journal
        self.contentItems = contentItems
        self.tags = tags
        self.location = location
    }

    // MARK: - Image Generation
    func generateShareImage() -> UIImage? {
        // Use actual screen width for better quality
        let screenScale = UIScreen.main.scale
        let screenWidth = UIScreen.main.bounds.width
        let pageWidth: CGFloat = screenWidth * screenScale
        let padding: CGFloat = 10 * screenScale
        let contentWidth = pageWidth - (padding * 2)

        // Calculate total height needed
        var totalHeight: CGFloat = padding

        // Header section height (scaled)
        totalHeight += 120 * screenScale // Date and mood section
        totalHeight += 20 * screenScale  // Spacing

        // Content sections height
        var contentHeight: CGFloat = 0
        for item in contentItems {
            contentHeight += calculateContentItemHeight(item, width: contentWidth, scale: screenScale)
            contentHeight += 20 * screenScale // Spacing between items
        }
        totalHeight += contentHeight

        // Tags and location section
        if !tags.isEmpty || !isLocationEmpty() {
            totalHeight += 80 * screenScale // Tags and location section
            totalHeight += 20 * screenScale // Spacing
        }

        // Footer
        totalHeight += 60 * screenScale // Footer with app name
        totalHeight += padding // Bottom padding

        let pageSize = CGSize(width: pageWidth, height: totalHeight)

        // Create the image with proper scale
        let format = UIGraphicsImageRendererFormat()
        format.scale = screenScale
        let renderer = UIGraphicsImageRenderer(size: pageSize, format: format)
        return renderer.image { context in
            drawImageContent(in: context, pageSize: pageSize, contentWidth: contentWidth, padding: padding, scale: screenScale)
        }
    }

    private func drawImageContent(in context: UIGraphicsImageRendererContext, pageSize: CGSize, contentWidth: CGFloat, padding: CGFloat, scale: CGFloat) {
        let cgContext = context.cgContext

        // Background
        let backgroundColor = Theme.Colors.pageBg.dynamicColor()
        backgroundColor.setFill()
        cgContext.fill(CGRect(origin: .zero, size: pageSize))

        var currentY: CGFloat = padding

        // Draw header (date and mood)
        currentY = drawHeader(in: cgContext, startY: currentY, width: contentWidth, padding: padding, scale: scale)
        currentY += 30 * scale

        // Draw content items
        for item in contentItems {
            currentY = drawContentItem(item, in: cgContext, startY: currentY, width: contentWidth, padding: padding, scale: scale)
            currentY += 20 * scale
        }

        // Draw tags and location
        if !tags.isEmpty || !isLocationEmpty() {
            currentY = drawTagsAndLocation(in: cgContext, startY: currentY, width: contentWidth, padding: padding, scale: scale)
            currentY += 30 * scale
        }

        // Draw footer
        drawFooter(in: cgContext, startY: currentY, width: contentWidth, padding: padding, scale: scale)
    }

    private func drawHeader(in context: CGContext, startY: CGFloat, width: CGFloat, padding: CGFloat, scale: CGFloat) -> CGFloat {
        let currentY = startY

        // Date formatting
        let dayFormatter = DateFormatter()
        dayFormatter.dateFormat = "dd"
        let dayText = dayFormatter.string(from: journal.date)

        let monthYearFormatter = DateFormatter()
        monthYearFormatter.dateFormat = "MMMM, yyyy"
        let monthYearText = monthYearFormatter.string(from: journal.date).uppercased()

        // Get text alignment from journal
        let textAlignment = NSTextAlignment(rawValue: journal.textAlignment) ?? .left

        // Draw day
        let dayFont = UIFont.boldSystemFont(ofSize: 48 * scale)
        let dayColor = Theme.Colors.themeColorBlue.dynamicColor()
        let dayAttributes: [NSAttributedString.Key: Any] = [
            .font: dayFont,
            .foregroundColor: dayColor
        ]

        let daySize = dayText.size(withAttributes: dayAttributes)
        let dayX: CGFloat
        switch textAlignment {
        case .center:
            dayX = padding + (width - daySize.width) / 2
        case .right:
            dayX = padding + width - daySize.width
        default: // .left
            dayX = padding
        }
        dayText.draw(at: CGPoint(x: dayX, y: currentY), withAttributes: dayAttributes)

        // Draw month/year
        let monthYearFont = UIFont.systemFont(ofSize: 18 * scale)
        let monthYearColor = Theme.Colors.secondaryText.dynamicColor()
        let monthYearAttributes: [NSAttributedString.Key: Any] = [
            .font: monthYearFont,
            .foregroundColor: monthYearColor
        ]

        let monthYearSize = monthYearText.size(withAttributes: monthYearAttributes)
        let monthYearX: CGFloat
        switch textAlignment {
        case .center:
            monthYearX = padding + (width - monthYearSize.width) / 2
        case .right:
            monthYearX = padding + width - monthYearSize.width
        default: // .left
            monthYearX = padding
        }
        monthYearText.draw(at: CGPoint(x: monthYearX, y: currentY + daySize.height + 5 * scale), withAttributes: monthYearAttributes)

        // Draw mood emoji on the right
        if !journal.moodEmoji.isEmpty {
            let moodFont = UIFont.systemFont(ofSize: 36 * scale)
            let moodAttributes: [NSAttributedString.Key: Any] = [.font: moodFont]
            let moodSize = journal.moodEmoji.size(withAttributes: moodAttributes)

            journal.moodEmoji.draw(at: CGPoint(x: padding + width - moodSize.width, y: currentY + 10 * scale), withAttributes: moodAttributes)
        }

        return currentY + 80 * scale
    }

    // swiftlint:disable:next cyclomatic_complexity function_body_length function_parameter_count
    private func drawContentItem(_ item: ContentItem, in context: CGContext, startY: CGFloat, width: CGFloat, padding: CGFloat, scale: CGFloat) -> CGFloat {
        var currentY = startY

        switch item.type {
        case .text:
            // Convert HTML to attributed string and draw
            if let attributedString = item.content.toAttributedString() {
                // Scale the font sizes in the attributed string
                let scaledAttributedString = scaleAttributedString(attributedString, scale: scale)

                // Get text alignment from journal
                let textAlignment = NSTextAlignment(rawValue: journal.textAlignment) ?? .left
                let mutableAttributedString = NSMutableAttributedString(attributedString: scaledAttributedString)

                // Apply alignment and line height
                let paragraphStyle = NSMutableParagraphStyle()
                paragraphStyle.alignment = textAlignment
                paragraphStyle.lineHeightMultiple = journal.lineHeightMultiple
                mutableAttributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: mutableAttributedString.length))

                // Apply journal's text color if needed (for theme consistency)
                if let textColor = UIColor(hexString: journal.textColor) {
                    // Only apply if the current color is a default color (to preserve user formatting)
                    let currentRange = NSRange(location: 0, length: mutableAttributedString.length)
                    mutableAttributedString.enumerateAttribute(.foregroundColor, in: currentRange, options: []) { (value, range, _) in
                        if value == nil {
                            // No color set, apply journal's default color
                            mutableAttributedString.addAttribute(.foregroundColor, value: textColor, range: range)
                        }
                    }
                }

                let boundingRect = mutableAttributedString.boundingRect(with: CGSize(width: width, height: CGFloat.greatestFiniteMagnitude), options: [.usesLineFragmentOrigin, .usesFontLeading], context: nil)

                mutableAttributedString.draw(in: CGRect(x: padding, y: currentY, width: width, height: boundingRect.height))
                currentY += boundingRect.height
            }

        case .image:
            // Get image metadata for actual dimensions
            let imageSize = item.size
            let imageMode = item.mode

            // Try to load and get actual image
            var actualImage: UIImage?
            if let imageURL = URL(string: item.content), let imageData = try? Data(contentsOf: MediaHandler.remapMediaURL(url: imageURL)) {
                actualImage = UIImage(data: imageData, scale: 0.7)
            }

            // Calculate image dimensions
            let maxImageWidth = imageMode == .full ? width : width * 0.7
            var imageWidth = maxImageWidth
            var imageHeight: CGFloat = 200 * scale // Default height

            if let image = actualImage {
                let aspectRatio = image.size.height / image.size.width
                imageHeight = maxImageWidth * aspectRatio

                // Limit maximum height to prevent extremely tall images
                let maxHeight = maxHeightForImage * scale
                if imageHeight > maxHeight {
                    imageHeight = maxHeight
                    imageWidth = imageHeight / aspectRatio
                }
            } else if imageSize.width > 0 && imageSize.height > 0 {
                // Use metadata dimensions if available
                let aspectRatio = imageSize.height / imageSize.width
                imageHeight = maxImageWidth * aspectRatio

                let maxHeight = maxHeightForImage * scale
                if imageHeight > maxHeight {
                    imageHeight = maxHeight
                    imageWidth = imageHeight / aspectRatio
                }
            }

            // Calculate X position based on alignment and image mode
            let textAlignment = NSTextAlignment(rawValue: journal.textAlignment) ?? .left
            var imageX: CGFloat = padding

            if imageMode == .half {
                // For half-width images, use alignment
                switch textAlignment {
                case .center:
                    imageX = padding + (width - imageWidth) / 2
                case .right:
                    imageX = padding + width - imageWidth
                default: // .left
                    imageX = padding
                }
            } else {
                imageX = padding
                imageWidth = width
            }

            let imageRect = CGRect(x: imageX, y: currentY, width: imageWidth, height: imageHeight)

            // Draw image background
            let imageBackgroundColor = Theme.Colors.cardBackground.dynamicColor()
            imageBackgroundColor.setFill()
            context.fill(imageRect)

            // Draw the actual image or placeholder
            if let image = actualImage {
                image.draw(in: imageRect)
            } else {
                // Draw placeholder
                let placeholderText = "📷 图片"
                let placeholderFont = UIFont.systemFont(ofSize: 16 * scale)
                let placeholderColor = Theme.Colors.secondaryText.dynamicColor()
                let placeholderAttributes: [NSAttributedString.Key: Any] = [
                    .font: placeholderFont,
                    .foregroundColor: placeholderColor
                ]

                let placeholderSize = placeholderText.size(withAttributes: placeholderAttributes)
                let placeholderPoint = CGPoint(
                    x: imageRect.midX - placeholderSize.width / 2,
                    y: imageRect.midY - placeholderSize.height / 2
                )
                placeholderText.draw(at: placeholderPoint, withAttributes: placeholderAttributes)
            }

            currentY += imageHeight

        case .video:
            // Get video metadata for dimensions
            let videoSize = item.size
            let videoMode = item.mode

            // Try to load video thumbnail
            var videoThumbnail: UIImage?
            if let videoURL = URL(string: item.content) {
                let remappedURL = MediaHandler.remapMediaURL(url: videoURL)
                videoThumbnail = generateVideoThumbnail(from: remappedURL)
            }

            // Calculate video dimensions
            let maxVideoWidth = videoMode == .full ? width : width * 0.7
            var videoWidth = maxVideoWidth
            var videoHeight: CGFloat = 200 * scale // Default height

            if let thumbnail = videoThumbnail {
                // Use thumbnail dimensions
                let aspectRatio = thumbnail.size.height / thumbnail.size.width
                videoHeight = maxVideoWidth * aspectRatio

                // Limit maximum height
                let maxHeight = maxHeightForImage * scale
                if videoHeight > maxHeight {
                    videoHeight = maxHeight
                    videoWidth = videoHeight / aspectRatio
                }
            } else if videoSize.width > 0 && videoSize.height > 0 {
                // Use metadata dimensions if available
                let aspectRatio = videoSize.height / videoSize.width
                videoHeight = maxVideoWidth * aspectRatio

                // Limit maximum height
                let maxHeight = maxHeightForImage * scale
                if videoHeight > maxHeight {
                    videoHeight = maxHeight
                    videoWidth = videoHeight / aspectRatio
                }
            }

            // Calculate X position based on alignment and video mode
            let textAlignment = NSTextAlignment(rawValue: journal.textAlignment) ?? .left
            var videoX: CGFloat = padding

            if videoMode == .half {
                // For half-width videos, use alignment
                switch textAlignment {
                case .center:
                    videoX = padding + (width - videoWidth) / 2
                case .right:
                    videoX = padding + width - videoWidth
                default: // .left
                    videoX = padding
                }
            } else {
                videoX = padding
                videoWidth = width
            }

            let videoRect = CGRect(x: videoX, y: currentY, width: videoWidth, height: videoHeight)

            // Draw video background
            let videoBackgroundColor = Theme.Colors.cardBackground.dynamicColor()
            videoBackgroundColor.setFill()
            context.fill(videoRect)

            // Draw the video thumbnail or placeholder
            if let thumbnail = videoThumbnail {
                // Draw thumbnail
                thumbnail.draw(in: videoRect)

                // Draw play button overlay
                let playButtonSize: CGFloat = 40 * scale
                let playButtonRect = CGRect(
                    x: videoRect.midX - playButtonSize / 2,
                    y: videoRect.midY - playButtonSize / 2,
                    width: playButtonSize,
                    height: playButtonSize
                )

                // Semi-transparent background for play button
                UIColor.black.withAlphaComponent(0.6).setFill()
                let playBackgroundPath = UIBezierPath(ovalIn: playButtonRect)
                playBackgroundPath.fill()

                // Draw play triangle
                UIColor.white.setFill()
                let triangleSize: CGFloat = 16 * scale
                let trianglePath = UIBezierPath()
                let triangleCenter = CGPoint(x: playButtonRect.midX + 2 * scale, y: playButtonRect.midY) // Slightly offset for visual balance
                trianglePath.move(to: CGPoint(x: triangleCenter.x - triangleSize/2, y: triangleCenter.y - triangleSize/2))
                trianglePath.addLine(to: CGPoint(x: triangleCenter.x - triangleSize/2, y: triangleCenter.y + triangleSize/2))
                trianglePath.addLine(to: CGPoint(x: triangleCenter.x + triangleSize/2, y: triangleCenter.y))
                trianglePath.close()
                trianglePath.fill()
            } else {
                // Draw placeholder
                let videoText = "🎥 视频"
                let videoFont = UIFont.systemFont(ofSize: 16 * scale)
                let videoColor = Theme.Colors.secondaryText.dynamicColor()
                let videoAttributes: [NSAttributedString.Key: Any] = [
                    .font: videoFont,
                    .foregroundColor: videoColor
                ]

                let videoTextSize = videoText.size(withAttributes: videoAttributes)
                let videoPoint = CGPoint(
                    x: videoRect.midX - videoTextSize.width / 2,
                    y: videoRect.midY - videoTextSize.height / 2
                )
                videoText.draw(at: videoPoint, withAttributes: videoAttributes)
            }

            currentY += videoHeight

        case .audio:
            // Draw audio placeholder
            let audioHeight: CGFloat = 60 * scale
            let audioRect = CGRect(x: padding, y: currentY, width: width, height: audioHeight)

            let audioBackgroundColor = Theme.Colors.cardBackground.dynamicColor()
            audioBackgroundColor.setFill()
            context.fill(audioRect)

            let audioText = "🎵 音频"
            let audioFont = UIFont.systemFont(ofSize: 16 * scale)
            let audioColor = Theme.Colors.secondaryText.dynamicColor()
            let audioAttributes: [NSAttributedString.Key: Any] = [
                .font: audioFont,
                .foregroundColor: audioColor
            ]

            let audioSize = audioText.size(withAttributes: audioAttributes)
            let audioPoint = CGPoint(
                x: audioRect.midX - audioSize.width / 2,
                y: audioRect.midY - audioSize.height / 2
            )
            audioText.draw(at: audioPoint, withAttributes: audioAttributes)

            currentY += audioHeight
        }

        return currentY
    }

    private func drawTagsAndLocation(in context: CGContext, startY: CGFloat, width: CGFloat, padding: CGFloat, scale: CGFloat) -> CGFloat {
        let currentY = startY

        // Draw background card
        let cardRect = CGRect(x: padding, y: currentY, width: width, height: 60 * scale)
        let cardBackgroundColor = Theme.Colors.cardBackground.dynamicColor()
        cardBackgroundColor.setFill()

        // Draw rounded rectangle
        let cardPath = UIBezierPath(roundedRect: cardRect, cornerRadius: 12 * scale)
        cardPath.fill()

        var textY = currentY + 15 * scale

        // Draw location
        if let loc = location, !loc.isEmpty {
            let locationText = "📍 \(loc)"
            let locationFont = UIFont.systemFont(ofSize: 14 * scale)
            let locationColor = Theme.Colors.text.dynamicColor()
            let locationAttributes: [NSAttributedString.Key: Any] = [
                .font: locationFont,
                .foregroundColor: locationColor
            ]

            locationText.draw(at: CGPoint(x: padding + 15 * scale, y: textY), withAttributes: locationAttributes)
            textY += 20 * scale
        }

        // Draw tags
        if !tags.isEmpty {
            let tagsText = "🏷️ \(tags.joined(separator: ", "))"
            let tagsFont = UIFont.systemFont(ofSize: 14 * scale)
            let tagsColor = Theme.Colors.text.dynamicColor()
            let tagsAttributes: [NSAttributedString.Key: Any] = [
                .font: tagsFont,
                .foregroundColor: tagsColor
            ]

            tagsText.draw(at: CGPoint(x: padding + 15 * scale, y: textY), withAttributes: tagsAttributes)
        }

        return currentY + 60 * scale
    }

    private func drawFooter(in context: CGContext, startY: CGFloat, width: CGFloat, padding: CGFloat, scale: CGFloat) {
        let footerText = "分享自意日记"
        let footerFont = UIFont.systemFont(ofSize: 16 * scale, weight: .medium)
        let footerColor = Theme.Colors.secondaryText.dynamicColor()
        let footerAttributes: [NSAttributedString.Key: Any] = [
            .font: footerFont,
            .foregroundColor: footerColor
        ]

        let footerSize = footerText.size(withAttributes: footerAttributes)
        let footerPoint = CGPoint(
            x: padding + width - footerSize.width,
            y: startY
        )
        footerText.draw(at: footerPoint, withAttributes: footerAttributes)
    }

    // swiftlint:disable:next cyclomatic_complexity
    private func calculateContentItemHeight(_ item: ContentItem, width: CGFloat, scale: CGFloat) -> CGFloat {
        switch item.type {
        case .text:
            if let attributedString = item.content.toAttributedString() {
                let scaledAttributedString = scaleAttributedString(attributedString, scale: scale)

                // Apply alignment and line height for accurate height calculation
                let mutableAttributedString = NSMutableAttributedString(attributedString: scaledAttributedString)
                let paragraphStyle = NSMutableParagraphStyle()
                paragraphStyle.alignment = NSTextAlignment(rawValue: journal.textAlignment) ?? .left
                paragraphStyle.lineHeightMultiple = journal.lineHeightMultiple
                mutableAttributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: mutableAttributedString.length))

                let boundingRect = mutableAttributedString.boundingRect(with: CGSize(width: width, height: CGFloat.greatestFiniteMagnitude), options: [.usesLineFragmentOrigin, .usesFontLeading], context: nil)
                return ceil(boundingRect.height)
            }
            return 0
        case .image:
            // Calculate actual image height based on dimensions and mode
            let imageSize = item.size
            let imageMode = item.mode
            let maxImageWidth = imageMode == .full ? width : width * 0.7
            var imageHeight: CGFloat = 200 * scale // Default height

            // Try to get actual image dimensions
            if let imageURL = URL(string: item.content), let imageData = try? Data(contentsOf: MediaHandler.remapMediaURL(url: imageURL)), let image = UIImage(data: imageData, scale: 0.5) {
                let aspectRatio = image.size.height / image.size.width
                imageHeight = maxImageWidth * aspectRatio

                // Limit maximum height
                let maxHeight = maxHeightForImage * scale
                if imageHeight > maxHeight {
                    imageHeight = maxHeight
                }
            } else if imageSize.width > 0 && imageSize.height > 0 {
                // Use metadata dimensions if available
                let aspectRatio = imageSize.height / imageSize.width
                imageHeight = maxImageWidth * aspectRatio

                let maxHeight = maxHeightForImage * scale
                if imageHeight > maxHeight {
                    imageHeight = maxHeight
                }
            }

            return imageHeight
        case .video:
            // Calculate actual video height based on dimensions and mode
            let videoSize = item.size
            let videoMode = item.mode
            let maxVideoWidth = videoMode == .full ? width : width * 0.7
            var videoHeight: CGFloat = 200 * scale // Default height

            // Try to get video thumbnail dimensions first
            if let videoURL = URL(string: item.content) {
                let remappedURL = MediaHandler.remapMediaURL(url: videoURL)
                if let thumbnail = generateVideoThumbnail(from: remappedURL) {
                    let aspectRatio = thumbnail.size.height / thumbnail.size.width
                    videoHeight = maxVideoWidth * aspectRatio

                    // Limit maximum height
                    let maxHeight = maxHeightForImage * scale
                    if videoHeight > maxHeight {
                        videoHeight = maxHeight
                    }
                    return videoHeight
                }
            }

            // Fallback to metadata dimensions if thumbnail generation fails
            if videoSize.width > 0 && videoSize.height > 0 {
                // Use metadata dimensions if available
                let aspectRatio = videoSize.height / videoSize.width
                videoHeight = maxVideoWidth * aspectRatio

                // Limit maximum height
                let maxHeight = maxHeightForImage * scale
                if videoHeight > maxHeight {
                    videoHeight = maxHeight
                }
            }

            return videoHeight
        case .audio:
            return 60 * scale
        }
    }

        // MARK: - Helper Methods
    private func scaleAttributedString(_ attributedString: NSAttributedString, scale: CGFloat) -> NSAttributedString {
        let mutableAttributedString = NSMutableAttributedString(attributedString: attributedString)
        let range = NSRange(location: 0, length: mutableAttributedString.length)
        if range.length == 0 {
            return mutableAttributedString
        }

        // Scale fonts
        mutableAttributedString.enumerateAttribute(.font, in: range, options: []) { (value, range, _) in
            if let font = value as? UIFont {
                let scaledFont = font.withSize(font.pointSize * scale)
                mutableAttributedString.addAttribute(.font, value: scaledFont, range: range)
            }
        }

        // Update paragraph styles to use journal's line height
        mutableAttributedString.enumerateAttribute(.paragraphStyle, in: range, options: []) { (value, range, _) in
            let paragraphStyle = NSMutableParagraphStyle()

            // Copy existing paragraph style if it exists
            if let existingStyle = value as? NSParagraphStyle {
                paragraphStyle.setParagraphStyle(existingStyle)
            }

            // Apply journal's line height
            paragraphStyle.lineHeightMultiple = journal.lineHeightMultiple

            mutableAttributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: range)
        }

        // If no paragraph style exists, add one with the journal's line height
        if mutableAttributedString.attribute(.paragraphStyle, at: 0, effectiveRange: nil) == nil {
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.lineHeightMultiple = journal.lineHeightMultiple
            mutableAttributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: range)
        }

        return mutableAttributedString
    }

    // MARK: - PDF Generation
    func generateSharePDF() -> Data? {
        let pageRect = CGRect(x: 0, y: 0, width: 612, height: 792) // Standard letter size
        let renderer = UIGraphicsPDFRenderer(bounds: pageRect)

        return renderer.pdfData { context in
            context.beginPage()
            drawPDFContent(in: context.cgContext, pageRect: pageRect, pdfContext: context)
        }
    }

    private func drawPDFContent(in context: CGContext, pageRect: CGRect, pdfContext: UIGraphicsPDFRendererContext) {
        let padding: CGFloat = 50
        let contentWidth = pageRect.width - (padding * 2)

        // White background
        UIColor.white.setFill()
        context.fill(pageRect)

        var currentY: CGFloat = padding

        // Draw header
        currentY = drawPDFHeader(in: context, startY: currentY, width: contentWidth, padding: padding)
        currentY += 30

        // Draw content items
        for item in contentItems {
            currentY = drawPDFContentItem(item, in: context, startY: currentY, width: contentWidth, padding: padding, pageRect: pageRect, pdfContext: pdfContext)
            currentY += 20
        }

        // Draw tags and location
        if !tags.isEmpty || !isLocationEmpty() {
            if currentY + 80 > pageRect.height - padding - 60 {
                pdfContext.beginPage()
                currentY = padding
            }
            currentY = drawPDFTagsAndLocation(in: context, startY: currentY, width: contentWidth, padding: padding)
            currentY += 30
        }

        // Draw footer
        drawPDFFooter(in: context, pageRect: pageRect, padding: padding)
    }

    private func drawPDFHeader(in context: CGContext, startY: CGFloat, width: CGFloat, padding: CGFloat) -> CGFloat {
        let currentY = startY

        // Date formatting - match image layout with large day and smaller month/year
        let dayFormatter = DateFormatter()
        dayFormatter.dateFormat = "dd"
        let dayText = dayFormatter.string(from: journal.date)

        let monthYearFormatter = DateFormatter()
        monthYearFormatter.dateFormat = "MMMM, yyyy"
        let monthYearText = monthYearFormatter.string(from: journal.date).uppercased()

        // Get text alignment from journal
        let textAlignment = NSTextAlignment.left

        // Draw day - large number like in image
        let dayFont = UIFont.boldSystemFont(ofSize: 48)
        let dayColor = UIColor.black
        let dayAttributes: [NSAttributedString.Key: Any] = [
            .font: dayFont,
            .foregroundColor: dayColor
        ]

        let daySize = dayText.size(withAttributes: dayAttributes)
        let dayX: CGFloat = padding
        dayText.draw(at: CGPoint(x: dayX, y: currentY), withAttributes: dayAttributes)

        // Draw month/year - smaller text like in image
        let monthYearFont = UIFont.systemFont(ofSize: 18)
        let monthYearColor = UIColor.gray
        let monthYearAttributes: [NSAttributedString.Key: Any] = [
            .font: monthYearFont,
            .foregroundColor: monthYearColor
        ]

        let monthYearSize = monthYearText.size(withAttributes: monthYearAttributes)
        let monthYearX: CGFloat = padding
        monthYearText.draw(at: CGPoint(x: monthYearX, y: currentY + daySize.height + 5), withAttributes: monthYearAttributes)

        // Draw mood emoji on the right like in image
        if !journal.moodEmoji.isEmpty {
            let moodFont = UIFont.systemFont(ofSize: 36)
            let moodAttributes: [NSAttributedString.Key: Any] = [.font: moodFont]
            let moodSize = journal.moodEmoji.size(withAttributes: moodAttributes)

            journal.moodEmoji.draw(at: CGPoint(x: padding + width - moodSize.width, y: currentY + 10), withAttributes: moodAttributes)
        }

        return currentY + 80
    }

    // swiftlint:disable:next function_body_length cyclomatic_complexity function_parameter_count
    private func drawPDFContentItem(_ item: ContentItem, in context: CGContext, startY: CGFloat, width: CGFloat, padding: CGFloat, pageRect: CGRect, pdfContext: UIGraphicsPDFRendererContext) -> CGFloat {
        var currentY = startY
        let textAlignment = NSTextAlignment(rawValue: journal.textAlignment) ?? .left

        switch item.type {
        case .text:
            if let attributedString = item.content.toAttributedString() {
                let mutableAttributedString = NSMutableAttributedString(attributedString: attributedString)

                // Apply journal's text color if available, otherwise use black for PDF
                let textColor: UIColor
                if let journalTextColor = UIColor(hexString: journal.textColor) {
                    textColor = journalTextColor
                } else {
                    textColor = UIColor.black
                }

                // Apply text color while preserving user formatting
                let currentRange = NSRange(location: 0, length: mutableAttributedString.length)
                mutableAttributedString.enumerateAttribute(.foregroundColor, in: currentRange, options: []) { (value, range, _) in
                    if value == nil {
                        // No color set, apply journal's default color
                        mutableAttributedString.addAttribute(.foregroundColor, value: textColor, range: range)
                    }
                }

                // Apply alignment and line height
                let paragraphStyle = NSMutableParagraphStyle()
                paragraphStyle.alignment = textAlignment
                paragraphStyle.lineHeightMultiple = journal.lineHeightMultiple
                mutableAttributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: mutableAttributedString.length))

                // Handle long text with page breaks
                currentY = drawTextWithPageBreaks(mutableAttributedString, startY: currentY, width: width, padding: padding, pageRect: pageRect, pdfContext: pdfContext, context: context)
            }

        case .image:
            // Try to load and draw the actual image first
            var imageDrawn = false
            if let imageURL = URL(string: item.content),
                let imageData = try? Data(contentsOf: MediaHandler.remapMediaURL(url: imageURL)),
                let image = UIImage(data: imageData) {

                // Get image mode and calculate size accordingly
                let imageMode = item.mode
                let maxWidth: CGFloat

                if imageMode == .full {
                    // Full width: use most of the available width
                    maxWidth = width - 20 // Small margin for full width
                } else {
                    // Half width: use 60% of available width
                    maxWidth = (width - 40) * 0.6
                }

                let aspectRatio = image.size.height / image.size.width
                let imageWidth = min(maxWidth, image.size.width)
                let imageHeight = imageWidth * aspectRatio

                // Limit height to prevent extremely tall images
                let maxHeight: CGFloat = imageMode == .full ? 500 : 300
                let finalHeight = min(imageHeight, maxHeight)
                var finalWidth = finalHeight / aspectRatio

                // Check if image fits on current page
                if currentY + finalHeight > pageRect.height - padding - 60 {
                    pdfContext.beginPage()
                    currentY = padding
                }

                // Calculate X position based on alignment and image mode - match image layout
                var imageX: CGFloat = padding

                if imageMode == .half {
                    // For half-width images, use alignment like in image layout
                    switch textAlignment {
                    case .center:
                        imageX = padding + (width - finalWidth) / 2
                    case .right:
                        imageX = padding + width - finalWidth
                    default: // .left
                        imageX = padding
                    }
                } else {
                    // Full width images use full width
                    imageX = padding
                    finalWidth = width
                }

                let imageRect = CGRect(x: imageX, y: currentY, width: finalWidth, height: finalHeight)
                image.draw(in: imageRect)
                currentY += finalHeight + 10
                imageDrawn = true
            }

            // If image couldn't be loaded, show placeholder text
            if !imageDrawn {
                // Check if placeholder fits on current page
                if currentY + 20 > pageRect.height - padding - 60 {
                    pdfContext.beginPage()
                    currentY = padding
                }

                let placeholderText = "[图片]"
                let placeholderFont = UIFont.systemFont(ofSize: 14)
                let placeholderAttributes: [NSAttributedString.Key: Any] = [
                    .font: placeholderFont,
                    .foregroundColor: UIColor.gray
                ]

                placeholderText.draw(at: CGPoint(x: padding, y: currentY), withAttributes: placeholderAttributes)
                currentY += 20
            }

        case .video:
            // Try to get video thumbnail and information for better PDF representation
            var videoDrawn = false
            var videoInfo = "[视频]"

            if let videoURL = URL(string: item.content) {
                let remappedURL = MediaHandler.remapMediaURL(url: videoURL)

                // Try to generate and draw video thumbnail
                if let thumbnail = generateVideoThumbnail(from: remappedURL) {
                    // Get video mode and calculate size accordingly
                    let videoMode = item.mode
                    let maxWidth: CGFloat

                    if videoMode == .full {
                        // Full width: use most of the available width
                        maxWidth = width - 20 // Small margin for full width
                    } else {
                        // Half width: use 60% of available width
                        maxWidth = (width - 40) * 0.6
                    }

                    let aspectRatio = thumbnail.size.height / thumbnail.size.width
                    let thumbnailWidth = min(maxWidth, thumbnail.size.width)
                    let thumbnailHeight = thumbnailWidth * aspectRatio

                    // Limit height
                    let maxHeight: CGFloat = videoMode == .full ? 500 : 300
                    let finalHeight = min(thumbnailHeight, maxHeight)
                    var finalWidth = finalHeight / aspectRatio

                    // Check if video fits on current page
                    if currentY + finalHeight > pageRect.height - padding - 60 {
                        pdfContext.beginPage()
                        currentY = padding
                    }

                    // Calculate X position based on alignment and video mode - match image layout
                    var videoX: CGFloat = padding

                    if videoMode == .half {
                        // For half-width videos, use alignment like in image layout
                        switch textAlignment {
                        case .center:
                            videoX = padding + (width - finalWidth) / 2
                        case .right:
                            videoX = padding + width - finalWidth
                        default: // .left
                            videoX = padding
                        }
                    } else {
                        // Full width videos use full width
                        videoX = padding
                        finalWidth = width
                    }

                    let thumbnailRect = CGRect(x: videoX, y: currentY, width: finalWidth, height: finalHeight)
                    thumbnail.draw(in: thumbnailRect)

                    // Draw play button overlay
                    let playButtonSize: CGFloat = 30
                    let playButtonRect = CGRect(
                        x: thumbnailRect.midX - playButtonSize / 2,
                        y: thumbnailRect.midY - playButtonSize / 2,
                        width: playButtonSize,
                        height: playButtonSize
                    )

                    // Semi-transparent background for play button
                    UIColor.black.withAlphaComponent(0.6).setFill()
                    let playBackgroundPath = UIBezierPath(ovalIn: playButtonRect)
                    playBackgroundPath.fill()

                    // Draw play triangle
                    UIColor.white.setFill()
                    let triangleSize: CGFloat = 12
                    let trianglePath = UIBezierPath()
                    let triangleCenter = CGPoint(x: playButtonRect.midX + 1, y: playButtonRect.midY)
                    trianglePath.move(to: CGPoint(x: triangleCenter.x - triangleSize/2, y: triangleCenter.y - triangleSize/2))
                    trianglePath.addLine(to: CGPoint(x: triangleCenter.x - triangleSize/2, y: triangleCenter.y + triangleSize/2))
                    trianglePath.addLine(to: CGPoint(x: triangleCenter.x + triangleSize/2, y: triangleCenter.y))
                    trianglePath.close()
                    trianglePath.fill()

                    currentY += finalHeight + 10
                    videoDrawn = true
                }

                // Get video duration using modern API
                let asset = AVAsset(url: remappedURL)
                let semaphore = DispatchSemaphore(value: 0)

                Task {
                    do {
                        let duration = try await asset.load(.duration)
                        if duration.isValid && !duration.isIndefinite {
                            let seconds = CMTimeGetSeconds(duration)
                            let minutes = Int(seconds) / 60
                            let remainingSeconds = Int(seconds) % 60
                            videoInfo = "[视频 - \(minutes):\(String(format: "%02d", remainingSeconds))]"
                        }
                    } catch {
                        // Keep default videoInfo if duration loading fails
                    }
                    semaphore.signal()
                }

                // Wait for duration loading (with timeout)
                _ = semaphore.wait(timeout: .now() + 2.0)
            }

            // If thumbnail couldn't be drawn, show text description
            if !videoDrawn {
                // Check if video placeholder fits on current page
                if currentY + 20 > pageRect.height - padding - 60 {
                    pdfContext.beginPage()
                    currentY = padding
                }

                let placeholderFont = UIFont.systemFont(ofSize: 14)
                let placeholderAttributes: [NSAttributedString.Key: Any] = [
                    .font: placeholderFont,
                    .foregroundColor: UIColor.gray
                ]

                videoInfo.draw(at: CGPoint(x: padding, y: currentY), withAttributes: placeholderAttributes)
                currentY += 20
            }

        case .audio:
            // Check if audio placeholder fits on current page
            if currentY + 20 > pageRect.height - padding - 60 {
                pdfContext.beginPage()
                currentY = padding
            }

            let placeholderText = "[音频]"
            let placeholderFont = UIFont.systemFont(ofSize: 14)
            let placeholderAttributes: [NSAttributedString.Key: Any] = [
                .font: placeholderFont,
                .foregroundColor: UIColor.gray
            ]

            placeholderText.draw(at: CGPoint(x: padding, y: currentY), withAttributes: placeholderAttributes)
            currentY += 20
        }

        return currentY
    }

    private func drawPDFTagsAndLocation(in context: CGContext, startY: CGFloat, width: CGFloat, padding: CGFloat) -> CGFloat {
        let currentY = startY

        // Draw background card like in image
        let cardRect = CGRect(x: padding, y: currentY, width: width, height: 60)
        let cardBackgroundColor = UIColor.lightGray.withAlphaComponent(0.1)
        cardBackgroundColor.setFill()

        // Draw rounded rectangle
        let cardPath = UIBezierPath(roundedRect: cardRect, cornerRadius: 12)
        cardPath.fill()

        var textY = currentY + 15

        // Draw location with emoji like in image
        if let loc = location, !loc.isEmpty {
            let locationText = "📍 \(loc)"
            let locationFont = UIFont.systemFont(ofSize: 14)
            let locationColor = UIColor.black
            let locationAttributes: [NSAttributedString.Key: Any] = [
                .font: locationFont,
                .foregroundColor: locationColor
            ]

            locationText.draw(at: CGPoint(x: padding + 15, y: textY), withAttributes: locationAttributes)
            textY += 20
        }

        // Draw tags with emoji like in image
        if !tags.isEmpty {
            let tagsText = "🏷️ \(tags.joined(separator: ", "))"
            let tagsFont = UIFont.systemFont(ofSize: 14)
            let tagsColor = UIColor.black
            let tagsAttributes: [NSAttributedString.Key: Any] = [
                .font: tagsFont,
                .foregroundColor: tagsColor
            ]

            tagsText.draw(at: CGPoint(x: padding + 15, y: textY), withAttributes: tagsAttributes)
        }

        return currentY + 60
    }

    private func drawTextWithPageBreaks(_ attributedString: NSAttributedString, startY: CGFloat, width: CGFloat, padding: CGFloat, pageRect: CGRect, pdfContext: UIGraphicsPDFRendererContext, context: CGContext) -> CGFloat {
        var currentY = startY
        let maxHeight = pageRect.height - padding - 60 // Leave space for footer

        // Create a text container for measuring and drawing
        let textContainer = NSTextContainer(size: CGSize(width: width, height: CGFloat.greatestFiniteMagnitude))
        textContainer.lineFragmentPadding = 0

        let layoutManager = NSLayoutManager()
        layoutManager.addTextContainer(textContainer)

        let textStorage = NSTextStorage(attributedString: attributedString)
        textStorage.addLayoutManager(layoutManager)

        // Get the total glyph range
        let totalGlyphRange = layoutManager.glyphRange(for: textContainer)
        var currentGlyphIndex = totalGlyphRange.location

        while currentGlyphIndex < NSMaxRange(totalGlyphRange) {
            // Calculate available height on current page
            let availableHeight = maxHeight - currentY

            if availableHeight <= 0 {
                // Start new page
                pdfContext.beginPage()
                currentY = padding
                continue
            }

            // Find how much text fits in the available space
            let tempContainer = NSTextContainer(size: CGSize(width: width, height: availableHeight))
            tempContainer.lineFragmentPadding = 0

            let tempLayoutManager = NSLayoutManager()
            tempLayoutManager.addTextContainer(tempContainer)

            // Create a substring from current position
            let remainingRange = NSRange(location: currentGlyphIndex, length: NSMaxRange(totalGlyphRange) - currentGlyphIndex)
            let remainingString = attributedString.attributedSubstring(from: remainingRange)

            let tempTextStorage = NSTextStorage(attributedString: remainingString)
            tempTextStorage.addLayoutManager(tempLayoutManager)

            // Get the range that fits
            let fittingGlyphRange = tempLayoutManager.glyphRange(for: tempContainer)

            if fittingGlyphRange.length == 0 {
                // Nothing fits, start new page
                pdfContext.beginPage()
                currentY = padding
                continue
            }

            // Draw the fitting portion
            let fittingCharacterRange = tempLayoutManager.characterRange(forGlyphRange: fittingGlyphRange, actualGlyphRange: nil)
            let originalCharacterRange = NSRange(location: remainingRange.location, length: fittingCharacterRange.length)
            let fittingText = attributedString.attributedSubstring(from: originalCharacterRange)

            let drawingRect = CGRect(x: padding, y: currentY, width: width, height: availableHeight)
            fittingText.draw(in: drawingRect)

            // Calculate the actual height used
            let usedRect = fittingText.boundingRect(with: CGSize(width: width, height: CGFloat.greatestFiniteMagnitude), options: [.usesLineFragmentOrigin, .usesFontLeading], context: nil)
            currentY += usedRect.height

            // Move to next portion
            currentGlyphIndex += fittingGlyphRange.length

            // If we've drawn everything that fits and there's more content, start new page
            if currentGlyphIndex < NSMaxRange(totalGlyphRange) && fittingGlyphRange.length < remainingRange.length {
                pdfContext.beginPage()
                currentY = padding
            }
        }

        return currentY
    }

    private func drawPDFFooter(in context: CGContext, pageRect: CGRect, padding: CGFloat) {
        let footerText = "分享自意日记"
        let footerFont = UIFont.systemFont(ofSize: 16, weight: .medium)
        let footerColor = UIColor.gray
        let footerAttributes: [NSAttributedString.Key: Any] = [
            .font: footerFont,
            .foregroundColor: footerColor
        ]

        let footerSize = footerText.size(withAttributes: footerAttributes)
        let footerPoint = CGPoint(
            x: pageRect.width - padding - footerSize.width,
            y: pageRect.height - padding
        )
        footerText.draw(at: footerPoint, withAttributes: footerAttributes)
    }
}

extension JournalShareGenerator {

    func isLocationEmpty() -> Bool {
        return location?.isEmpty ?? true
    }

    // MARK: - Video Thumbnail Generation
    private func generateVideoThumbnail(from videoURL: URL) -> UIImage? {
        let asset = AVAsset(url: videoURL)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.maximumSize = CGSize(width: 800, height: 600) // Reasonable thumbnail size

        // Use semaphore to make async call synchronous for this context
        let semaphore = DispatchSemaphore(value: 0)
        var resultImage: UIImage?

        // Try to generate thumbnail at 1 second first
        imageGenerator.generateCGImageAsynchronously(for: CMTime(seconds: 1.0, preferredTimescale: 60)) { cgImage, _, error in
            if let cgImage = cgImage {
                resultImage = UIImage(cgImage: cgImage)
                semaphore.signal()
            } else {
                // If failed at 1 second, try at the beginning
                imageGenerator.generateCGImageAsynchronously(for: CMTime.zero) { cgImage, _, error in
                    if let cgImage = cgImage {
                        resultImage = UIImage(cgImage: cgImage)
                    } else {
                        XYLog("Failed to generate video thumbnail: \(error?.localizedDescription ?? "Unknown error")")
                    }
                    semaphore.signal()
                }
            }
        }

        // Wait for the async operation to complete (with timeout)
        _ = semaphore.wait(timeout: .now() + 5.0) // 5 second timeout
        return resultImage
    }
}
