import Combine
import Foundation

class JournalDetailViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var journal: JournalEntry?
    @Published var isLoading: Bool = false
    @Published var errorWrapper: ErrorWrapper?
    @Published var isDeleted: Bool = false

    // MARK: - Private Properties
    private let journalId: String
    private let journalRepository: JournalRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init(
        journalId: String, journal: JournalEntry? = nil,
        journalRepository: JournalRepositoryProtocol = JournalRepository()
    ) {
        self.journalId = journalId
        self.journal = journal
        self.journalRepository = journalRepository
    }

    // MARK: - Public Methods
    func loadJournal() {
        isLoading = true
        let jouranl = DB.shared.getJournalEntryById(id: self.journalId)
        if jouranl != self.journal {
            self.journal = jouranl
        }
    }

    func deleteJournal() {
        DB.shared.deleteJournalEntry(id: self.journalId)
        self.isDeleted = true
    }
}

extension JournalDetailViewModel: FormatProtocol {

    var format: FontFormat {
        return FontFormat()
    }

    var disableEditing: Bool {
        return true
    }
}
