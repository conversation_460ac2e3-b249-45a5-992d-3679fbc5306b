import UIKit
import SnapKit

protocol JournalActionMenuViewControllerDelegate: AnyObject {
    func journalActionMenuDidSelectEdit()
    func journalActionMenuDidSelectDelete()
    func journalActionMenuDidSelectShare()
    func journalActionMenuDidCancel()
}

class JournalActionMenuViewController: UIViewController {

    weak var delegate: JournalActionMenuViewControllerDelegate?

    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let editButton = UIButton(type: .system)
    private let deleteButton = UIButton(type: .system)
    private let shareButton = UIButton(type: .system)
    private let cancelButton = UIButton(type: .system)

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        animateIn()
    }

    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)

        // Container view
        containerView.backgroundColor = Colors.pageBg.dynamicColor()
        containerView.layer.cornerRadius = 16
        containerView.layer.masksToBounds = true

        // Title
        titleLabel.text = "选择操作"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = Colors.text.dynamicColor()
        titleLabel.textAlignment = .center

        // Edit Button
        setupButton(editButton, title: "编辑", icon: "square.and.pencil", color: Colors.theme.dynamicColor(), action: #selector(editTapped))

        // Share Button
        setupButton(shareButton, title: "分享", icon: "square.and.arrow.up", color: Colors.theme.dynamicColor(), action: #selector(shareTapped))

        // Delete Button
        setupButton(deleteButton, title: "删除", icon: "trash", color: UIColor.systemRed, action: #selector(deleteTapped))

        // Cancel Button
        cancelButton.setTitle("取消", for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        cancelButton.setTitleColor(Colors.secondaryText.dynamicColor(), for: .normal)
        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)

        // Add tap gesture to background
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tapGesture)
    }

    private func setupButton(_ button: UIButton, title: String, icon: String, color: UIColor, action: Selector) {
        button.setTitle(title, for: .normal)
        button.setImage(UIImage(systemName: icon), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.setTitleColor(color, for: .normal)
        button.tintColor = color
        button.backgroundColor = Colors.tabBarBackground.dynamicColor()
        button.layer.cornerRadius = 12

        // Image and title layout
        var configuration = UIButton.Configuration.plain()
        configuration.imagePadding = 8
        configuration.imagePlacement = .leading
        configuration.titleAlignment = .center
        button.configuration = configuration

        button.addTarget(self, action: action, for: .touchUpInside)
    }

    private func setupConstraints() {
        view.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(editButton)
        containerView.addSubview(shareButton)
        containerView.addSubview(deleteButton)
        containerView.addSubview(cancelButton)

        containerView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(400) // Start off-screen
            make.leading.trailing.equalToSuperview().inset(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        editButton.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        shareButton.snp.makeConstraints { make in
            make.top.equalTo(editButton.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        deleteButton.snp.makeConstraints { make in
            make.top.equalTo(shareButton.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        cancelButton.snp.makeConstraints { make in
            make.top.equalTo(deleteButton.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
    }

    private func animateIn() {
        containerView.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(-20)
        }

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.view.layoutIfNeeded()
        }
    }

    private func animateOut(completion: @escaping () -> Void) {
        containerView.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(400)
        }

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn, animations: {
            self.view.layoutIfNeeded()
            self.view.backgroundColor = UIColor.clear
        }) { _ in
            completion()
        }
    }

    @objc private func editTapped() {
        animateOut { [weak self] in
            self?.dismiss(animated: false)
            self?.delegate?.journalActionMenuDidSelectEdit()
        }
    }

    @objc private func shareTapped() {
        animateOut { [weak self] in
            self?.dismiss(animated: false)
            self?.delegate?.journalActionMenuDidSelectShare()
        }
    }

    @objc private func deleteTapped() {
        animateOut { [weak self] in
            self?.dismiss(animated: false)
            self?.delegate?.journalActionMenuDidSelectDelete()
        }
    }

    @objc private func cancelTapped() {
        animateOut { [weak self] in
            self?.delegate?.journalActionMenuDidCancel()
            self?.dismiss(animated: false)
        }
    }

    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !containerView.frame.contains(location) {
            cancelTapped()
        }
    }
}
