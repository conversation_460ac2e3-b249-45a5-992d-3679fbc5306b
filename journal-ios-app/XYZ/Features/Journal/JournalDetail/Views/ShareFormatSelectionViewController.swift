import UIKit
import SnapKit

protocol ShareFormatSelectionDelegate: AnyObject {
    func shareFormatSelectionDidSelectImage()
    func shareFormatSelectionDidSelectPDF()
    func shareFormatSelectionDidCancel()
}

class ShareFormatSelectionViewController: UIViewController {

    weak var delegate: ShareFormatSelectionDelegate?

    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let imageOptionView = UIView()
    private let pdfOptionView = UIView()
    private let cancelButton = UIButton(type: .system)

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        animateIn()
    }

    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.6)

        // Container view with modern design
        containerView.backgroundColor = Colors.pageBg.dynamicColor()
        containerView.layer.cornerRadius = 20
        containerView.layer.masksToBounds = true

        // Add subtle shadow
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 10)
        containerView.layer.shadowRadius = 20
        containerView.layer.shadowOpacity = 0.1

        // Title
        titleLabel.text = "选择分享格式"
        titleLabel.font = UIFont.systemFont(ofSize: 22, weight: .bold)
        titleLabel.textColor = Colors.text.dynamicColor()
        titleLabel.textAlignment = .center

        // Subtitle
        subtitleLabel.text = "选择您希望分享的格式"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        subtitleLabel.textColor = Colors.secondaryText.dynamicColor()
        subtitleLabel.textAlignment = .center

        // Setup option views
        setupOptionView(imageOptionView,
                        title: "图片格式",
                        subtitle: "生成精美的图片",
                        icon: "photo.on.rectangle.angled",
                        action: #selector(imageOptionTapped))

        setupOptionView(pdfOptionView,
                        title: "PDF格式",
                        subtitle: "创建专业的PDF文档",
                        icon: "doc.richtext",
                        action: #selector(pdfOptionTapped))

        // Cancel Button
        cancelButton.setTitle("取消", for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        cancelButton.setTitleColor(Colors.secondaryText.dynamicColor(), for: .normal)
        cancelButton.backgroundColor = Colors.cardBackground.dynamicColor()
        cancelButton.layer.cornerRadius = 12
        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)

        // Add tap gesture to background
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tapGesture)
    }

    private func setupOptionView(_ optionView: UIView, title: String, subtitle: String, icon: String, action: Selector) {
        optionView.backgroundColor = Colors.cardBackground.dynamicColor()
        optionView.layer.cornerRadius = 16
        optionView.layer.borderWidth = 2
        optionView.layer.borderColor = UIColor.clear.cgColor

        // Add hover effect
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        optionView.addGestureRecognizer(tapGesture)
        optionView.isUserInteractionEnabled = true

        // Icon
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = Colors.theme.dynamicColor()
        iconImageView.contentMode = .scaleAspectFit

        // Title label
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = Colors.text.dynamicColor()

        // Subtitle label
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        subtitleLabel.textColor = Colors.secondaryText.dynamicColor()
        subtitleLabel.numberOfLines = 0

        // Arrow icon
        let arrowImageView = UIImageView()
        arrowImageView.image = UIImage(systemName: "chevron.right")
        arrowImageView.tintColor = Colors.secondaryText.dynamicColor()
        arrowImageView.contentMode = .scaleAspectFit

        // Add subviews
        optionView.addSubview(iconImageView)
        optionView.addSubview(titleLabel)
        optionView.addSubview(subtitleLabel)
        optionView.addSubview(arrowImageView)

        // Constraints
        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }

        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(16)
            make.top.equalToSuperview().offset(16)
            make.trailing.equalTo(arrowImageView.snp.leading).offset(-16)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.trailing.equalTo(titleLabel)
            make.bottom.equalToSuperview().offset(-16)
        }

        arrowImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }

    private func setupConstraints() {
        view.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(imageOptionView)
        containerView.addSubview(pdfOptionView)
        containerView.addSubview(cancelButton)

        containerView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(500) // Start off-screen
            make.leading.trailing.equalToSuperview().inset(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        imageOptionView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(80)
        }

        pdfOptionView.snp.makeConstraints { make in
            make.top.equalTo(imageOptionView.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(80)
        }

        cancelButton.snp.makeConstraints { make in
            make.top.equalTo(pdfOptionView.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-24)
            make.height.equalTo(50)
        }
    }

    private func animateIn() {
        containerView.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(-20)
        }

        UIView.animate(withDuration: 0.4, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.5, options: .curveEaseOut) {
            self.view.layoutIfNeeded()
        }
    }

    private func animateOut(completion: @escaping () -> Void) {
        containerView.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(500)
        }

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn, animations: {
            self.view.layoutIfNeeded()
            self.view.backgroundColor = UIColor.clear
        }) { _ in
            completion()
        }
    }

    private func addHoverEffect(to view: UIView) {
        UIView.animate(withDuration: 0.1) {
            view.transform = CGAffineTransform(scaleX: 0.98, y: 0.98)
            view.layer.borderColor = Colors.theme.dynamicColor().cgColor
        } completion: { _ in
            UIView.animate(withDuration: 0.1) {
                view.transform = .identity
                view.layer.borderColor = UIColor.clear.cgColor
            }
        }
    }

    @objc private func imageOptionTapped() {
        addHoverEffect(to: imageOptionView)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            self?.animateOut { [weak self] in
                self?.dismiss(animated: false)
                self?.delegate?.shareFormatSelectionDidSelectImage()
            }
        }
    }

    @objc private func pdfOptionTapped() {
        addHoverEffect(to: pdfOptionView)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            self?.animateOut { [weak self] in
                self?.dismiss(animated: false)
                self?.delegate?.shareFormatSelectionDidSelectPDF()
            }
        }
    }

    @objc private func cancelTapped() {
        animateOut { [weak self] in
            self?.delegate?.shareFormatSelectionDidCancel()
            self?.dismiss(animated: false)
        }
    }

    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !containerView.frame.contains(location) {
            cancelTapped()
        }
    }
}
