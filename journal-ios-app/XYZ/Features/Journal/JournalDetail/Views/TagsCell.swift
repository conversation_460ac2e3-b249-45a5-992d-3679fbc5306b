import UIKit

class TagsCell: UITableViewCell {
    private let tagsStackView = UIStackView()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear

        tagsStackView.axis = .horizontal
        tagsStackView.spacing = 8
        tagsStackView.alignment = .center

        contentView.addSubview(tagsStackView)
        tagsStackView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
    }

    func configure(tags: [String]) {
        tagsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        for tag in tags {
            let tagLabel = UILabel()
            tagLabel.text = tag
            tagLabel.font = UIFont.systemFont(ofSize: 14)
            tagLabel.textColor = .white
            tagLabel.backgroundColor = UIColor(hexString: "#3498db")
            tagLabel.layer.cornerRadius = 8
            tagLabel.layer.masksToBounds = true
            tagLabel.textAlignment = .center
            tagLabel.snp.makeConstraints { make in
                make.height.equalTo(32)
            }

            tagsStackView.addArrangedSubview(tagLabel)
            tagLabel.snp.makeConstraints { make in
                make.width.greaterThanOrEqualTo(64)
            }
        }
    }
}
