import UIKit

class JournalDetailViewConfigurator {
    static func configureViewController(journalId: String) -> UIViewController {
        // Create the view controller
        let viewController = JournalDetailViewController()
        // Create the view model
        let viewModel = JournalDetailViewModel(journalId: journalId)
        let router = JournalDetailRouter(viewController: viewController)
        viewController.viewModel = viewModel
        viewController.router = router

        return viewController
    }
}
