import Combine
import Foundation

class OnboardingViewModel: BaseService {
    @Published private(set) var onboardingCompleted = false

    // At the top of the class
    private var cancellables = Set<AnyCancellable>()

    func setGender(_ gender: Gender) {
        Task {
            try await updateProfile(
                userProfile: UserProfile(
                    gender: gender.rawValue
                ))
            saveProfileInfo(
                user: UserProfile(
                    gender: gender.rawValue
                ))
        }
    }

    func setSmokingYears(_ years: Int) {
        self.smokingYears = years
    }

    // Add property to store smoking years since it's not in UserSmokePlan
    private var smokingYears: Int = 0

    func completeOnboarding() {
        createUserSmokingPlan()
    }

    private func createUserSmokingPlan() { }
}
