import UIKit
import SnapKit
import Combine

class OnboardingViewController: BaseViewController {
    // MARK: - Properties
    private var viewModel: OnboardingViewModel!
    private var router: OnboardingRouter!
    private var cancellables = Set<AnyCancellable>()
    private var currentPage = 0
    private var pages: [UIViewController] = []

    // MARK: - UI Components
    private let pageViewController = UIPageViewController(
        transitionStyle: .scroll,
        navigationOrientation: .horizontal
    )

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupPages()
        bindViewModel()
    }

    // MARK: - Setup
    func setup(viewModel: OnboardingViewModel, router: OnboardingRouter) {
        self.viewModel = viewModel
        self.router = router
    }

    private func setupUI() {
        view.backgroundColor = Colors.pageBg.dynamicColor()

        addChild(pageViewController)
        view.addSubview(pageViewController.view)
        pageViewController.didMove(toParent: self)

        pageViewController.view.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupPages() {
        // 欢迎页
        let welcomePage = WelcomePageViewController()
        welcomePage.delegate = self

        // 性别选择页
        let genderPage = GenderSelectionViewController()
        genderPage.delegate = self

        // 吸烟年限页
        let smokingYearsPage = SmokingYearsViewController()
        smokingYearsPage.delegate = self

        // 每日吸烟量页
        let dailySmokingPage = DailySmokingViewController()
        dailySmokingPage.delegate = self

        pages = [welcomePage, genderPage, smokingYearsPage, dailySmokingPage]

        if let firstPage = pages.first {
            pageViewController.setViewControllers([firstPage], direction: .forward, animated: false)
        }
    }

    private func bindViewModel() {
        viewModel.$onboardingCompleted
            .receive(on: RunLoop.main)
            .filter { $0 }
            .sink { [weak self] _ in
                self?.router.showMainApp()
            }
            .store(in: &cancellables)
    }

    private func showAlert(error: Error) {
        self.error = error
    }

    private func navigateToNextPage() {
        currentPage += 1

        if currentPage < pages.count {
            pageViewController.setViewControllers([pages[currentPage]], direction: .forward, animated: true)
        } else {
            // 完成引导流程
            viewModel.completeOnboarding()
        }
    }
}

// MARK: - OnboardingPageDelegate
extension OnboardingViewController: OnboardingPageDelegate {
    func didTapNext() {
        navigateToNextPage()
    }

    func didSelectGender(_ gender: Gender) {
        viewModel.setGender(gender)
        navigateToNextPage()
    }

    func didSetSmokingYears(_ years: Int) {
        viewModel.setSmokingYears(years)
        navigateToNextPage()
    }

    func didSetDailySmokingAmount(_ amount: Int) {
        navigateToNextPage()
    }
}
