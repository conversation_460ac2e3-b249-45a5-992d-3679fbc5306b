import SnapKit
import UIKit

enum Gender: String {
    case male, female, unknown
}

class GenderSelectionViewController: BaseViewController {
    // MARK: - Properties

    weak var delegate: OnboardingPageDelegate?

    private var femaleImageView: UIImageView?
    private var maleImageView: UIImageView?

    private var gender: Gender = .unknown

    // MARK: - UI Components
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("select_your_gender")
        label.textColor = Colors.primary.dynamicColor()
        label.font = .systemFont(ofSize: 22, weight: .bold)
        label.textAlignment = .center
        return label
    }()

    private let subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("gender_selection_subtitle")
        label.textColor = .lightGray
        label.font = .systemFont(ofSize: 14)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private let genderButtonView: UIView = {
        let view = UIView()
        return view
    }()

    private let maleButton: UIButton = {
        let button = UIButton(type: .system)

        // Create stack view for vertical layout
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.alignment = .center
        stackView.distribution = .equalCentering
        stackView.spacing = 12
        stackView.isUserInteractionEnabled = false

        // Configure image view
        let imageView = UIImageView()
        imageView.image = UIImage(named: "male")?.withTintColor(Colors.welcomeTitle.light)
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .white
        imageView.tag = 11

        // Fix the constraint issue by setting frame size directly
        imageView.translatesAutoresizingMaskIntoConstraints = false
        // Configure label
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("male")
        label.textColor = .white
        label.font = .systemFont(ofSize: 16)

        // Add views to stack
        stackView.addArrangedSubview(imageView)
        stackView.addArrangedSubview(label)

        NSLayoutConstraint.activate([
            imageView.widthAnchor.constraint(equalToConstant: 35),
            imageView.heightAnchor.constraint(equalToConstant: 35)
        ])

        // Add stack view to button
        button.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalTo(UIEdgeInsets(top: 18, left: 12, bottom: 18, right: 18))
        }

        button.backgroundColor = Colors.buttonDisable.dynamicColor()
        button.layer.cornerRadius = 10
        return button
    }()

    private let femaleButton: UIButton = {
        let button = UIButton(type: .system)

        // Create stack view for vertical layout
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.alignment = .center
        stackView.distribution = .equalCentering
        stackView.spacing = 12
        stackView.isUserInteractionEnabled = false

        // Configure image view
        let imageView = UIImageView()
        imageView.image = UIImage(named: "female")?.withTintColor(Colors.welcomeTitle.light)
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .white
        imageView.tag = 11

        // Fix the constraint issue by setting frame size directly
        imageView.translatesAutoresizingMaskIntoConstraints = false
        // Configure label
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("female")
        label.textColor = .white
        label.font = .systemFont(ofSize: 16)

        // Add views to stack
        stackView.addArrangedSubview(imageView)
        stackView.addArrangedSubview(label)

        NSLayoutConstraint.activate([
            imageView.widthAnchor.constraint(equalToConstant: 35),
            imageView.heightAnchor.constraint(equalToConstant: 35)
        ])

        // Add stack view to button
        button.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalTo(UIEdgeInsets(top: 18, left: 12, bottom: 18, right: 18))
        }

        button.backgroundColor = Colors.buttonDisable.dynamicColor()
        button.layer.cornerRadius = 10
        button.tag = 1
        return button
    }()

    private let nextButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(XYLocalize.XYLocalize("next"), for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = Colors.buttonDisable.dynamicColor()
        button.layer.cornerRadius = 25
        button.isEnabled = false
        return button
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = Colors.pageBg.dynamicColor()

        genderButtonView.addSubview(maleButton)
        genderButtonView.addSubview(femaleButton)

        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        view.addSubview(genderButtonView)
        view.addSubview(nextButton)

        femaleImageView = femaleButton.viewWithTag(11) as? UIImageView
        maleImageView = maleButton.viewWithTag(11) as? UIImageView

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(80)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
        }

        genderButtonView.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        maleButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().offset(0)
            make.height.equalTo(120)
            make.width.equalTo(80)
        }

        femaleButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview()
            make.width.height.equalTo(maleButton)
            make.leading.equalTo(maleButton.snp.trailing).offset(50)
        }

        nextButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-50)
            make.width.equalTo(200)
            make.height.equalTo(50)
        }

        maleButton.addTarget(self, action: #selector(genderButtonTapped(_:)), for: .touchUpInside)
        femaleButton.addTarget(self, action: #selector(genderButtonTapped(_:)), for: .touchUpInside)
        nextButton.addTarget(self, action: #selector(nextButtonTapped), for: .touchUpInside)
    }

    // MARK: - Actions
    @objc private func genderButtonTapped(_ sender: UIButton) {

        gender = sender.tag == 2 ? .female : .male

        // 重置按钮状态
        maleButton.backgroundColor = Colors.buttonDisable.dynamicColor()
        femaleButton.backgroundColor = Colors.buttonDisable.dynamicColor()

        // 设置选中按钮状态
        sender.backgroundColor = Colors.themeColorGreen.dynamicColor()

        // 启用下一步按钮
        nextButton.isEnabled = true
        nextButton.backgroundColor = Colors.themeColorGreen.dynamicColor()
    }

    @objc private func nextButtonTapped() {
        delegate?.didSelectGender(gender)
    }

    override func themeDidChange(interfaceStyle: UIUserInterfaceStyle) {
        femaleImageView?.image = UIImage(named: "female")?.withTintColor(
            Colors.welcomeTitle.light)
        maleImageView?.image = UIImage(named: "male")?.withTintColor(
            Colors.welcomeTitle.light
        )
    }

}
