import SnapKit
import UIKit

protocol OnboardingPageDelegate: AnyObject {
    func didTapNext()
    func didSelectGender(_ gender: Gender)
    func didSetSmokingYears(_ years: Int)
    func didSetDailySmokingAmount(_ amount: Int)
}

class WelcomePageViewController: BaseViewController {
    // MARK: - Properties
    weak var delegate: OnboardingPageDelegate?
    private var animationItems: [UIView] = []

    // MARK: - UI Components
    private let backgroundGradientView: UIView = {
        let view = UIView()
        return view
    }()

    private let logoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.image = UIImage(named: "Logo")
        imageView.layer.cornerRadius = 30
        imageView.clipsToBounds = true
        imageView.alpha = 0
        imageView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        return imageView
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("welcome_to_journal")
        label.textColor = Colors.textPrimary.dynamicColor()
        label.font = .systemFont(ofSize: 28, weight: .bold)
        label.textAlignment = .center
        label.alpha = 0
        label.transform = CGAffineTransform(translationX: 0, y: 20)
        return label
    }()

    private let subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("journal_slogan")
        label.textColor = Colors.secondaryText.dynamicColor()
        label.font = .systemFont(ofSize: 18)
        label.textAlignment = .center
        label.numberOfLines = 0
        label.alpha = 0
        label.transform = CGAffineTransform(translationX: 0, y: 20)
        return label
    }()

    private let startButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("注册/登录", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(red: 76 / 255, green: 175 / 255, blue: 80 / 255, alpha: 1)
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .semibold)
        button.layer.cornerRadius = 25
        button.alpha = 0
        button.transform = CGAffineTransform(translationX: 0, y: 30)
        return button
    }()

    private let skipButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("跳过，稍后注册", for: .normal)
        button.setTitleColor(UIColor.white.withAlphaComponent(0.8), for: .normal)
        button.backgroundColor = .clear
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.white.withAlphaComponent(0.5).cgColor
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.alpha = 0
        button.transform = CGAffineTransform(translationX: 0, y: 30)
        return button
    }()

    private let decorationView1: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.1)
        view.layer.cornerRadius = 150
        view.alpha = 0
        return view
    }()

    private let decorationView2: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.05)
        view.layer.cornerRadius = 150
        view.alpha = 0
        return view
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        hideNavigationBar = true
        setupUI()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        animateElements()
    }

    // MARK: - Setup
    private func setupUI() {
        // Setup background gradient
        view.addSubview(backgroundGradientView)
        backgroundGradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        //        applyGradient(to: backgroundGradientView)

        // Add decoration views
        view.addSubview(decorationView1)
        view.addSubview(decorationView2)

        decorationView1.snp.makeConstraints { make in
            make.width.height.equalTo(300)
            make.leading.equalTo(view.snp.trailing).offset(-200)
            make.bottom.equalTo(view.snp.top).offset(200)
        }

        decorationView2.snp.makeConstraints { make in
            make.width.height.equalTo(300)
            make.top.equalTo(view.snp.bottom).offset(-270)
            make.trailing.equalTo(view.snp.leading).offset(200)
        }

        // Add main UI elements
        view.addSubview(logoImageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        view.addSubview(startButton)

        logoImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(100)
            make.width.height.equalTo(120)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(logoImageView.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }

        startButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-120)
            make.width.equalTo(220)
            make.height.equalTo(50)
        }

        // Add skip button
        view.addSubview(skipButton)
        skipButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(startButton.snp.bottom).offset(15)
            make.width.equalTo(220)
            make.height.equalTo(50)
        }

        // Apply gradient to button
        applyButtonGradient(to: startButton)

        // Add button actions
        startButton.addTarget(self, action: #selector(startButtonTapped), for: .touchUpInside)
        skipButton.addTarget(self, action: #selector(skipButtonTapped), for: .touchUpInside)

        // Add shadow to button
        startButton.layer.shadowColor = UIColor.black.cgColor
        startButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        startButton.layer.shadowRadius = 8
        startButton.layer.shadowOpacity = 0.2

        // Collect items for animation
        animationItems = [logoImageView, titleLabel, subtitleLabel, startButton, skipButton, decorationView1, decorationView2]
    }

    private func applyGradient(to view: UIView) {
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = self.view.bounds
        gradientLayer.colors = [
            UIColor(hexString: "#4E71FF")!.cgColor,
            UIColor(hexString: "#8DD8FF")!.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 1.0)
        view.layer.insertSublayer(gradientLayer, at: 0)
    }

    private func applyButtonGradient(to button: UIButton) {
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = CGRect(x: 0, y: 0, width: 220, height: 50)
        gradientLayer.cornerRadius = 25
        gradientLayer.colors = [
            UIColor(hexString: "#4E71FF")!.cgColor,
            UIColor(hexString: "#8DD8FF")!.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        button.layer.insertSublayer(gradientLayer, at: 0)
    }

    private func animateElements() {
        // Animate decoration views
        UIView.animate(withDuration: 1.0, delay: 0.1, options: [.curveEaseOut], animations: {
            self.decorationView1.alpha = 1
            self.decorationView2.alpha = 1
        })

        // Animate logo
        UIView.animate(withDuration: 0.8, delay: 0.2, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.5, options: .curveEaseOut, animations: {
            self.logoImageView.alpha = 1
            self.logoImageView.transform = .identity
        })

        // Animate title
        UIView.animate(withDuration: 0.8, delay: 0.4, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut, animations: {
            self.titleLabel.alpha = 1
            self.titleLabel.transform = .identity
        })

        // Animate subtitle
        UIView.animate(withDuration: 0.8, delay: 0.6, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut, animations: {
            self.subtitleLabel.alpha = 1
            self.subtitleLabel.transform = .identity
        })

        // Animate button
        UIView.animate(withDuration: 0.8, delay: 0.8, usingSpringWithDamping: 0.7, initialSpringVelocity: 0, options: .curveEaseOut, animations: {
            self.startButton.alpha = 1
            self.startButton.transform = .identity
        })
    }

    // MARK: - Actions
    @objc private func startButtonTapped() {
        // Add button press animation
        UIView.animate(withDuration: 0.1, animations: {
            self.startButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.startButton.transform = .identity
            } completion: { _ in
                self.goToLoginScreen()
            }
        }
    }

    @objc private func skipButtonTapped() {
        // Add button press animation
        UIView.animate(withDuration: 0.1, animations: {
            self.skipButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.skipButton.transform = .identity
            } completion: { _ in
                self.goToMainApp()
            }
        }
    }

    private func goToLoginScreen() {
        let login = LoginScreenViewController(nibName: nil, bundle: nil)
        self.navigationController?.pushViewController(login)
    }

    private func goToMainApp() {
        // Set guest mode and dismiss to main app
        App.shared.isGuestMode = true
        self.navigationController?.dismiss(animated: true)
    }
}
