import SnapKit
import UIKit

class DailySmokingViewController: UIViewController {
    // MARK: - Properties
    weak var delegate: OnboardingPageDelegate?
    private var selectedAmount: Int = 10

    // MARK: - UI Components
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("daily_smoking_question")
        label.textColor = Colors.primary.dynamicColor()
        label.font = .systemFont(ofSize: 22, weight: .bold)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private let subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("daily_smoking_subtitle")
        label.textColor = .lightGray
        label.font = .systemFont(ofSize: 14)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private let amountLabel: UILabel = {
        let label = UILabel()
        label.text = "10"
        label.textColor = Colors.primary.dynamicColor()
        label.font = .systemFont(ofSize: 36, weight: .bold)
        label.textAlignment = .center
        return label
    }()

    private let unitLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("cigarettes_per_day")
        label.textColor = Colors.primary.dynamicColor()
        label.font = .systemFont(ofSize: 16)
        label.textAlignment = .center
        return label
    }()

    private let slider: UISlider = {
        let slider = UISlider()
        slider.minimumValue = 1
        slider.maximumValue = 40
        slider.value = 10
        slider.tintColor = UIColor(red: 76 / 255, green: 175 / 255, blue: 80 / 255, alpha: 1)
        return slider
    }()

    private let nextButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(XYLocalize.XYLocalize("finish"), for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(red: 76 / 255, green: 175 / 255, blue: 80 / 255, alpha: 1)
        button.layer.cornerRadius = 25
        return button
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = Colors.pageBg.dynamicColor()

        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        view.addSubview(amountLabel)
        view.addSubview(unitLabel)
        view.addSubview(slider)
        view.addSubview(nextButton)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(80)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
        }

        amountLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }

        unitLabel.snp.makeConstraints { make in
            make.top.equalTo(amountLabel.snp.bottom).offset(5)
            make.centerX.equalToSuperview()
        }

        slider.snp.makeConstraints { make in
            make.top.equalTo(unitLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(40)
        }

        nextButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-50)
            make.width.equalTo(200)
            make.height.equalTo(50)
        }

        slider.addTarget(self, action: #selector(sliderValueChanged), for: .valueChanged)
        nextButton.addTarget(self, action: #selector(nextButtonTapped), for: .touchUpInside)
    }

    // MARK: - Actions
    @objc private func sliderValueChanged(_ sender: UISlider) {
        selectedAmount = Int(sender.value)
        amountLabel.text = "\(selectedAmount)"
    }

    @objc private func nextButtonTapped() {
        delegate?.didSetDailySmokingAmount(selectedAmount)
    }
}
