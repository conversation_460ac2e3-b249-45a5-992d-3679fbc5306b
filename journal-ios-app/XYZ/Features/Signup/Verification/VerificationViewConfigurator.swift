//
//  VerificationViewConfigurator.swift

//
//  Created by <PERSON> on 2025/2/19.
//

import UIKit

class VerificationViewConfigurator {

    func getViewController(config: VerificationViewModel.Configuration) -> UIViewController {
        let viewModel = VerificationViewModel(
            config: config
        )
        let viewController = VerificationViewController(viewModel: viewModel)
        return viewController
    }
}
