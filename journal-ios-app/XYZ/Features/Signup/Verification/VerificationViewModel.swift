//
//  VerificationViewModel.swift

//
//  Created by <PERSON> on 2025/1/11.
//
import Alamofire
import Combine

struct EmailVerificationResult: Decodable {
    var token: String
    var type: String
}

class VerificationViewModel: ObservableObject, BaseService {

    struct Configuration {
        var email: String
    }

    let emailVerificationPath = "auth/code"
    let loginWithCode = "auth/loginWithCode"
    var config: VerificationViewModel.Configuration!

    @Published var verificationCode: String = ""
    @Published var isVerificationEnabled: Bool = false
    @Published var codeDigits: [String] = Array(repeating: "", count: 6)

    private var cancellables = Set<AnyCancellable>()

    init(config: VerificationViewModel.Configuration) {

        self.config = config

        $verificationCode
            .map { $0.count == 6 }  // Enable button only when code has 4 digits
            .assign(to: \.isVerificationEnabled, on: self)
            .store(in: &cancellables)
    }

    func appendCode(_ digit: String) {
        // Handle multi-digit input by splitting it into individual characters
        let characters = Array(digit)

        // Add each character to the codeDigits array
        for char in characters {
            // Stop if we've filled all available slots
            guard codeDigits.contains("") else { break }

            if let index = codeDigits.firstIndex(of: "") {
                codeDigits[index] = String(char)
            }
        }

        verificationCode = codeDigits.filter({ $0 != "" }).joined()
    }

    func removeLastDigit() {
        if let lastFilledIndex = codeDigits.lastIndex(where: { !$0.isEmpty }) {
            codeDigits[lastFilledIndex] = ""
        }
        verificationCode = codeDigits.filter({ $0 != "" }).joined()
    }

    func verifyCode() async throws -> EmailVerificationResult {
        let parameters: Parameters = ["code": verificationCode, "email": config.email]
        let emailVerificationResult: EmailVerificationResult = try await APIClient.shared
            .sendRequestAsync(
                requestModel: RequestModel(
                    path: loginWithCode, parameters: parameters, method: .post)
            )
        return emailVerificationResult
    }

    func sendEmailVerificationCode() async throws -> SendVerificationResult {
        return try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: emailVerificationPath,
                    parameters: [
                        "email": config.email
                    ],
                    method: .post
                )
        )
    }

}
