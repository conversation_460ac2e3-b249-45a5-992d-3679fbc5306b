//
//  VerificationViewController.swift

//
//  Created by <PERSON> on 2025/1/10.
//

import Combine
import CombineCocoa
import Foundation
import SnapKit
import UIKit

class VerificationViewController: BaseViewController {

    private var viewModel: VerificationViewModel!
    private var cancellables = Set<AnyCancellable>()
    private var isResendEnabled = true
    private var countdownTimer: Timer?
    private var countdownSeconds: Int = 60

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .boldSystemFont(ofSize: 20)
        label.textColor = Colors.primary.dynamicColor()
        return label
    }()

    private let subtitleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .gray
        label.numberOfLines = 0
        label.textAlignment = .center
        label.textColor = Colors.secondary.dynamicColor()
        return label
    }()

    private var codeLabels: [UILabel] = []  // Use labels instead of text fields
    private let codeStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 10
        stackView.distribution = .fillEqually  // Equal distribution
        return stackView
    }()

    private let verifyButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle(XYLocalize.XYLocalize("verify_now"), for: .normal)
        button.backgroundColor = Colors.themeColor.dynamicColor()
        button.setTitleColor(.gray, for: .normal)
        button.layer.cornerRadius = 8
        return button
    }()

    private let resendLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .gray
        label.numberOfLines = 0
        label.textAlignment = .center
        label.isUserInteractionEnabled = true
        return label
    }()

    private let invisibleTextField: UITextField = {  // Invisible text field
        let textField = UITextField()
        textField.keyboardType = .numberPad
        textField.isHidden = true  // Make it invisible
        return textField
    }()

    init(viewModel: VerificationViewModel) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }

    @MainActor required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setUpLocalize()
        setupConstraints()
        setupBindings()
        startCountdownTimer()
    }

    func setUpLocalize() {
        titleLabel.text = XYLocalize.XYLocalize("enter_verification_code")
        subtitleLabel.text =
            "\(XYLocalize.XYLocalize("email_has_sent_to")) \(viewModel.config.email)"

        let fullText =
            "\(XYLocalize.XYLocalize("did_not_receive_code_question")) \(XYLocalize.XYLocalize("did_not_receive_code_resend"))"
        let attributedString = NSMutableAttributedString(string: fullText)
        attributedString.addAttribute(
            .foregroundColor, value: Colors.themeColor.dynamicColor(),
            range: (fullText as NSString).range(
                of: XYLocalize.XYLocalize("did_not_receive_code_resend")))
        resendLabel.attributedText = attributedString
    }

    private func setupUI() {
        view.backgroundColor = Colors.pageBg.dynamicColor()

        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        view.addSubview(codeStackView)
        view.addSubview(verifyButton)
        view.addSubview(resendLabel)
        view.addSubview(invisibleTextField)

        // 验证码输入框样式
        for _ in 0..<6 {
            let label = UILabel()
            label.textAlignment = .center
            label.font = .systemFont(ofSize: 24, weight: .bold)
            label.textColor = Colors.primary.dynamicColor()
            label.backgroundColor = .clear

            let container = UIView()
            container.backgroundColor = .clear
            container.layer.borderColor = UIColor.gray.cgColor
            container.layer.borderWidth = 0.5
            container.layer.cornerRadius = 8

            container.addSubview(label)
            label.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }

            codeLabels.append(label)
            codeStackView.addArrangedSubview(container)

            container.snp.makeConstraints { make in
                make.size.equalTo(40)
            }
        }

        verifyButton.backgroundColor = Colors.themeColor.dynamicColor()
        verifyButton.setTitleColor(.white, for: .normal)
        verifyButton.layer.cornerRadius = 8

        invisibleTextField.delegate = self
        invisibleTextField.becomeFirstResponder()

        // Add tap gesture recognizer to resendLabel, enable resend when one minute has passed
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleResendTap))
        resendLabel.addGestureRecognizer(tapGesture)

        // Initial setup - disable resend until one minute has passed after initial send
        isResendEnabled = false
    }

    // MARK: - Resend Logic
    private func resendEmailVerificationCode() {
        Task {
            self.isLoadingParts = true
            do {
                _ = try await self.viewModel.sendEmailVerificationCode()
            } catch {
                XYLog(error)
            }
            self.isLoadingParts = false
        }
    }

    private func setupConstraints() {

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(16)
            make.centerX.equalToSuperview()
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(30)
        }

        codeStackView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(40)
            make.centerX.equalToSuperview()
            make.height.equalTo(40)
        }

        verifyButton.snp.makeConstraints { make in
            make.top.equalTo(codeStackView.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(50)
        }

        resendLabel.snp.makeConstraints { make in
            make.top.equalTo(verifyButton.snp.bottom).offset(16)
            make.left.equalTo(verifyButton)
            make.centerX.equalToSuperview()
        }
    }

    private func setupBindings() {
        viewModel.$isVerificationEnabled
            .assign(to: \.isEnabled, on: verifyButton)
            .store(in: &cancellables)

        viewModel.$isVerificationEnabled
            .map {
                $0
                    ? Colors.theme.dynamicColor()
                    : Colors.buttonDisable.dynamicColor()
            }
            .assign(to: \.backgroundColor, on: verifyButton)
            .store(in: &cancellables)

        verifyButton.tapPublisher
            .sink { [weak self] () in
                guard let self = self else { return }
                self.verifyButtonClicked()
            }
            .store(in: &cancellables)

        viewModel.$codeDigits
            .sink { [weak self] digits in
                guard let self = self else { return }
                for (index, digit) in digits.enumerated() {
                    self.codeLabels[index].text = digit
                }
            }
            .store(in: &cancellables)
    }

    func startCountdownTimer() {
        countdownSeconds = 60
        countdownTimer?.invalidate()
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { [weak self] timer in
            guard let self = self else { return }
            self.countdownSeconds -= 1
            if self.countdownSeconds <= 0 {
                self.isResendEnabled = true
                timer.invalidate()
                self.updateResendState()
            } else {
                self.showCountdownTimer(seconds: countdownSeconds)
            }
        }
    }

    @objc private func handleResendTap() {
        // Check if resend is enabled
        guard isResendEnabled else {
            return
        }

        isResendEnabled = false
        resendEmailVerificationCode()
        startCountdownTimer()
    }

    private func updateResendState() {
        if isResendEnabled {
            self.setUpLocalize()
        }
    }

    private func showCountdownTimer(seconds: Int) {
        // Update label with countdown timer
        resendLabel.text =
            "\(XYLocalize.XYLocalize("did_not_receive_code_question")) \(seconds)\(XYLocalize.XYLocalize("retry_after_seconds"))"
    }

    private func verifyButtonClicked() {
        Task {
            self.isLoadingParts = true
            do {
                let result = try await self.viewModel.verifyCode()
                self.viewModel.didObtainAuthToken(token: result.token)

                let userInfo = try await self.viewModel.fetchProfile()
                self.viewModel.saveProfileInfo(user: userInfo)

                showMainContentPage()

            } catch {
                XYLog(error)
                self.error = XYError.raw("验证失败，请重试", 0)
            }
            self.isLoadingParts = false
        }
    }

    private func rectForCharacterRange(
        _ range: NSRange, in label: UILabel, attributedText: NSAttributedString
    ) -> CGRect {
        let layoutManager = NSLayoutManager()
        let textContainer = NSTextContainer(size: label.bounds.size)
        let textStorage = NSTextStorage(attributedString: attributedText)

        layoutManager.addTextContainer(textContainer)
        textStorage.addLayoutManager(layoutManager)
        textContainer.lineFragmentPadding = 0  // Important for accurate calculations

        let glyphRange = layoutManager.glyphRange(
            forCharacterRange: range, actualCharacterRange: nil)
        let glyphRect = layoutManager.boundingRect(forGlyphRange: glyphRange, in: textContainer)
        return glyphRect  // Use glyphRect directly
    }

    func showMainContentPage() {
        MainConfigurator().showMainContentPage(from: self)
    }

    override func themeDidChange(interfaceStyle: UIUserInterfaceStyle) {
        setUpLocalize()
    }
}

extension VerificationViewController: UITextFieldDelegate {
    func textField(
        _ textField: UITextField, shouldChangeCharactersIn range: NSRange,
        replacementString string: String
    ) -> Bool {
        guard string.allSatisfy(\.isNumber) || string.isEmpty else { return false }
        if !string.isEmpty, viewModel.verificationCode.count < 6 {
            viewModel.appendCode(string)  // Update the viewModel
        } else if string.isEmpty, !viewModel.verificationCode.isEmpty {
            viewModel.removeLastDigit()  // Handle backspace
        }

        return true
    }
}
