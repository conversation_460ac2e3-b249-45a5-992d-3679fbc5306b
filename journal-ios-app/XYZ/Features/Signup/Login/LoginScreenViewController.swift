//
//  L.swift

//
//  Created by <PERSON> on 2025/1/7.
//

import AuthenticationServices
import Combine
import GoogleSignIn
import SnapKit
import UIKit

class LoginScreenViewController: BaseViewController {

    var viewModel = LoginScreenViewModel()

    private var cancellables = Set<AnyCancellable>()  // For Combine

    private let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = true
        scrollView.alwaysBounceVertical = true
        scrollView.keyboardDismissMode = .interactive
        return scrollView
    }()

    private let contentView: UIView = {
        let view = UIView()
        return view
    }()

    private let logoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "Logo")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("continue_using_the_app")
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 24, weight: .semibold)
        label.textColor = Theme.Colors.primary.dynamicColor()
        return label
    }()

    private let emailTextField: UITextField = {
        let textField = UITextField()
        textField.configure(
            cornerRadius: 12,
            placeholder: XYLocalize.XYLocalize("input_your_email_address"),
            backgroundColor: Theme.Colors.lightBackGround.dynamicColor(),
            keyboardType: .emailAddress
        )
        #if DEBUG
            textField.text = "<EMAIL>"
        #endif

        // Configure left icon view
        let iconSize = CGSize(width: 20, height: 20)
        let containerWidth: CGFloat = 44
        let mailIcon = UIImage(systemName: "envelope")?.withTintColor(.systemGray)
        let iconView = UIImageView(image: mailIcon)
        iconView.contentMode = .scaleAspectFit
        iconView.frame = CGRect(origin: .zero, size: iconSize)
        let container = UIView(
            frame: CGRect(x: 0, y: 0, width: containerWidth, height: iconSize.height))
        container.addSubview(iconView)
        iconView.center = container.center
        textField.leftView = container
        textField.leftViewMode = .always

        // Styling
        textField.layer.borderColor = Theme.Colors.theme.dynamicColor().withAlphaComponent(0.2).cgColor
        textField.layer.borderWidth = 1
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.setLeftPaddingPoints(16)
        textField.setRightPaddingPoints(16)
        return textField
    }()

    private let continueButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(XYLocalize.XYLocalize("send_email_verification_button"), for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        button.backgroundColor = Theme.Colors.theme.light
        button.layer.cornerRadius = 12
        return button
    }()

    private let orLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("or_login_with")
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = Theme.Colors.secondary.dynamicColor()
        return label
    }()

    private let googleSignInButton: CenteredImageTitleButton = {
        let button = CenteredImageTitleButton()
        button.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        button.title = XYLocalize.XYLocalize("sign_in_with_google")
        button.image = UIImage(named: "google")
        return button
    }()

    let appleSignInButton: CenteredImageTitleButton = {
        let button = CenteredImageTitleButton()
        button.backgroundColor = .black
        button.title = XYLocalize.XYLocalize("sign_in_with_apple")
        button.image = UIImage(systemName: "apple.logo")?.withTintColor(.white)
        button.imageSize = .init(width: 25, height: 25)
        button.textColor = .white
        button.layer.cornerRadius = 12
        return button
    }()

    private let skipButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(XYLocalize.XYLocalize("skip"), for: .normal)
        button.setTitleColor(Theme.Colors.secondaryText.dynamicColor(), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        return button
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        hideNavigationBar = true
        view.backgroundColor = Theme.Colors.background.dynamicColor()
        setupViews()
        setupConstraints()
        setupKeyboardHandling()

        continueButton.addTarget(self, action: #selector(buttonTouchDown), for: .touchDown)
        continueButton.addTarget(self, action: #selector(buttonTouchUp), for: [.touchUpInside, .touchUpOutside, .touchCancel])
        continueButton.addTarget(
            self,
            action: #selector(loginTapped),
            for: .touchUpInside
        )
        appleSignInButton.addTarget(
            self,
            action: #selector(handleAuthorizationAppleIDButtonPress),
            for: .touchUpInside)
        googleSignInButton.addTarget(
            self,
            action: #selector(handleAuthorizationGoogleButtonPress),
            for: .touchUpInside
        )
        skipButton.addTarget(self, action: #selector(skipButtonTapped), for: .touchUpInside)

        setupBindings()
    }

    private func setupBindings() {
        let isEmailValidPublisher = emailTextField.publisher(for: \.text)
            .map { $0?.isValidEmail() ?? false }
            .eraseToAnyPublisher()

        isEmailValidPublisher
            .receive(on: RunLoop.main)
            .map({ enabled in
                enabled ? Theme.Colors.theme.light : Theme.Colors.buttonDisable.dynamicColor()
            })
            .assign(to: \.backgroundColor, on: continueButton)
            .store(in: &cancellables)

        // Add textDidChange target for immediate updates
        emailTextField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
    }

    // Email validation function (can be improved)
    private func isValidEmail(email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format: "SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: email)
    }

    @objc func loginTapped() {
        guard let email = emailTextField.text, !email.isEmpty else {
            self.error = XYError.raw("请收入有效的邮箱地址", 0)
            return
        }

        Task {
            do {
                self.isLoadingParts = true
                try await viewModel.sendEmailVerificationCode(email: email)

                let verification = VerificationViewConfigurator().getViewController(
                    config: VerificationViewModel.Configuration(email: email)
                )

                self.isLoadingParts = false
                navigationController?.pushViewController(verification)

            } catch {
                XYLog(error)
                self.isLoadingParts = false
                self.error = error
                return
            }
        }
    }

    @objc func handleAuthorizationAppleIDButtonPress() {
        let appleIDProvider = ASAuthorizationAppleIDProvider()
        let request = appleIDProvider.createRequest()
        request.requestedScopes = [.fullName, .email]

        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }

    @objc func handleAuthorizationGoogleButtonPress() {
        GIDSignIn.sharedInstance.signIn(withPresenting: self) { [weak self] signInResult, error in
            guard error == nil else {
                return
            }
            guard let self = self else {
                return
            }

            if let serverAuthCode = signInResult?.serverAuthCode {
                self.validateGoogleAuthCode(serverAuthCode: serverAuthCode)
            }
        }
    }

    private func setupViews() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        scrollView.setBasicContentInsets()

        contentView.addSubview(logoImageView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(emailTextField)
        contentView.addSubview(continueButton)
        contentView.addSubview(orLabel)
        contentView.addSubview(googleSignInButton)
        contentView.addSubview(appleSignInButton)

        view.addSubview(skipButton)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
            make.bottom.equalToSuperview()
        }

        logoImageView.snp.makeConstraints { make in
            make.top.equalTo(contentView).offset(60)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(logoImageView.snp.bottom).offset(15)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
        }

        emailTextField.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(15)
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(50)
        }

        continueButton.snp.makeConstraints { make in
            make.top.equalTo(emailTextField.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(50)
        }

        orLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.greaterThanOrEqualTo(continueButton.snp.bottom).offset(20)
        }

        appleSignInButton.snp.makeConstraints { make in
            make.top.equalTo(orLabel.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(50)
        }

        googleSignInButton.snp.makeConstraints { make in
            make.top.equalTo(appleSignInButton.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(50)
            make.bottom.equalTo(contentView).offset(-20)
        }

        skipButton.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(50)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-20)
        }
    }

    private func setupKeyboardHandling() {
        NotificationCenter.default.addObserver(
            self, selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(
            self, selector: #selector(keyboardWillHide),
            name: UIResponder.keyboardWillHideNotification, object: nil)
    }

    @objc private func keyboardWillShow(_ notification: Notification) {
        guard
            let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey]
                as? CGRect
        else {
            return
        }

        let keyboardHeight = keyboardFrame.height
        let bottomInset = keyboardHeight - view.safeAreaInsets.bottom

        // Add bottom inset to scroll view
        scrollView.contentInset.bottom = bottomInset
        scrollView.verticalScrollIndicatorInsets.bottom = bottomInset

        // Scroll to make the email field visible if it's the first responder
        if emailTextField.isFirstResponder {
            let rect = emailTextField.convert(emailTextField.bounds, to: scrollView)
            scrollView.scrollRectToVisible(rect, animated: true)
        }
    }

    @objc private func keyboardWillHide(_ notification: Notification) {
        // Reset scroll view insets
        scrollView.contentInset.bottom = 0
        scrollView.verticalScrollIndicatorInsets.bottom = 0
    }

    func validateAuthCode(data: Data) {
        Task {
            self.isLoadingParts = true
            do {
                let result = try await self.viewModel.validateSiwaAuthCode(data: data)
                self.viewModel.didObtainAuthToken(token: result.token)

                let userInfo = try await self.viewModel.fetchProfile()
                self.viewModel.saveProfileInfo(user: userInfo)
                self.showMainContentPage()

                self.isLoadingParts = false
            } catch {
                self.error = XYError.raw("登陆失败，请重试", 0)
            }
            self.isLoadingParts = false
        }
    }

    func validateGoogleAuthCode(serverAuthCode: String) {
        Task {
            self.isLoadingParts = true
            do {
                let result = try await self.viewModel.validateGoogleAuthCode(code: serverAuthCode)
                self.viewModel.didObtainAuthToken(token: result.token)

                let userInfo = try await self.viewModel.fetchProfile()
                self.viewModel.saveProfileInfo(user: userInfo)
                self.showMainContentPage()
            } catch {
                self.error = XYError.raw("登陆失败，请重试", 0)
            }
            self.isLoadingParts = false
        }
    }

    func showMainContentPage() {
        MainConfigurator().showMainContentPage(from: self)
    }

    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        view.endEditing(true)
    }

    override func themeDidChange(interfaceStyle: UIUserInterfaceStyle) {
        // MARK: Change theme style
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesEnded(touches, with: event)
        view.endEditing(true)
    }

    // Add these methods to handle button animations
    @objc private func buttonTouchDown() {
        UIView.animate(withDuration: 0.08) {
            self.continueButton.transform = CGAffineTransform(scaleX: 0.97, y: 0.97)
        }
    }

    @objc private func buttonTouchUp() {
        UIView.animate(withDuration: 0.12) {
            self.continueButton.transform = .identity
        }
    }

    @objc private func textFieldDidChange() {
        let isValid = emailTextField.text?.isValidEmail() ?? false
        continueButton.backgroundColor = isValid ? Theme.Colors.theme.light : Theme.Colors.buttonDisable.dynamicColor()
    }

    @objc private func skipButtonTapped() {
        self.navigationController?.dismiss(animated: true)
    }
}

// MARK: - ASAuthorizationControllerDelegate

extension LoginScreenViewController: ASAuthorizationControllerDelegate {
    func authorizationController(
        controller: ASAuthorizationController,
        didCompleteWithAuthorization authorization: ASAuthorization
    ) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            let userIdentifier = appleIDCredential.user
            let fullName = appleIDCredential.fullName
            let email = appleIDCredential.email

            // Handle sign-in with Apple (send user data to server or proceed with app logic)
            XYLog("User ID: \(userIdentifier)")
            XYLog("Full Name: \(fullName?.givenName ?? "") \(fullName?.familyName ?? "")")
            XYLog("Email: \(email ?? "")")

            if let authorizeCode = appleIDCredential.authorizationCode {
                self.validateAuthCode(data: authorizeCode)
            } else {

            }
        }
    }

    func authorizationController(
        controller: ASAuthorizationController, didCompleteWithError error: Error
    ) {
        XYLog("Authorization failed: \(error.localizedDescription)")
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding

extension LoginScreenViewController: ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        return view.window!
    }
}

extension UITextField {
    fileprivate func configure(
        cornerRadius: CGFloat,
        placeholder: String,
        backgroundColor: UIColor,
        keyboardType: UIKeyboardType
    ) {
        self.layer.cornerRadius = cornerRadius
        self.placeholder = placeholder
        self.backgroundColor = backgroundColor
        self.borderStyle = .none
        self.keyboardType = keyboardType
    }
}

// Add padding helpers for UITextField
extension UITextField {
    func setLeftPaddingPoints(_ amount:CGFloat) {
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: amount, height: self.frame.size.height))
        self.leftView = paddingView
        self.leftViewMode = .always
    }
    func setRightPaddingPoints(_ amount:CGFloat) {
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: amount, height: self.frame.size.height))
        self.rightView = paddingView
        self.rightViewMode = .always
    }
}
