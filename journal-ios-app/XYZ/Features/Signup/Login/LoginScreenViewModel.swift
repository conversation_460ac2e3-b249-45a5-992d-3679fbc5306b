//
//  LoginScreenViewModel.swift

//
//  Created by <PERSON> on 2025/1/10.
//

import Foundation
import SwifterSwift

enum AuthCodeError: Error {
    case siwaAuthFailed // sign in with apple
    case emailVerificationFailed(reason: String)
}

struct Response<T: Decodable>: Decodable {
    var code: Int
    var data: T
}

struct Token: Decodable {
    var token: String
    var type: String
}

struct SendVerificationResult: Decodable { }

class LoginScreenViewModel: BaseService {

    let validatePath = "auth/apple/verify"
    let emailVerificationPath = "auth/code"
    let validateGooglePath = "auth/google/verify"

    func validateSiwaAuthCode(data: Data) async throws -> Token {
        return try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: validatePath,
                    parameters: [
                        "code": data.string(encoding: .utf8) ?? ""
                    ])
        )
    }

    @discardableResult
    func sendEmailVerificationCode(email: String) async throws -> SendVerificationResult {
        return try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: emailVerificationPath,
                    parameters: [
                        "email": email
                    ],
                    method: .post
                )
        )
    }

    func validateGoogleAuthCode(code: String) async throws -> Token {
        return try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: validateGooglePath,
                    parameters: [
                        "code": code,
                        "state": "state"
                    ],
                    method: .post
                )
        )
    }
}
