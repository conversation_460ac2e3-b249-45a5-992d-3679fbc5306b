//
//  SettingsViewModel.swift
//  XYZ
//
//  Created by Trae AI on 2025/5/1.
//

import Combine
import Foundation
import LocalAuthentication

class SettingsViewModel: ObservableObject {
    // MARK: - Properties
    @Published var isLoading: Bool = false
    @Published var isDeletingAccount: Bool = false
    private let biometricAuthContext = LAContext()
    private let biometricAuthKey = "biometricAuthEnabled"

    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init() {
        setupBindings()
    }

    private func setupBindings() {
        // Monitor login state changes
        App.shared.$isLoggedIn
            .sink { [weak self] _ in
                // Handle login state changes if needed
            }
            .store(in: &cancellables)
    }

    // MARK: - Public Methods
    func logMeOut() {
        DB.shared.logOut()
        App.shared.isLoggedIn = false
        App.shared.instance = nil
    }

    // MARK: - Account Deletion
    func deleteAccount() async throws {
        isDeletingAccount = true
        defer { isDeletingAccount = false }

        do {
            let userService = UserService()
            try await userService.deleteAccount()
            await MainActor.run {
                self.logMeOut()
            }
        } catch {
            throw error
        }
    }

    // MARK: - Biometric Authentication
    func getBiometricType() -> LABiometryType {
        var error: NSError?
        guard
            biometricAuthContext.canEvaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics, error: &error)
        else {
            return .none
        }
        return biometricAuthContext.biometryType
    }

    func isBiometricAuthEnabled() -> Bool {
        return App.shared.instance?.isAuthEnabled ?? false
    }

    func toggleBiometricAuth(_ enable: Bool) {
        App.shared.instance?.isAuthEnabled = enable
        DB.shared.setBiometricAuth(uid: App.shared.instance?.uid ?? 0, biometricAuth: enable)
    }

    func setBiometricAuth(enabled: Bool) {
        App.shared.instance?.isAuthEnabled = enabled
        DB.shared.setBiometricAuth(uid: App.shared.instance?.uid ?? 0, biometricAuth: enabled)
    }

    // MARK: - Pattern Protection
    func hasPatternProtection() -> Bool {
        let uid = App.shared.instance?.uid ?? 0
        return DB.shared.hasPatternProtection(uid: uid)
    }

    func checkPatternProtectionOnAppLaunch(completion: @escaping (Bool) -> Void) {
        let uid = App.shared.instance?.uid ?? 0
        let hasPattern = DB.shared.hasPatternProtection(uid: uid)
        completion(hasPattern)
    }
}
