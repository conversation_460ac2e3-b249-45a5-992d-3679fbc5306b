//
//  PasswordSetupViewModel.swift
//  XYZ
//
//  Created by <PERSON> on 2025/2/12.
//

import Combine
import Foundation

class PasswordSetupViewModel: ObservableObject {

    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var error: Error?
    @Published var success: Bool = false

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Public Methods
    func setupPassword(password: String, confirmPassword: String) {
        guard validatePassword(password: password, confirmPassword: confirmPassword) else {
            return
        }

        isLoading = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let hashedPassword = password.sha256()
            let uid = App.shared.instance?.uid ?? 0

            DB.shared.setPasswordHash(uid: uid, passwordHash: hashedPassword)

            DispatchQueue.main.async {
                self.isLoading = false
                self.success = true
                self.showSuccessMessage(XYLocalize.XYLocalize("password_set_success"))
            }
        }
    }

    func changePassword(currentPassword: String, newPassword: String, confirmPassword: String) {
        guard let uid = App.shared.instance?.uid else {
            self.error = PasswordError.invalidUser
            return
        }

        guard !currentPassword.isEmpty else {
            self.error = PasswordError.currentPasswordRequired
            return
        }

        guard DB.shared.verifyPassword(uid: uid, password: currentPassword) else {
            self.error = PasswordError.incorrectCurrentPassword
            return
        }

        guard validatePassword(password: newPassword, confirmPassword: confirmPassword) else {
            return
        }

        isLoading = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let hashedPassword = newPassword.sha256()

            DB.shared.setPasswordHash(uid: uid, passwordHash: hashedPassword)

            DispatchQueue.main.async {
                self.isLoading = false
                self.success = true
                self.showSuccessMessage(XYLocalize.XYLocalize("password_changed_success"))
            }
        }
    }

    func removePassword(currentPassword: String) {
        guard let uid = App.shared.instance?.uid else {
            self.error = PasswordError.invalidUser
            return
        }

        guard !currentPassword.isEmpty else {
            self.error = PasswordError.currentPasswordRequired
            return
        }

        guard DB.shared.verifyPassword(uid: uid, password: currentPassword) else {
            self.error = PasswordError.incorrectCurrentPassword
            return
        }

        isLoading = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            DB.shared.setPasswordHash(uid: uid, passwordHash: nil)

            DispatchQueue.main.async {
                self.isLoading = false
                self.success = true
                self.showSuccessMessage(XYLocalize.XYLocalize("password_removed_success"))
            }
        }
    }

    // MARK: - Private Methods
    private func validatePassword(password: String, confirmPassword: String) -> Bool {
        guard !password.isEmpty else {
            self.error = PasswordError.passwordRequired
            return false
        }

        guard password.count >= 6 else {
            self.error = PasswordError.passwordTooShort
            return false
        }

        guard password == confirmPassword else {
            self.error = PasswordError.passwordMismatch
            return false
        }

        return true
    }

    private func showSuccessMessage(_ message: String) {
        // You can implement a toast or notification system here
        XYLog(message)
    }
}

// MARK: - Password Errors
enum PasswordError: LocalizedError {
    case passwordRequired
    case passwordTooShort
    case passwordMismatch
    case currentPasswordRequired
    case incorrectCurrentPassword
    case invalidUser

    var errorDescription: String? {
        switch self {
        case .passwordRequired:
            return XYLocalize.XYLocalize("password_required")
        case .passwordTooShort:
            return XYLocalize.XYLocalize("password_too_short")
        case .passwordMismatch:
            return XYLocalize.XYLocalize("password_mismatch")
        case .currentPasswordRequired:
            return XYLocalize.XYLocalize("enter_current_password")
        case .incorrectCurrentPassword:
            return XYLocalize.XYLocalize("current_password_incorrect")
        case .invalidUser:
            return "Invalid user"
        }
    }
}
