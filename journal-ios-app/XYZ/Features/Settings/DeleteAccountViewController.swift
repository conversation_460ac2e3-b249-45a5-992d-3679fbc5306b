//
//  DeleteAccountViewController.swift
//  XYZ
//
//  Created by Trae AI on 2025/6/20.
//

import UIKit
import Combine

class DeleteAccountViewController: BaseViewController {

    // MARK: - Properties
    private let viewModel: SettingsViewModel

    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var warningIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "exclamationmark.triangle.fill")
        imageView.tintColor = .systemRed
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "删除账户"
        label.font = UIFont.boldSystemFont(ofSize: 24)
        label.textColor = .label
        label.textAlignment = .center
        return label
    }()

    private lazy var warningLabel: UILabel = {
        let label = UILabel()
        label.text = "此操作将永久删除您的账户和所有数据，包括：\n\n• 所有日记内容\n• 个人设置和偏好\n• 账户信息\n\n此操作无法撤销，请谨慎操作。"
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = .secondaryLabel
        label.numberOfLines = 0
        label.textAlignment = .left
        return label
    }()

    private lazy var deleteButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("确认删除账户", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemRed
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        button.layer.cornerRadius = 12
        button.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var cancelButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("取消", for: .normal)
        button.setTitleColor(.label, for: .normal)
        button.backgroundColor = .secondarySystemBackground
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18)
        button.layer.cornerRadius = 12
        button.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - Initialization
    init(viewModel: SettingsViewModel) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupBindings()
    }

    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "删除账户"

        // Add navigation bar buttons
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )

        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(warningIconView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(warningLabel)
        contentView.addSubview(deleteButton)
        contentView.addSubview(cancelButton)
    }

    private func setupConstraints() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        warningIconView.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        warningLabel.translatesAutoresizingMaskIntoConstraints = false
        deleteButton.translatesAutoresizingMaskIntoConstraints = false
        cancelButton.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // ScrollView constraints
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),

            // ContentView constraints
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),

            // Warning icon constraints
            warningIconView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 40),
            warningIconView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            warningIconView.widthAnchor.constraint(equalToConstant: 60),
            warningIconView.heightAnchor.constraint(equalToConstant: 60),

            // Title label constraints
            titleLabel.topAnchor.constraint(equalTo: warningIconView.bottomAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Warning label constraints
            warningLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 30),
            warningLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            warningLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Delete button constraints
            deleteButton.topAnchor.constraint(equalTo: warningLabel.bottomAnchor, constant: 30),
            deleteButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            deleteButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            deleteButton.heightAnchor.constraint(equalToConstant: 50),

            // Cancel button constraints
            cancelButton.topAnchor.constraint(equalTo: deleteButton.bottomAnchor, constant: 15),
            cancelButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            cancelButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            cancelButton.heightAnchor.constraint(equalToConstant: 50),
            cancelButton.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -40)
        ])
    }

    private func setupBindings() {
        // Observe loading state
        viewModel.$isDeletingAccount
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                self?.updateLoadingState(isLoading)
            }
            .store(in: &cancellables)
    }

    private var cancellables = Set<AnyCancellable>()

    // MARK: - Actions
    @objc private func deleteButtonTapped() {
        // Show final confirmation
        let alert = UIAlertController(
            title: "最终确认",
            message: "您确定要删除账户吗？此操作无法撤销。",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            self?.performAccountDeletion()
        })

        present(alert, animated: true)
    }

    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }

    // MARK: - Private Methods
    private func performAccountDeletion() {
        Task {

            self.isLoadingParts = true
            do {
                try await viewModel.deleteAccount()

                await MainActor.run {
                    // Account deleted successfully, dismiss and show success
                    self.dismiss(animated: true) {
                        // The viewModel will handle logout automatically
                        self.showSuccessMessage()
                    }
                }
            } catch {
                await MainActor.run {
                    self.showAlert(title: "删除失败", message: error.localizedDescription)
                }
            }
            self.isLoadingParts = false
        }
    }

    private func updateLoadingState(_ isLoading: Bool) {
        deleteButton.isEnabled = !isLoading
        cancelButton.isEnabled = !isLoading
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showSuccessMessage() {
        // This could be shown in the main app or login screen
        // For now, we'll just log it since the user will be logged out
        print("Account deleted successfully")
    }
}
