//
//  SettingsViewController.swift
//  XYZ
//
//  Created by Trae AI on 2025/5/1.
//

import Combine
import UIKit

class SettingsViewController: BaseViewController, UITableViewDataSource, UITableViewDelegate {

    // MARK: - Properties
    private var tableView: UITableView!
    private var sections: [SectionItem] = []

    var viewModel: SettingsViewModel!
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Lifecycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        title = XYLocalize.XYLocalize("settings_title")
        setupTableView()
        setupBindings()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setupTableViewSections()
    }

    private func setupTableView() {
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .singleLine
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ProfileCell.self, forCellReuseIdentifier: "Cell")
        tableView.showsVerticalScrollIndicator = false

        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 设置表格内容
        setupTableViewSections()
    }

    private func setupTableViewSections() {
        let hasPattern = PatternLockProtectionManager.shared.hasPatternProtection()
        App.shared.instance?.isAuthEnabled = hasPattern

        var securityRows: [RowItem] = [
            RowItem(
                title: XYLocalize.XYLocalize("settings_biometric_auth"),
                accessoryType: .none,
                switchState: viewModel.isBiometricAuthEnabled(),
                action: { [weak self] in
                    self?.handleBiometricAuthToggle()
                })
        ]

        // Add pattern protection options
        if hasPattern {
            securityRows.append(
                RowItem(
                    title: XYLocalize.XYLocalize("settings_change_pattern"),
                    accessoryType: .disclosureIndicator,
                    action: { [weak self] in
                        self?.showPatternSetup(mode: .change)
                    })
            )
        }

        sections = [
            SectionItem(
                title: XYLocalize.XYLocalize("settings_section_appearance"),
                rows: [
                    RowItem(
                        title: XYLocalize.XYLocalize("settings_theme_setting"),
                        action: { [weak self] in
                            self?.showThemeSelector()
                        })
                ])
        ]

        if !App.shared.isInGuestMode() {
            sections.append(
                SectionItem(
                    title: XYLocalize.XYLocalize("settings_section_security"),
                    rows: securityRows
                )
            )
            sections.append(
                SectionItem(
                title: XYLocalize.XYLocalize("settings_section_account"),
                rows: [
                    RowItem(
                        title: XYLocalize.XYLocalize("logout"), accessoryType: .none,
                        action: { [weak self] in
                            self?.showLogoutConfirmation()
                        }),
                    RowItem(
                        title: "删除账户", accessoryType: .disclosureIndicator,
                        action: { [weak self] in
                            self?.showDeleteAccountConfirmation()
                        })
                ])
            )
        }

        tableView.reloadData()
    }

    private func setupBindings() {
        // 监听登录状态变化
        App.shared.$isLoggedIn
            .receive(on: RunLoop.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
    }

    // MARK: - UITableViewDataSource
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].rows.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let rowItem = sections[indexPath.section].rows[indexPath.row]
        let cell =
            tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath) as? ProfileCell
            ?? UITableViewCell(style: .value1, reuseIdentifier: "Cell")

        if let profileCell = cell as? ProfileCell {
            profileCell.titleLabel.text = rowItem.title
            profileCell.accessoryView = nil
            profileCell.accessoryType = rowItem.accessoryType
            profileCell.selectionStyle = .none

            // Handle switch state for biometric auth
            if let switchState = rowItem.switchState {
                let switchControl = UISwitch()
                switchControl.isOn = switchState
                switchControl.addTarget(self, action: #selector(switchValueChanged(_:)), for: .valueChanged)
                profileCell.accessoryView = switchControl
                profileCell.accessoryType = .none
            }

            // 设置退出登录和删除账户按钮的文本颜色为红色
            if rowItem.title == XYLocalize.XYLocalize("logout") || rowItem.title == "删除账户" {
                profileCell.titleLabel.textColor = Colors.danger.dynamicColor()
            } else {
                profileCell.titleLabel.textColor = Colors.text.dynamicColor()
            }

        } else {
            cell.textLabel?.text = rowItem.title
            cell.textLabel?.font = UIFont.systemFont(ofSize: 16)
            cell.accessoryType = rowItem.accessoryType

            // Handle switch state for biometric auth
            if let switchState = rowItem.switchState {
                let switchControl = UISwitch()
                switchControl.isOn = switchState
                switchControl.addTarget(self, action: #selector(switchValueChanged(_:)), for: .valueChanged)
                cell.accessoryView = switchControl
                cell.accessoryType = .none
            } else {
                cell.accessoryView = nil
            }

            // 设置退出登录和删除账户按钮的文本颜色为红色
            if rowItem.title == XYLocalize.XYLocalize("logout") || rowItem.title == "删除账户" {
                cell.textLabel?.textColor = Colors.danger.dynamicColor()
            } else {
                cell.textLabel?.textColor = Colors.text.dynamicColor()
            }
        }
        return cell
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return sections[section].title.isEmpty ? nil : sections[section].title
    }

    // MARK: - UITableViewDelegate
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let rowItem = sections[indexPath.section].rows[indexPath.row]
        rowItem.action?()
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 55
    }

    // MARK: - Actions
    @objc private func switchValueChanged(_ sender: UISwitch) {
        // Find which row this switch belongs to and handle accordingly
        if let cell = sender.superview as? UITableViewCell,
            let indexPath = tableView.indexPath(for: cell) {
            let rowItem = sections[indexPath.section].rows[indexPath.row]

            if rowItem.title == XYLocalize.XYLocalize("settings_biometric_auth") {
                handleBiometricAuthSwitchChange(sender.isOn)
            }
        }
    }

    private func handleBiometricAuthToggle() {
        let isCurrentlyEnabled = viewModel.isBiometricAuthEnabled()
        handleBiometricAuthSwitchChange(!isCurrentlyEnabled)
    }

    private func handleBiometricAuthSwitchChange(_ isEnabled: Bool) {
        if isEnabled {
            // Turning ON: Show pattern setup directly if no pattern exists
            if !PatternLockProtectionManager.shared.hasPatternProtection() {
                showPatternSetup(mode: .setup)
            } else {
                // Pattern exists, just enable biometric auth
                viewModel.setBiometricAuth(enabled: true)
                setupTableViewSections() // Refresh the table to hide/show remove option
            }
        } else {
            // Turning OFF: Show verification view to confirm
            if PatternLockProtectionManager.shared.hasPatternProtection() {
                showPatternVerificationForDisabling()
            } else {
                // No pattern protection, just disable
                viewModel.setBiometricAuth(enabled: false)
                setupTableViewSections() // Refresh the table to hide/show remove option
            }
        }
    }

    private func showPatternVerificationForDisabling() {
        // Create a pattern login view controller for verification
        let patternLoginVC = PatternLockLoginConfigurator.configure { [weak self] in
            // On successful verification, disable biometric auth
            self?.viewModel.setBiometricAuth(enabled: false)
            self?.setupTableViewSections() // Refresh the table to show remove option
        }

        let navigationController = UINavigationController(rootViewController: patternLoginVC)
        navigationController.modalPresentationStyle = .fullScreen
        presentSafely(navigationController, animated: true)
    }

    private func showThemeSelector() {
        let alert = UIAlertController(
            title: XYLocalize.XYLocalize("settings_theme_setting"),
            message: XYLocalize.XYLocalize("settings_theme_select_message"),
            preferredStyle: .actionSheet
        )

        alert.addAction(
            UIAlertAction(title: XYLocalize.XYLocalize("settings_theme_light"), style: .default) { _ in
                ThemeManager.shared.currentThemeMode = .light
            })

        alert.addAction(
            UIAlertAction(title: XYLocalize.XYLocalize("settings_theme_dark"), style: .default) { _ in
                ThemeManager.shared.currentThemeMode = .dark
            })

        alert.addAction(
            UIAlertAction(title: XYLocalize.XYLocalize("settings_theme_system"), style: .default) { _ in
                ThemeManager.shared.currentThemeMode = .system
            })

        alert.addAction(UIAlertAction(title: XYLocalize.XYLocalize("cancel"), style: .cancel))

        // 对于iPad，需要设置弹出位置
        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = view
            popoverController.sourceRect = CGRect(
                x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
            popoverController.permittedArrowDirections = []
        }

        present(alert, animated: true)
    }

    private func toggleBiometricAuth() {
        let biometricType = viewModel.getBiometricType()
        guard biometricType != .none else {
            showAlert(
                title: XYLocalize.XYLocalize("settings_biometric_not_available"),
                message: XYLocalize.XYLocalize("settings_biometric_not_available_message"))
            return
        }

        let isEnabled = viewModel.isBiometricAuthEnabled()
        viewModel.toggleBiometricAuth(!isEnabled)
        tableView.reloadData()
    }

    private func showLogoutConfirmation() {
        let alert = UIAlertController(
            title: XYLocalize.XYLocalize("logout"),
            message: XYLocalize.XYLocalize("settings_logout_confirm_message"),
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: XYLocalize.XYLocalize("cancel"), style: .cancel))
        alert.addAction(
            UIAlertAction(title: XYLocalize.XYLocalize("ok"), style: .destructive) { [weak self] _ in
                self?.viewModel.logMeOut()
            }
        )

        present(alert, animated: true)
    }

    private func showDeleteAccountConfirmation() {
        let alert = UIAlertController(
            title: "删除账户",
            message: "此操作将永久删除您的账户和所有数据。您确定要继续吗？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(
            UIAlertAction(title: "继续", style: .destructive) { [weak self] _ in
                self?.showDeleteAccountViewController()
            }
        )

        present(alert, animated: true)
    }

    private func showDeleteAccountViewController() {
        let deleteAccountVC = DeleteAccountViewController(viewModel: viewModel)
        let navigationController = UINavigationController(rootViewController: deleteAccountVC)
        navigationController.modalPresentationStyle = .formSheet
        present(navigationController, animated: true)
    }

    private func showPatternSetup(mode: PatternLockSetupMode) {
        PatternLockProtectionManager.shared.showPatternSetup(mode: mode, from: self)
    }
}

// MARK: - Configurator
class SettingsConfigurator {
    static func configure() -> SettingsViewController {
        let viewController = SettingsViewController()
        let viewModel = SettingsViewModel()
        viewController.viewModel = viewModel
        return viewController
    }
}
