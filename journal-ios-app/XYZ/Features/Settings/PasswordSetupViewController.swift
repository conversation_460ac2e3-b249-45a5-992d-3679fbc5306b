//
//  PasswordSetupViewController.swift
//  XYZ
//
//  Created by <PERSON> on 2025/2/12.
//

import Combine
import SnapKit
import UIKit

enum PasswordSetupMode {
    case setup
    case change
    case remove
}

class PasswordSetupViewController: BaseViewController {

    // MARK: - Properties
    private var mode: PasswordSetupMode
    private var viewModel: PasswordSetupViewModel!
    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.keyboardDismissMode = .onDrag
        return scrollView
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        label.textColor = Colors.text.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.textColor = Colors.secondaryText.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var currentPasswordTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = XYLocalize.XYLocalize("enter_current_password")
        textField.isSecureTextEntry = true
        textField.borderStyle = .none
        textField.backgroundColor = Colors.cardBackground.dynamicColor()
        textField.layer.cornerRadius = 12
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = Colors.text.dynamicColor()
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        textField.isHidden = true
        return textField
    }()

    private lazy var passwordTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = XYLocalize.XYLocalize("password_placeholder")
        textField.isSecureTextEntry = true
        textField.borderStyle = .none
        textField.backgroundColor = Colors.cardBackground.dynamicColor()
        textField.layer.cornerRadius = 12
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = Colors.text.dynamicColor()
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        return textField
    }()

    private lazy var confirmPasswordTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = XYLocalize.XYLocalize("confirm_password_placeholder")
        textField.isSecureTextEntry = true
        textField.borderStyle = .none
        textField.backgroundColor = Colors.cardBackground.dynamicColor()
        textField.layer.cornerRadius = 12
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = Colors.text.dynamicColor()
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        return textField
    }()

    private lazy var saveButton: UIButton = {
        let button = UIButton(type: .system)
        button.backgroundColor = Colors.themeColorGreen.dynamicColor()
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        button.layer.cornerRadius = 12
        button.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var removeButton: UIButton = {
        let button = UIButton(type: .system)
        button.backgroundColor = Colors.themeColorRed.dynamicColor()
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        button.layer.cornerRadius = 12
        button.setTitle(XYLocalize.XYLocalize("settings_remove_password"), for: .normal)
        button.addTarget(self, action: #selector(removeButtonTapped), for: .touchUpInside)
        button.isHidden = true
        return button
    }()

    // MARK: - Initialization
    init(mode: PasswordSetupMode) {
        self.mode = mode
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        configureForMode()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.prefersLargeTitles = false
    }

    // MARK: - Setup
    func setup(viewModel: PasswordSetupViewModel) {
        self.viewModel = viewModel
    }

    private func setupUI() {
        view.backgroundColor = Colors.background.dynamicColor()

        // Navigation
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )

        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(titleLabel)
        contentView.addSubview(subtitleLabel)
        contentView.addSubview(currentPasswordTextField)
        contentView.addSubview(passwordTextField)
        contentView.addSubview(confirmPasswordTextField)
        contentView.addSubview(saveButton)
        contentView.addSubview(removeButton)

        // Layout
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        currentPasswordTextField.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        passwordTextField.snp.makeConstraints { make in
            make.top.equalTo(currentPasswordTextField.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        confirmPasswordTextField.snp.makeConstraints { make in
            make.top.equalTo(passwordTextField.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        saveButton.snp.makeConstraints { make in
            make.top.equalTo(confirmPasswordTextField.snp.bottom).offset(32)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        removeButton.snp.makeConstraints { make in
            make.top.equalTo(saveButton.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
            make.bottom.equalToSuperview().offset(-40)
        }
    }

    private func configureForMode() {
        switch mode {
        case .setup:
            titleLabel.text = XYLocalize.XYLocalize("password_setup_title")
            subtitleLabel.text = XYLocalize.XYLocalize("password_setup_subtitle")
            saveButton.setTitle(XYLocalize.XYLocalize("settings_set_password"), for: .normal)
            currentPasswordTextField.isHidden = true
            removeButton.isHidden = true

        case .change:
            titleLabel.text = XYLocalize.XYLocalize("settings_change_password")
            subtitleLabel.text = XYLocalize.XYLocalize("enter_current_password")
            saveButton.setTitle(XYLocalize.XYLocalize("settings_change_password"), for: .normal)
            currentPasswordTextField.isHidden = false
            removeButton.isHidden = false
            passwordTextField.placeholder = XYLocalize.XYLocalize("enter_new_password")
            confirmPasswordTextField.placeholder = XYLocalize.XYLocalize("confirm_new_password")

        case .remove:
            titleLabel.text = XYLocalize.XYLocalize("settings_remove_password")
            subtitleLabel.text = XYLocalize.XYLocalize("enter_current_password")
            saveButton.setTitle(XYLocalize.XYLocalize("settings_remove_password"), for: .normal)
            saveButton.backgroundColor = Colors.themeColorRed.dynamicColor()
            currentPasswordTextField.isHidden = false
            passwordTextField.isHidden = true
            confirmPasswordTextField.isHidden = true
            removeButton.isHidden = true
        }

        // Update constraints for hidden fields
        if mode == .setup {
            passwordTextField.snp.remakeConstraints { make in
                make.top.equalTo(subtitleLabel.snp.bottom).offset(40)
                make.leading.trailing.equalToSuperview().inset(20)
                make.height.equalTo(50)
            }
        } else if mode == .remove {
            saveButton.snp.remakeConstraints { make in
                make.top.equalTo(currentPasswordTextField.snp.bottom).offset(32)
                make.leading.trailing.equalToSuperview().inset(20)
                make.height.equalTo(50)
                make.bottom.equalToSuperview().offset(-40)
            }
        }
    }

    private func setupBindings() {
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                self?.saveButton.isEnabled = !isLoading
                self?.saveButton.alpha = isLoading ? 0.7 : 1.0
            }
            .store(in: &cancellables)

        viewModel.$error
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.showAlert(
                    title: XYLocalize.XYLocalize("error"), message: error.localizedDescription)
            }
            .store(in: &cancellables)

        viewModel.$success
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    self?.dismiss(animated: true)
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }

    @objc private func saveButtonTapped() {
        let currentPassword = currentPasswordTextField.text ?? ""
        let password = passwordTextField.text ?? ""
        let confirmPassword = confirmPasswordTextField.text ?? ""

        switch mode {
        case .setup:
            viewModel.setupPassword(password: password, confirmPassword: confirmPassword)
        case .change:
            viewModel.changePassword(
                currentPassword: currentPassword, newPassword: password,
                confirmPassword: confirmPassword)
        case .remove:
            viewModel.removePassword(currentPassword: currentPassword)
        }
    }

    @objc private func removeButtonTapped() {
        let alert = UIAlertController(
            title: XYLocalize.XYLocalize("settings_remove_password"),
            message: XYLocalize.XYLocalize("enter_current_password"),
            preferredStyle: .alert
        )

        alert.addTextField { textField in
            textField.placeholder = XYLocalize.XYLocalize("password_placeholder")
            textField.isSecureTextEntry = true
        }

        alert.addAction(UIAlertAction(title: XYLocalize.XYLocalize("cancel"), style: .cancel))
        alert.addAction(
            UIAlertAction(
                title: XYLocalize.XYLocalize("settings_remove_password"), style: .destructive
            ) { [weak self] _ in
                if let password = alert.textFields?.first?.text {
                    self?.viewModel.removePassword(currentPassword: password)
                }
            })

        present(alert, animated: true)
    }
}
