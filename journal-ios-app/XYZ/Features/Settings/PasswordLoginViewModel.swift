//
//  PasswordLoginViewModel.swift
//  XYZ
//
//  Created by <PERSON> on 2025/2/12.
//

import Combine
import Foundation
import LocalAuthentication

class PasswordLoginViewModel: ObservableObject {

    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var error: Error?
    @Published var success: Bool = false

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Public Methods
    func verifyPassword(_ password: String) {
        guard !password.isEmpty else {
            self.error = PasswordError.passwordRequired
            return
        }

        guard let uid = App.shared.instance?.uid else {
            self.error = PasswordError.invalidUser
            return
        }

        isLoading = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let isValid = DB.shared.verifyPassword(uid: uid, password: password)

            DispatchQueue.main.async {
                self.isLoading = false
                if isValid {
                    self.success = true
                } else {
                    self.error = PasswordError.incorrectCurrentPassword
                }
            }
        }
    }

    func authenticateWithBiometrics() {
        let context = LAContext()
        var error: NSError?

        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        else {
            self.error = error
            return
        }

        isLoading = true

        let reason = XYLocalize.XYLocalize("password_login_subtitle")

        context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) {
            [weak self] success, authenticationError in
            DispatchQueue.main.async {
                self?.isLoading = false
                if success {
                    self?.success = true
                } else {
                    self?.error = authenticationError
                }
            }
        }
    }
}
