//
//  PasswordSetupConfigurator.swift
//  XYZ
//
//  Created by <PERSON> on 2025/2/12.
//

import Foundation

class PasswordSetupConfigurator {
    static func configure(mode: PasswordSetupMode) -> PasswordSetupViewController {
        let viewModel = PasswordSetupViewModel()
        let viewController = PasswordSetupViewController(mode: mode)
        viewController.setup(viewModel: viewModel)
        return viewController
    }
}

class PasswordLoginConfigurator {
    static func configure(onSuccess: @escaping () -> Void) -> PasswordLoginViewController {
        let viewModel = PasswordLoginViewModel()
        let viewController = PasswordLoginViewController(onSuccess: onSuccess)
        viewController.setup(viewModel: viewModel)
        return viewController
    }
}
