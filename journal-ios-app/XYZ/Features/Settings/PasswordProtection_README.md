# Password Protection Feature

This document outlines the password protection feature implementation for the journal app.

## Overview

The password protection feature allows users to secure their journals with a password that is stored locally in the SQLite database. The password is hashed using SHA256 and is not synchronized to the server.

## Components

### 1. Database Changes (`DB.swift`)
- Added `passwordHash` field to the session table
- Added migration logic to add the password_hash column to existing databases
- Added methods for password management:
  - `setPasswordHash(uid:passwordHash:)` - Set or update password hash
  - `hasPasswordProtection(uid:)` - Check if user has password protection enabled
  - `verifyPassword(uid:password:)` - Verify entered password against stored hash

### 2. Models
- Updated `UserSession.swift` to include `passwordHash` field

### 3. Password Setup (`PasswordSetupViewController.swift`)
- Supports three modes: setup, change, remove
- Beautiful UI with proper validation
- Handles password confirmation and validation

### 4. Password Login (`PasswordLoginViewController.swift`)
- Fancy login screen with gradient background
- Animated content view with shake animation for wrong passwords
- Support for biometric authentication if enabled
- Modern UI with rounded corners and shadows

### 5. View Models
- `PasswordSetupViewModel.swift` - Handles password setup logic
- `PasswordLoginViewModel.swift` - Handles password verification and biometric auth

### 6. Password Protection Manager (`PasswordProtectionManager.swift`)
- Centralized manager for password protection functionality
- Handles showing password login screen when needed
- Provides easy integration points for the app

### 7. Settings Integration
- Added password protection options to Settings screen
- Dynamic menu based on whether password is already set
- Options: Set Password, Change Password, Remove Password

## Localization

Added localization strings in both English and Chinese:
- Password setup and validation messages
- Error messages
- UI labels and buttons

## Security Features

1. **Password Hashing**: Passwords are hashed using SHA256 before storage
2. **Local Storage Only**: Passwords are never sent to the server
3. **Biometric Integration**: Works with existing biometric authentication
4. **App Launch Protection**: Automatically prompts for password when app becomes active

## Integration Points

### App Launch
The `TabBarController` checks for password protection when the app becomes active:

```swift
PasswordProtectionManager.shared.checkPasswordProtectionOnAppLaunch(from: self) {
    // App is unlocked, continue normal flow
}
```

### Settings Screen
Users can manage password protection from the Settings screen with dynamic options based on current state.

## Usage Flow

1. **First Time Setup**: User goes to Settings > Set Password
2. **App Launch**: If password is set, user must enter password to access journals
3. **Change Password**: User can change existing password from Settings
4. **Remove Password**: User can remove password protection from Settings

## Files Modified/Created

### New Files:
- `PasswordSetupViewController.swift`
- `PasswordSetupViewModel.swift`
- `PasswordLoginViewController.swift`
- `PasswordLoginViewModel.swift`
- `PasswordProtectionManager.swift`
- `PasswordSetupConfigurator.swift`

### Modified Files:
- `DB.swift` - Added password fields and methods
- `UserSession.swift` - Added passwordHash field
- `SettingsViewController.swift` - Added password protection options
- `SettingsViewModel.swift` - Added password protection methods
- `TabBarController.swift` - Added password check on app launch
- `String+Extension.swift` - Added SHA256 hashing
- Localization files - Added password protection strings

## Future Enhancements

1. **Password Strength Indicator**: Visual feedback for password strength
2. **Auto-lock Timer**: Automatically lock app after inactivity
3. **Touch ID/Face ID Only Mode**: Option to use only biometric authentication
4. **Password Recovery**: Secure password recovery mechanism
5. **Multiple Attempts Protection**: Lock app after multiple failed attempts

## Notes

- The implementation follows the existing MVVM architecture
- All UI components use the existing theme system
- Password protection is completely optional and can be disabled
- The feature integrates seamlessly with existing biometric authentication