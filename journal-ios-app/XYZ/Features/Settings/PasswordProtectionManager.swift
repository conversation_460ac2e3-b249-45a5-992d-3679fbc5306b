//
//  PatternLockProtectionManager.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/12.
//

import Combine
import Foundation
import LocalAuthentication
import UIKit

class PatternLockProtectionManager {
    static let shared = PatternLockProtectionManager()

    private init() {}

    // MARK: - Pattern Protection Check
    func checkPatternProtectionOnAppLaunch(
        from viewController: UIViewController, completion: @escaping () -> Void
    ) {
        guard let uid = App.shared.instance?.uid else {
            completion()
            return
        }

        let hasPattern = DB.shared.hasPatternProtection(uid: uid)

        if hasPattern {
            showPatternLogin(from: viewController, completion: completion)
        } else {
            completion()
        }
    }

    // MARK: - Show Pattern Login
    private func showPatternLogin(
        from viewController: UIViewController, completion: @escaping () -> Void
    ) {
        // Use a small delay to ensure the view controller is ready for presentation
        DispatchQueue.main.asyncAfter(deadline: .now()) {
            let patternLoginVC = PatternLockLoginConfigurator.configure(onSuccess: completion)
            patternLoginVC.modalPresentationStyle = .fullScreen

            // Use the PresentationManager for safe presentation with high priority
            viewController.presentSafely(
                patternLoginVC,
                animated: false,
                priority: .high
            )
        }
    }

    // MARK: - Pattern Setup
    func showPatternSetup(mode: PatternLockSetupMode, from viewController: UIViewController) {
        let patternSetupVC = PatternLockSetupConfigurator.configure(mode: mode)
        let navigationController = UINavigationController(rootViewController: patternSetupVC)
        navigationController.modalPresentationStyle = .fullScreen

        // Use the PresentationManager for consistent presentation
        viewController.presentSafely(navigationController, animated: true)
    }

    // MARK: - Pattern Verification
    func hasPatternProtection() -> Bool {
        guard let uid = App.shared.instance?.uid else { return false }
        return DB.shared.hasPatternProtection(uid: uid)
    }

    func verifyPattern(_ pattern: String) -> Bool {
        guard let uid = App.shared.instance?.uid else { return false }
        return DB.shared.verifyPattern(uid: uid, pattern: pattern)
    }

    func setPattern(_ pattern: String) {
        guard let uid = App.shared.instance?.uid else { return }
        let hashedPattern = pattern.sha256()
        DB.shared.setPatternHash(uid: uid, patternHash: hashedPattern)
    }

    func removePattern() {
        guard let uid = App.shared.instance?.uid else { return }
        DB.shared.setPatternHash(uid: uid, patternHash: nil)
    }
}
