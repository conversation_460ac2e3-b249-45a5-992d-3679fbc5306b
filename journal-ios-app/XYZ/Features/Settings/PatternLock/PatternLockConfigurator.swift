//
//  PatternLockConfigurator.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/12.
//

import Foundation

class PatternLockSetupConfigurator {
    static func configure(mode: PatternLockSetupMode) -> PatternLockSetupViewController {
        let viewModel = PatternLockSetupViewModel()
        let viewController = PatternLockSetupViewController(mode: mode)
        viewController.setup(viewModel: viewModel)
        return viewController
    }
}

class PatternLockLoginConfigurator {
    static func configure(onSuccess: @escaping () -> Void) -> PatternLockLoginViewController {
        let viewModel = PatternLockLoginViewModel()
        let viewController = PatternLockLoginViewController(onSuccess: onSuccess)
        viewController.setup(viewModel: viewModel)
        return viewController
    }
}
