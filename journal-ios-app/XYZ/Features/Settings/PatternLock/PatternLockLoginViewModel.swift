//
//  PatternLockLoginViewModel.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/12.
//

import Combine
import Foundation
import LocalAuthentication

class PatternLockLoginViewModel: ObservableObject {

    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var error: Error?
    @Published var success: Bool = false

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Public Methods
    func verifyPattern(_ pattern: String) {
        guard !pattern.isEmpty else {
            self.error = PatternLockError.patternRequired
            return
        }

        guard let uid = App.shared.instance?.uid else {
            self.error = PatternLockError.invalidUser
            return
        }

        isLoading = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let isValid = DB.shared.verifyPattern(uid: uid, pattern: pattern)

            DispatchQueue.main.async {
                self.isLoading = false
                if isValid {
                    self.success = true
                } else {
                    self.error = PatternLockError.incorrectPattern
                }
            }
        }
    }

    func authenticateWithBiometrics() {
        let context = LAContext()
        var error: NSError?

        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        else {
            self.error = error
            return
        }

        isLoading = true

        let reason = XYLocalize.XYLocalize("pattern_lock_subtitle")

        context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) {
            [weak self] success, authenticationError in
            DispatchQueue.main.async {
                self?.isLoading = false
                if success {
                    self?.success = true
                } else {
                    self?.error = authenticationError
                }
            }
        }
    }
}

// MARK: - Pattern Lock Errors
enum PatternLockError: LocalizedError {
    case patternRequired
    case patternTooShort
    case patternMismatch
    case currentPatternRequired
    case incorrectPattern
    case invalidUser

    var errorDescription: String? {
        switch self {
        case .patternRequired:
            return XYLocalize.XYLocalize("pattern_required")
        case .patternTooShort:
            return XYLocalize.XYLocalize("pattern_too_short")
        case .patternMismatch:
            return XYLocalize.XYLocalize("pattern_mismatch")
        case .currentPatternRequired:
            return XYLocalize.XYLocalize("enter_current_pattern")
        case .incorrectPattern:
            return XYLocalize.XYLocalize("incorrect_pattern")
        case .invalidUser:
            return "Invalid user"
        }
    }
}
