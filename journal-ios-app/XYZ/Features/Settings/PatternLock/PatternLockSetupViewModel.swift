//
//  PatternLockSetupViewModel.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/12.
//

import Combine
import Foundation

class PatternLockSetupViewModel: ObservableObject {

    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var error: Error?
    @Published var success: Bool = false

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Public Methods
    func setupPattern(pattern: String) {
        guard validatePattern(pattern: pattern) else {
            return
        }

        isLoading = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let hashedPattern = pattern.sha256()
            let uid = App.shared.instance?.uid ?? 0

            DB.shared.setPatternHash(uid: uid, patternHash: hashedPattern)

            DispatchQueue.main.async {
                self.isLoading = false
                self.success = true
                self.showSuccessMessage(XYLocalize.XYLocalize("pattern_set_success"))
            }
        }
    }

    func changePattern(currentPattern: String, newPattern: String) {
        let uid = App.shared.instance?.uid ?? 0

        guard !currentPattern.isEmpty else {
            self.error = PatternLockError.currentPatternRequired
            return
        }

        guard DB.shared.verifyPattern(uid: uid, pattern: currentPattern) else {
            self.error = PatternLockError.incorrectPattern
            return
        }

        guard validatePattern(pattern: newPattern) else {
            return
        }

        isLoading = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let hashedPattern = newPattern.sha256()

            DB.shared.setPatternHash(uid: uid, patternHash: hashedPattern)

            DispatchQueue.main.async {
                self.isLoading = false
                self.success = true
                self.showSuccessMessage(XYLocalize.XYLocalize("pattern_changed_success"))
            }
        }
    }

    func removePattern(currentPattern: String? = nil) {
        guard let uid = App.shared.instance?.uid else {
            self.error = PatternLockError.invalidUser
            return
        }

        // If current pattern is provided, verify it first
        if let currentPattern = currentPattern {
            guard !currentPattern.isEmpty else {
                self.error = PatternLockError.currentPatternRequired
                return
            }

            guard DB.shared.verifyPattern(uid: uid, pattern: currentPattern) else {
                self.error = PatternLockError.incorrectPattern
                return
            }
        }

        isLoading = true

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            DB.shared.setPatternHash(uid: uid, patternHash: nil)

            DispatchQueue.main.async {
                self.isLoading = false
                self.success = true
                self.showSuccessMessage(XYLocalize.XYLocalize("pattern_removed_success"))
            }
        }
    }

    func verifyCurrentPattern(_ pattern: String, completion: @escaping (Bool) -> Void) {
        guard let uid = App.shared.instance?.uid else {
            completion(false)
            return
        }

        DispatchQueue.global(qos: .userInitiated).async {
            let isValid = DB.shared.verifyPattern(uid: uid, pattern: pattern)
            completion(isValid)
        }
    }

    // MARK: - Private Methods
    private func validatePattern(pattern: String) -> Bool {
        guard !pattern.isEmpty else {
            self.error = PatternLockError.patternRequired
            return false
        }

        // Pattern should have at least 4 points
        let patternPoints = pattern.split(separator: ",")
        guard patternPoints.count >= 4 else {
            self.error = PatternLockError.patternTooShort
            return false
        }

        return true
    }

    private func showSuccessMessage(_ message: String) {
        // You can implement a toast or notification system here
        XYLog(message)
    }
}
