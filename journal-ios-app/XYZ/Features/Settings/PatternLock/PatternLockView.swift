//
//  PatternLockView.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/12.
//

import UIKit

protocol PatternLockViewDelegate: AnyObject {
    func patternLockView(_ view: PatternLockView, didCompletePattern pattern: [Int])
    func patternLockView(_ view: PatternLockView, didFailPattern pattern: [Int])
}

class PatternLockView: UIView {

    // MARK: - Properties
    weak var delegate: PatternLockViewDelegate?

    private var dots: [PatternDotView] = []
    private var currentPattern: [Int] = []
    private var isDrawing = false
    private var pathLayer = CAShapeLayer()
    private var currentPath = UIBezierPath()

    // Configuration
    private let gridSize = 3
    private let dotSize: CGFloat = 60
    private let dotSpacing: CGFloat = 100

    // Colors
    private var normalColor: UIColor {
        return Colors.secondaryText.light.withAlphaComponent(0.3)
    }

    private var selectedColor: UIColor {
        return Colors.themeColor.light
    }

    private var errorColor: UIColor {
        return Colors.themeColorRed.light
    }

    private var lineColor: UIColor {
        return Colors.themeColor.light
    }

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    // MARK: - Setup
    private func setupView() {
        backgroundColor = .clear
        setupDots()
        setupPathLayer()
    }

    private func setupDots() {
        for index in 0..<(gridSize * gridSize) {
            let dot = PatternDotView()
            dot.tag = index
            dot.normalColor = normalColor
            dot.selectedColor = selectedColor
            addSubview(dot)
            dots.append(dot)
        }
    }

    private func setupPathLayer() {
        pathLayer.strokeColor = lineColor.cgColor
        pathLayer.lineWidth = 4.0
        pathLayer.lineCap = .round
        pathLayer.lineJoin = .round
        pathLayer.fillColor = UIColor.clear.cgColor
        layer.addSublayer(pathLayer)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        layoutDots()
    }

    private func layoutDots() {
        let totalWidth = CGFloat(gridSize - 1) * dotSpacing + dotSize
        let totalHeight = CGFloat(gridSize - 1) * dotSpacing + dotSize

        let startX = (bounds.width - totalWidth) / 2
        let startY = (bounds.height - totalHeight) / 2

        for index in 0..<dots.count {
            let row = index / gridSize
            let col = index % gridSize

            let originX = startX + CGFloat(col) * dotSpacing
            let originY = startY + CGFloat(row) * dotSpacing

            dots[index].frame = CGRect(x: originX, y: originY, width: dotSize, height: dotSize)
        }
    }

    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let location = touch.location(in: self)

        if let dotIndex = getDotIndex(at: location) {
            startPattern(at: dotIndex, location: location)
        }
    }

    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first, isDrawing else { return }
        let location = touch.location(in: self)

        updatePath(to: location)

        if let dotIndex = getDotIndex(at: location), !currentPattern.contains(dotIndex) {
            addDotToPattern(dotIndex)
        }
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawing else { return }
        completePattern()
    }

    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawing else { return }
        completePattern()
    }

    // MARK: - Pattern Logic
    private func startPattern(at dotIndex: Int, location: CGPoint) {
        isDrawing = true
        currentPattern = [dotIndex]
        dots[dotIndex].isSelected = true

        currentPath = UIBezierPath()
        currentPath.move(to: dots[dotIndex].center)
        pathLayer.path = currentPath.cgPath
    }

    private func addDotToPattern(_ dotIndex: Int) {
        currentPattern.append(dotIndex)
        dots[dotIndex].isSelected = true

        currentPath.addLine(to: dots[dotIndex].center)
        pathLayer.path = currentPath.cgPath
    }

    private func updatePath(to location: CGPoint) {
        let tempPath = currentPath.copy() as! UIBezierPath
        tempPath.addLine(to: location)
        pathLayer.path = tempPath.cgPath
    }

    private func completePattern() {
        isDrawing = false

        if currentPattern.count >= 4 {
            delegate?.patternLockView(self, didCompletePattern: currentPattern)
        } else {
            delegate?.patternLockView(self, didFailPattern: currentPattern)
            showError()
        }
    }

    private func getDotIndex(at location: CGPoint) -> Int? {
        for (index, dot) in dots.enumerated() where dot.frame.contains(location) {
            return index
        }
        return nil
    }

    // MARK: - Public Methods
    func resetPattern() {
        currentPattern.removeAll()
        isDrawing = false

        for dot in dots {
            dot.isSelected = false
            dot.isError = false
        }

        pathLayer.path = nil
        currentPath = UIBezierPath()
    }

    func showError() {
        for dotIndex in currentPattern {
            dots[dotIndex].isError = true
        }

        pathLayer.strokeColor = errorColor.cgColor

        // Shake animation
        let animation = CAKeyframeAnimation(keyPath: "transform.translation.x")
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.duration = 0.6
        animation.values = [-10.0, 10.0, -10.0, 10.0, -5.0, 5.0, 0.0]
        layer.add(animation, forKey: "shake")

        // Reset after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.resetPattern()
            self.pathLayer.strokeColor = self.lineColor.cgColor
        }
    }

    func showSuccess() {
        pathLayer.strokeColor = Colors.themeColorGreen.light.cgColor

        // Success animation
        UIView.animate(withDuration: 0.3, animations: {
            self.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
        }) { _ in
            UIView.animate(withDuration: 0.3) {
                self.transform = .identity
            }
        }

        // Reset after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.resetPattern()
            self.pathLayer.strokeColor = self.lineColor.cgColor
        }
    }
}

// MARK: - Pattern Dot View
class PatternDotView: UIView {

    var normalColor: UIColor = .gray {
        didSet { updateAppearance() }
    }

    var selectedColor: UIColor = .blue {
        didSet { updateAppearance() }
    }

    var errorColor: UIColor = .red {
        didSet { updateAppearance() }
    }

    var isSelected: Bool = false {
        didSet { updateAppearance() }
    }

    var isError: Bool = false {
        didSet { updateAppearance() }
    }

    private let outerCircle = UIView()
    private let innerCircle = UIView()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        backgroundColor = .clear

        addSubview(outerCircle)
        addSubview(innerCircle)

        outerCircle.layer.borderWidth = 2
        outerCircle.backgroundColor = .clear

        innerCircle.backgroundColor = normalColor

        updateAppearance()
    }

    override func layoutSubviews() {
        super.layoutSubviews()

        outerCircle.frame = bounds
        outerCircle.layer.cornerRadius = bounds.width / 2

        let innerSize = bounds.width * 0.3
        let innerOrigin = (bounds.width - innerSize) / 2
        innerCircle.frame = CGRect(x: innerOrigin, y: innerOrigin, width: innerSize, height: innerSize)
        innerCircle.layer.cornerRadius = innerSize / 2
    }

    private func updateAppearance() {
        if isError {
            outerCircle.layer.borderColor = errorColor.cgColor
            innerCircle.backgroundColor = errorColor
            outerCircle.alpha = 1.0
            innerCircle.alpha = 1.0
        } else if isSelected {
            outerCircle.layer.borderColor = selectedColor.cgColor
            innerCircle.backgroundColor = selectedColor
            outerCircle.alpha = 1.0
            innerCircle.alpha = 1.0
        } else {
            outerCircle.layer.borderColor = normalColor.cgColor
            innerCircle.backgroundColor = normalColor
            outerCircle.alpha = 0.5
            innerCircle.alpha = 0.5
        }
    }
}
