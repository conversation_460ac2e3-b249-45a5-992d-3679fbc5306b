//
//  PatternLockLoginViewController.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/12.
//

import Combine
import Foundation
import UIKit
import LocalAuthentication

class PatternLockLoginViewController: BaseViewController {

    // MARK: - Properties
    private var viewModel: PatternLockLoginViewModel!
    private var cancellables = Set<AnyCancellable>()
    private var onSuccess: (() -> Void)?

    // MARK: - UI Components
    private lazy var backgroundGradientView: UIView = {
        let view = UIView()
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            Colors.themeColor.dynamicColor().cgColor,
            Colors.themeColorBlue.dynamicColor().cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        view.layer.insertSublayer(gradientLayer, at: 0)
        return view
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = Colors.background.dynamicColor()
        view.layer.cornerRadius = 24
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: -2)
        view.layer.shadowRadius = 8
        return view
    }()

    private lazy var lockIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "lock.shield.fill")
        imageView.tintColor = Colors.themeColor.dynamicColor()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("pattern_lock_title")
        label.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        label.textColor = Colors.text.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("pattern_lock_subtitle")
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.textColor = Colors.secondaryText.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var patternLockView: PatternLockView = {
        let view = PatternLockView()
        view.delegate = self
        return view
    }()

    private lazy var forgotPatternLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("forgot_pattern_hint")
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = Colors.secondaryText.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var biometricButton: UIButton = {
        let button = UIButton(type: .system)
        button.backgroundColor = Colors.cardBackground.dynamicColor()
        button.layer.cornerRadius = 30
        button.tintColor = Colors.themeColor.dynamicColor()
        button.addTarget(self, action: #selector(biometricButtonTapped), for: .touchUpInside)
        button.isHidden = true
        return button
    }()

    // MARK: - Initialization
    init(onSuccess: @escaping () -> Void) {
        self.onSuccess = onSuccess
        super.init(nibName: nil, bundle: nil)
        self.modalPresentationStyle = .fullScreen
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        setupBiometricButton()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        backgroundGradientView.layer.sublayers?.first?.frame = backgroundGradientView.bounds
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        animateContentView()
    }

    // MARK: - Setup
    func setup(viewModel: PatternLockLoginViewModel) {
        self.viewModel = viewModel
    }

    private func setupUI() {
        view.backgroundColor = .clear

        // Add subviews
        view.addSubview(backgroundGradientView)
        view.addSubview(contentView)

        contentView.addSubview(lockIconView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(subtitleLabel)
        contentView.addSubview(patternLockView)
        contentView.addSubview(forgotPatternLabel)
        contentView.addSubview(biometricButton)

        // Layout
        backgroundGradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.greaterThanOrEqualTo(600)
            make.top.greaterThanOrEqualTo(view.safeAreaLayoutGuide.snp.top).offset(20)
        }

        lockIconView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(60)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(lockIconView.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        patternLockView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(280).priority(.high)
            make.width.height.lessThanOrEqualTo(280)
        }

        biometricButton.snp.makeConstraints { make in
            make.top.equalTo(patternLockView.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(60)
        }

        forgotPatternLabel.snp.makeConstraints { make in
            make.top.equalTo(biometricButton.snp.bottom).offset(15)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.lessThanOrEqualToSuperview().offset(-20)
        }
    }

    private func setupBiometricButton() {
        guard let uid = App.shared.instance?.uid,
            let userSession = DB.shared.loadUserSession(uid: uid),
            userSession.biometricAuth
        else {
            return
        }

        let context = LAContext()
        var error: NSError?

        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        else {
            return
        }

        biometricButton.isHidden = false

        let biometricType = context.biometryType
        switch biometricType {
        case .faceID:
            biometricButton.setImage(UIImage(systemName: "faceid"), for: .normal)
        case .touchID:
            biometricButton.setImage(UIImage(systemName: "touchid"), for: .normal)
        default:
            biometricButton.setImage(UIImage(systemName: "person.badge.key"), for: .normal)
        }
    }

    private func setupBindings() {
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                self?.patternLockView.isUserInteractionEnabled = !isLoading
                self?.patternLockView.alpha = isLoading ? 0.7 : 1.0
            }
            .store(in: &cancellables)

        viewModel.$error
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.showError(error)
                self?.patternLockView.showError()
            }
            .store(in: &cancellables)

        viewModel.$success
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    self?.patternLockView.showSuccess()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        self?.onSuccess?()
                        self?.dismissSafely(animated: true)
                    }
                }
            }
            .store(in: &cancellables)
    }

    private func animateContentView() {
        contentView.transform = CGAffineTransform(translationX: 0, y: 600)

        UIView.animate(
            withDuration: 0.6, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0,
            options: .curveEaseOut
        ) {
            self.contentView.transform = .identity
        }
    }

    private func showError(_ error: Error) {
        let alert = UIAlertController(
            title: XYLocalize.XYLocalize("error"),
            message: error.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: XYLocalize.XYLocalize("ok"), style: .default))
        presentSafely(alert, animated: true)
    }

    // MARK: - Actions
    @objc private func biometricButtonTapped() {
        viewModel.authenticateWithBiometrics()
    }
}

// MARK: - PatternLockViewDelegate
extension PatternLockLoginViewController: PatternLockViewDelegate {
    func patternLockView(_ view: PatternLockView, didCompletePattern pattern: [Int]) {
        let patternString = pattern.map { String($0) }.joined(separator: ",")
        viewModel.verifyPattern(patternString)
    }

    func patternLockView(_ view: PatternLockView, didFailPattern pattern: [Int]) {
        // Pattern too short, already handled by the view
    }
}
