//
//  PatternLockSetupViewController.swift
//  XYZ
//
//  Created by <PERSON> on 2025/1/12.
//

import Combine
import SnapKit
import UIKit

enum PatternLockSetupMode {
    case setup
    case change
    case remove
}

class PatternLockSetupViewController: BaseViewController {

    // MARK: - Properties
    private var mode: PatternLockSetupMode
    private var viewModel: PatternLockSetupViewModel!
    private var cancellables = Set<AnyCancellable>()

    // Pattern setup state
    private var currentStep: PatternSetupStep = .enterCurrent
    private var firstPattern: String?
    private var currentPattern: String?

    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        label.textColor = Theme.Colors.text.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.textColor = Theme.Colors.secondaryText.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var patternLockView: PatternLockView = {
        let view = PatternLockView()
        view.delegate = self
        return view
    }()

    private lazy var skipButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(XYLocalize.XYLocalize("skip"), for: .normal)
        button.setTitleColor(Theme.Colors.secondaryText.dynamicColor(), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.addTarget(self, action: #selector(skipButtonTapped), for: .touchUpInside)
        button.isHidden = true
        return button
    }()

    // MARK: - Initialization
    init(mode: PatternLockSetupMode) {
        self.mode = mode
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        configureForMode()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.prefersLargeTitles = false
    }

    // MARK: - Setup
    func setup(viewModel: PatternLockSetupViewModel) {
        self.viewModel = viewModel
    }

    private func setupUI() {
        view.backgroundColor = Theme.Colors.background.dynamicColor()

        // Navigation
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )

        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(titleLabel)
        contentView.addSubview(subtitleLabel)
        contentView.addSubview(patternLockView)
        contentView.addSubview(skipButton)

        // Layout
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(contentView.safeAreaLayoutGuide.snp.top).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(32)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        patternLockView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(40)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(280)
        }

        skipButton.snp.makeConstraints { make in
            make.top.equalTo(patternLockView.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.height.equalTo(44)
            make.bottom.lessThanOrEqualToSuperview().offset(-40)
        }
    }

    private func setupBindings() {
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                self?.patternLockView.isUserInteractionEnabled = !isLoading
                self?.patternLockView.alpha = isLoading ? 0.7 : 1.0
            }
            .store(in: &cancellables)

        viewModel.$error
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.showError(error)
                self?.patternLockView.showError()
            }
            .store(in: &cancellables)

        viewModel.$success
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    self?.patternLockView.showSuccess()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        self?.dismissSafely(animated: true)
                    }
                }
            }
            .store(in: &cancellables)
    }

    private func configureForMode() {
        switch mode {
        case .setup:
            currentStep = .createNew
            titleLabel.text = XYLocalize.XYLocalize("create_pattern_title")
            subtitleLabel.text = XYLocalize.XYLocalize("create_pattern_subtitle")
            skipButton.isHidden = false

        case .change:
            currentStep = .enterCurrent
            titleLabel.text = XYLocalize.XYLocalize("change_pattern_title")
            subtitleLabel.text = XYLocalize.XYLocalize("enter_current_pattern")

        case .remove:
            currentStep = .enterCurrent
            titleLabel.text = XYLocalize.XYLocalize("remove_pattern_title")
            subtitleLabel.text = XYLocalize.XYLocalize("enter_current_pattern")
        }
    }

    private func updateUIForStep() {
        switch currentStep {
        case .enterCurrent:
            titleLabel.text = mode == .change ?
                XYLocalize.XYLocalize("change_pattern_title") :
                XYLocalize.XYLocalize("remove_pattern_title")
            subtitleLabel.text = XYLocalize.XYLocalize("enter_current_pattern")

        case .createNew:
            titleLabel.text = XYLocalize.XYLocalize("create_pattern_title")
            subtitleLabel.text = XYLocalize.XYLocalize("create_pattern_subtitle")

        case .confirmNew:
            titleLabel.text = XYLocalize.XYLocalize("confirm_pattern_title")
            subtitleLabel.text = XYLocalize.XYLocalize("confirm_pattern_subtitle")
        }

        patternLockView.resetPattern()
    }

    private func showError(_ error: Error) {
        let alert = UIAlertController(
            title: XYLocalize.XYLocalize("error"),
            message: error.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: XYLocalize.XYLocalize("ok"), style: .default))
        presentSafely(alert, animated: true)
    }

    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismissSafely(animated: true)
    }

    @objc private func skipButtonTapped() {
        dismissSafely(animated: true)
    }
}

// MARK: - PatternLockViewDelegate
extension PatternLockSetupViewController: PatternLockViewDelegate {
    func patternLockView(_ view: PatternLockView, didCompletePattern pattern: [Int]) {
        let patternString = pattern.map { String($0) }.joined(separator: ",")

        switch currentStep {
        case .enterCurrent:
            currentPattern = patternString
            if mode == .change {
                // Verify current pattern first
                viewModel.verifyCurrentPattern(patternString) { [weak self] success in
                    DispatchQueue.main.async {
                        if success {
                            self?.currentStep = .createNew
                            self?.updateUIForStep()
                        } else {
                            self?.patternLockView.showError()
                        }
                    }
                }
            } else {
                // For remove mode, verify and remove
                viewModel.removePattern(currentPattern: patternString)
            }

        case .createNew:
            firstPattern = patternString
            currentStep = .confirmNew
            updateUIForStep()

        case .confirmNew:
            if firstPattern == patternString {
                // Patterns match, save it
                switch mode {
                case .setup:
                    viewModel.setupPattern(pattern: patternString)
                case .change:
                    viewModel.changePattern(
                        currentPattern: currentPattern ?? "",
                        newPattern: patternString
                    )
                case .remove:
                    break // Not applicable
                }
            } else {
                // Patterns don't match
                viewModel.error = PatternLockError.patternMismatch
                patternLockView.showError()

                // Reset to first step
                firstPattern = nil
                currentStep = .createNew
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.updateUIForStep()
                }
            }
        }
    }

    func patternLockView(_ view: PatternLockView, didFailPattern pattern: [Int]) {
        // Pattern too short, already handled by the view
    }
}

// MARK: - Pattern Setup Steps
private enum PatternSetupStep {
    case enterCurrent
    case createNew
    case confirmNew
}
