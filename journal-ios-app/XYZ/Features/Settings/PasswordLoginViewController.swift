//
//  PasswordLoginViewController.swift
//  XYZ
//
//  Created by <PERSON> on 2025/2/12.
//

import Combine
import Foundation
import UIKit
import LocalAuthentication

class PasswordLoginViewController: BaseViewController {

    // MARK: - Properties
    private var viewModel: PasswordLoginViewModel!
    private var cancellables = Set<AnyCancellable>()
    private var onSuccess: (() -> Void)?

    // MARK: - UI Components
    private lazy var backgroundGradientView: UIView = {
        let view = UIView()
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            Colors.themeColor.dynamicColor().cgColor,
            Colors.themeColorBlue.dynamicColor().cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        view.layer.insertSublayer(gradientLayer, at: 0)
        return view
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = Colors.background.dynamicColor()
        view.layer.cornerRadius = 24
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: -2)
        view.layer.shadowRadius = 8
        return view
    }()

    private lazy var lockIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "lock.shield.fill")
        imageView.tintColor = Colors.themeColor.dynamicColor()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("password_login_title")
        label.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        label.textColor = Colors.text.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("password_login_subtitle")
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.textColor = Colors.secondaryText.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var passwordTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = XYLocalize.XYLocalize("password_placeholder")
        textField.isSecureTextEntry = true
        textField.borderStyle = .none
        textField.backgroundColor = Colors.cardBackground.dynamicColor()
        textField.layer.cornerRadius = 16
        textField.layer.borderWidth = 2
        textField.layer.borderColor = UIColor.clear.cgColor
        textField.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        textField.textColor = Colors.text.dynamicColor()
        textField.textAlignment = .center
        textField.delegate = self

        // Add padding
        let leftPadding = UIView(frame: CGRect(x: 0, y: 0, width: 20, height: 0))
        let rightPadding = UIView(frame: CGRect(x: 0, y: 0, width: 20, height: 0))
        textField.leftView = leftPadding
        textField.leftViewMode = .always
        textField.rightView = rightPadding
        textField.rightViewMode = .always

        return textField
    }()

    private lazy var unlockButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(XYLocalize.XYLocalize("unlock"), for: .normal)
        button.backgroundColor = Colors.themeColorGreen.dynamicColor()
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        button.layer.cornerRadius = 16
        button.layer.shadowColor = Colors.themeColorGreen.dynamicColor().cgColor
        button.layer.shadowOpacity = 0.3
        button.layer.shadowOffset = CGSize(width: 0, height: 4)
        button.layer.shadowRadius = 8
        button.addTarget(self, action: #selector(unlockButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var forgotPasswordLabel: UILabel = {
        let label = UILabel()
        label.text = XYLocalize.XYLocalize("forgot_password_hint")
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = Colors.secondaryText.dynamicColor()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var biometricButton: UIButton = {
        let button = UIButton(type: .system)
        button.backgroundColor = Colors.cardBackground.dynamicColor()
        button.layer.cornerRadius = 30
        button.tintColor = Colors.themeColor.dynamicColor()
        button.addTarget(self, action: #selector(biometricButtonTapped), for: .touchUpInside)
        button.isHidden = true
        return button
    }()

    // MARK: - Initialization
    init(onSuccess: @escaping () -> Void) {
        self.onSuccess = onSuccess
        super.init(nibName: nil, bundle: nil)
        self.modalPresentationStyle = .fullScreen
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        setupBiometricButton()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        backgroundGradientView.layer.sublayers?.first?.frame = backgroundGradientView.bounds
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        passwordTextField.becomeFirstResponder()
        animateContentView()
    }

    // MARK: - Setup
    func setup(viewModel: PasswordLoginViewModel) {
        self.viewModel = viewModel
    }

    private func setupUI() {
        view.backgroundColor = .clear

        // Add subviews
        view.addSubview(backgroundGradientView)
        view.addSubview(contentView)

        contentView.addSubview(lockIconView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(subtitleLabel)
        contentView.addSubview(passwordTextField)
        contentView.addSubview(unlockButton)
        contentView.addSubview(forgotPasswordLabel)
        contentView.addSubview(biometricButton)

        // Layout
        backgroundGradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(500)
        }

        lockIconView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(60)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(lockIconView.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        passwordTextField.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(60)
        }

        unlockButton.snp.makeConstraints { make in
            make.top.equalTo(passwordTextField.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
        }

        biometricButton.snp.makeConstraints { make in
            make.top.equalTo(unlockButton.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(60)
        }

        forgotPasswordLabel.snp.makeConstraints { make in
            make.top.equalTo(biometricButton.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.lessThanOrEqualToSuperview().offset(-40)
        }
    }

    private func setupBiometricButton() {
        guard let uid = App.shared.instance?.uid,
            let userSession = DB.shared.loadUserSession(uid: uid),
            userSession.biometricAuth
        else {
            return
        }

        let context = LAContext()
        var error: NSError?

        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        else {
            return
        }

        biometricButton.isHidden = false

        let biometricType = context.biometryType
        switch biometricType {
        case .faceID:
            biometricButton.setImage(UIImage(systemName: "faceid"), for: .normal)
        case .touchID:
            biometricButton.setImage(UIImage(systemName: "touchid"), for: .normal)
        default:
            biometricButton.setImage(UIImage(systemName: "person.badge.key"), for: .normal)
        }
    }

    private func setupBindings() {
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                self?.unlockButton.isEnabled = !isLoading
                self?.unlockButton.alpha = isLoading ? 0.7 : 1.0
            }
            .store(in: &cancellables)

        viewModel.$error
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.showError(error)
                self?.shakePasswordField()
            }
            .store(in: &cancellables)

        viewModel.$success
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    self?.onSuccess?()
                    self?.dismiss(animated: true)
                }
            }
            .store(in: &cancellables)
    }

    private func animateContentView() {
        contentView.transform = CGAffineTransform(translationX: 0, y: 500)

        UIView.animate(
            withDuration: 0.6, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0,
            options: .curveEaseOut
        ) {
            self.contentView.transform = .identity
        }
    }

    private func shakePasswordField() {
        let animation = CAKeyframeAnimation(keyPath: "transform.translation.x")
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.duration = 0.6
        animation.values = [-20.0, 20.0, -20.0, 20.0, -10.0, 10.0, -5.0, 5.0, 0.0]
        passwordTextField.layer.add(animation, forKey: "shake")

        // Change border color to red temporarily
        passwordTextField.layer.borderColor = Colors.themeColorRed.dynamicColor().cgColor

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            self.passwordTextField.layer.borderColor = UIColor.clear.cgColor
        }
    }

    private func showError(_ error: Error) {
        let alert = UIAlertController(
            title: XYLocalize.XYLocalize("error"),
            message: error.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: XYLocalize.XYLocalize("ok"), style: .default))
        present(alert, animated: true)
    }

    // MARK: - Actions
    @objc private func unlockButtonTapped() {
        let password = passwordTextField.text ?? ""
        viewModel.verifyPassword(password)
    }

    @objc private func biometricButtonTapped() {
        viewModel.authenticateWithBiometrics()
    }
}

// MARK: - UITextFieldDelegate
extension PasswordLoginViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        unlockButtonTapped()
        return true
    }

    func textField(
        _ textField: UITextField, shouldChangeCharactersIn range: NSRange,
        replacementString string: String
    ) -> Bool {
        // Reset border color when user starts typing
        textField.layer.borderColor = UIColor.clear.cgColor
        return true
    }
}
