//
//  WelcomeViewModel.swift

//
//  Created by <PERSON> on 2025/1/8.
//

import Foundation
import SwifterSwift

class WelcomeViewModel {

    let validatePath = "auth/apple/verify"

    func validateAuthCode(data: Data) async throws -> Token {
        return try await APIClient.shared.sendRequestAsync(
            requestModel:
                RequestModel(
                    path: validatePath,
                    parameters: [
                        "code": data.string(encoding: .utf8) ?? ""
                    ])
        )
    }
}
