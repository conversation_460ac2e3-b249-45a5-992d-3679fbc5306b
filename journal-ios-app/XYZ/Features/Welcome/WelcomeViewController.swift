import UIKit
import AuthenticationServices
import SnapKit

class WelcomeViewController: BaseViewController {

    var viewModel = WelcomeViewModel()
    var router = WelcomeRouter()

    // UI Elements
    let descriptionLabel = UILabel()
    let secondaryDescriptionLabel = UILabel()
    let actionButton = UIButton(type: .system)
    let containerView = UIView()  // New container view
    let imageView = UIImageView()

    override func viewDidLoad() {
        super.viewDidLoad()
        hideNavigationBar = true
        setupUI()
    }

    func setupUI() {

        view.backgroundColor = Colors.pageBg.dynamicColor()

        // 2. Set up top image (optional or replace with illustration)
        imageView.image = UIImage(named: "welcome_illustration") // Replace with your image or illustration
        imageView.contentMode = .scaleAspectFill
        view.addSubview(imageView)

        containerView.backgroundColor = Colors.pageBg.dynamicColor()
        containerView.layer.cornerRadius = 30
        containerView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.addSubview(containerView)

        descriptionLabel.text = "Embrace Your Language Journey with Verbivy! 🎉"
        descriptionLabel.font = UIFont.systemFont(ofSize: 30, weight: .bold)
        descriptionLabel.textAlignment = .center
        descriptionLabel.numberOfLines = 0
        descriptionLabel.textColor = Colors.primary.dynamicColor() // Black text for better contrast
        containerView.addSubview(descriptionLabel)

        secondaryDescriptionLabel.text = "Begin an engaging, immersive experience learning new languages. 🌍"
        secondaryDescriptionLabel.font = UIFont.systemFont(ofSize: 18)
        secondaryDescriptionLabel.textAlignment = .center
        secondaryDescriptionLabel.numberOfLines = 0
        secondaryDescriptionLabel.textColor = Colors.primary.dynamicColor()
        containerView.addSubview(secondaryDescriptionLabel)

        // 5. Setup action button
        actionButton.setTitle("Let's Start!", for: .normal)
        actionButton.backgroundColor = Colors.themeColor.dynamicColor()
        actionButton.setTitleColor(UIColor.white, for: .normal)
        actionButton.layer.cornerRadius = 30
        actionButton.titleLabel?.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        actionButton.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        containerView.addSubview(actionButton)

        containerView.setContentHuggingPriority(.required, for: .vertical)
        containerView.snp.makeConstraints { make in
            make.left.equalTo(view) // Add padding from left
            make.right.equalTo(view) // Add padding from right
            make.bottom.equalTo(view.snp.bottom) // Add padding from bottom (you can adjust this)
        }

        // Description Label
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(containerView.snp.top).offset(30) // Space from top of container
            make.centerX.equalTo(containerView)
            make.left.equalTo(containerView).offset(16)
            make.right.equalTo(containerView).offset(-16)
        }

        // Secondary Description Label
        secondaryDescriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(30) // Space below the descriptionLabel
            make.centerX.equalTo(containerView)
            make.left.equalTo(containerView).offset(16)
            make.right.equalTo(containerView).offset(-16)
        }

        // Action Button
        actionButton.snp.makeConstraints { make in
            make.top.equalTo(secondaryDescriptionLabel.snp.bottom).offset(30)
            make.height.equalTo(60)
            make.left.equalTo(containerView.snp.left).offset(16)
            make.right.equalTo(containerView.snp.right).offset(-16)
            make.bottom.equalTo(containerView.safeAreaLayoutGuide.snp.bottom).offset(-30) // Space from bottom of container
        }

        // ImageView (Top section)
        imageView.setContentHuggingPriority(.defaultLow, for: .vertical)
        imageView.snp.makeConstraints { make in
            make.leading.trailing.top.equalTo(view)
            make.bottom.equalTo(containerView.snp.top).offset(30)
        }
    }

    @objc func actionButtonTapped() {
        self.router.goToLogin(fromVc: self)
    }

    override func themeDidChange(interfaceStyle: UIUserInterfaceStyle) {

    }
}
