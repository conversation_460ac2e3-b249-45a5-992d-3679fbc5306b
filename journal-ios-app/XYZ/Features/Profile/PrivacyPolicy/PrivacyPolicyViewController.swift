//
//  PrivacyPolicyViewController.swift
//  XYZ
//
//  Created by AI Assistant on 2023/12/01.
//

import UIKit
import WebKit

class PrivacyPolicyViewController: UIViewController {

    // MARK: - Properties
    private var webView: WKWebView!
    private let activityIndicator = UIActivityIndicatorView(style: .medium)

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        loadPrivacyPolicy()
    }

    // MARK: - UI Setup
    private func setupUI() {
        title = "隐私政策"
        view.backgroundColor = Theme.Colors.background.dynamicColor()

        // 设置返回按钮
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "chevron.left"),
            style: .plain,
            target: self,
            action: #selector(backButtonTapped)
        )

        // 配置WebView
        let webConfiguration = WKWebViewConfiguration()
        webView = WKWebView(frame: .zero, configuration: webConfiguration)
        webView.navigationDelegate = self
        webView.backgroundColor = Theme.Colors.background.dynamicColor()
        webView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(webView)

        // 配置活动指示器
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        activityIndicator.hidesWhenStopped = true
        activityIndicator.color = Theme.Colors.primary.dynamicColor()
        view.addSubview(activityIndicator)

        // 设置约束
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),

            activityIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
    }

    // MARK: - Actions
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }

    // MARK: - Private Methods
    private func loadPrivacyPolicy() {
        activityIndicator.startAnimating()

        // 获取本地HTML文件路径
        let remotePath = JSONFiles.apiEndpoint + "/static/privacy_policy.html"
        if let url = URL(string: remotePath) {
            let request = URLRequest(url: url)
            webView.load(request)
        } else if let htmlPath = Bundle.main.path(forResource: "privacy_policy", ofType: "html") {
            let url = URL(fileURLWithPath: htmlPath)
            let request = URLRequest(url: url)
            webView.load(request)
        } else {
            showErrorAlert(message: "无法加载隐私政策文件")
            activityIndicator.stopAnimating()
        }
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(
            title: "加载错误",
            message: message,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - WKNavigationDelegate
extension PrivacyPolicyViewController: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        activityIndicator.stopAnimating()
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        activityIndicator.stopAnimating()
        showErrorAlert(message: "加载失败: \(error.localizedDescription)")
    }
}
