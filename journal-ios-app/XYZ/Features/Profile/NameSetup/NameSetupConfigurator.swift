import UIKit

class NameSetupConfigurator {
    static func createModule(completion: ((Bool) -> Void)? = nil) -> UIViewController {
        let userService = UserService()
        let viewModel = NameSetupViewModel(userService: userService, completion: completion)
        let viewController = NameSetupViewController()
        viewController.setup(viewModel: viewModel)
        viewController.modalPresentationStyle = .fullScreen
        return viewController
    }
}
