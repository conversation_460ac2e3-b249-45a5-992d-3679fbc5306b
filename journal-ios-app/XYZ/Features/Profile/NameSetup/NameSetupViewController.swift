import UIKit
import SnapKit
import Combine

class NameSetupViewController: UIViewController {
    // MARK: - Properties
    private var viewModel: NameSetupViewModel!
    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI Components
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "设置您的姓名"
        label.textColor = Theme.Colors.primary.dynamicColor()
        label.font = .systemFont(ofSize: 24, weight: .bold)
        label.textAlignment = .center
        return label
    }()

    private let subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "让我们更好地称呼您"
        label.textColor = Theme.Colors.secondary.dynamicColor()
        label.font = .systemFont(ofSize: 16)
        label.textAlignment = .center
        return label
    }()

    private let lastNameTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入您的姓"
        textField.borderStyle = .none
        textField.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        textField.textColor = Theme.Colors.primary.dynamicColor()
        textField.layer.cornerRadius = 8
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 15, height: 0))
        textField.leftViewMode = .always
        return textField
    }()

    private let firstNameTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入您的名"
        textField.borderStyle = .none
        textField.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        textField.textColor = Theme.Colors.primary.dynamicColor()
        textField.layer.cornerRadius = 8
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 15, height: 0))
        textField.leftViewMode = .always
        return textField
    }()

    private let saveButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("保存并继续", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = Theme.Colors.themeColorGreen.dynamicColor()
        button.layer.cornerRadius = 8
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        return button
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
    }

    // MARK: - Setup
    func setup(viewModel: NameSetupViewModel) {
        self.viewModel = viewModel
    }

    private func setupUI() {
        view.backgroundColor = Theme.Colors.pageBg.dynamicColor()

        // Add subviews
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        view.addSubview(lastNameTextField)
        view.addSubview(firstNameTextField)
        view.addSubview(saveButton)

        // Layout constraints
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(60)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(20)
        }

        lastNameTextField.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        firstNameTextField.snp.makeConstraints { make in
            make.top.equalTo(lastNameTextField.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        saveButton.snp.makeConstraints { make in
            make.top.equalTo(firstNameTextField.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        // Add actions
        saveButton.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)

        // Add text field delegates
        lastNameTextField.delegate = self
        firstNameTextField.delegate = self

        // Update button state
        updateSaveButtonState()
    }

    private func setupBindings() {
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                self?.saveButton.isEnabled = !isLoading
                self?.saveButton.alpha = isLoading ? 0.7 : 1.0
            }
            .store(in: &cancellables)

        viewModel.$error
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.showError(error)
            }
            .store(in: &cancellables)
    }

    // MARK: - Actions
    @objc private func saveButtonTapped() {
        guard let lastName = lastNameTextField.text, !lastName.isEmpty,
              let firstName = firstNameTextField.text, !firstName.isEmpty else {
            return
        }

        viewModel.saveName(lastName: lastName, firstName: firstName)
    }

    private func updateSaveButtonState() {
        let isLastNameEmpty = lastNameTextField.text?.isEmpty ?? true
        let isFirstNameEmpty = firstNameTextField.text?.isEmpty ?? true

        saveButton.isEnabled = !isLastNameEmpty && !isFirstNameEmpty
        saveButton.backgroundColor = saveButton.isEnabled ?
            Theme.Colors.themeColorGreen.dynamicColor() :
            Theme.Colors.buttonDisable.dynamicColor()
    }

    private func showError(_ error: Error) {
        let alert = UIAlertController(
            title: "错误",
            message: error.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UITextFieldDelegate
extension NameSetupViewController: UITextFieldDelegate {
    func textFieldDidChangeSelection(_ textField: UITextField) {
        updateSaveButtonState()
    }

    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == lastNameTextField {
            firstNameTextField.becomeFirstResponder()
        } else {
            textField.resignFirstResponder()
            if saveButton.isEnabled {
                saveButtonTapped()
            }
        }
        return true
    }
}
