import Foundation
import Combine

class NameSetupViewModel {
    // MARK: - Properties
    @Published var isLoading = false
    @Published var error: Error?

    private let userService: UserServiceProtocol
    private var completion: ((Bool) -> Void)?

    // MARK: - Initialization
    init(userService: UserServiceProtocol, completion: ((Bool) -> Void)? = nil) {
        self.userService = userService
        self.completion = completion
    }

    // MARK: - Methods
    func saveName(lastName: String, firstName: String) {
        isLoading = true

        Task {
            do {
                try await userService.updateUserName(firstName: firstName, lastName: lastName)

                await MainActor.run {
                    self.isLoading = false
                    self.completion?(true)
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.error = error
                    self.completion?(false)
                }
            }
        }
    }
}
