//
//  ProfileViewController.swift

//
//  Created by <PERSON> on 2025/1/12.
//

import Combine
import CombineCocoa
import Kingfisher
import SnapKit
import SwifterSwift
import UIKit

// Model for Row Item
struct RowItem {
    let title: String
    var accessoryType: UITableViewCell.AccessoryType = .disclosureIndicator
    var switchState: Bool?  // For rows with switches (like Push Notifications, Face ID)
    var buttonTitle: String?  // For rows with buttons (like PIN Code)
    var action: (() -> Void)?  // 添加一个闭包来处理点击事件
}

// Model for Section Item
struct SectionItem {
    let title: String
    var rows: [RowItem]
}

enum ProfileItem: String {
    case profile = "Profile"
    case appearance = "Appearance"
    case settings = "Settings"
    case logout = "Logout"
}

class ProfileViewController: BaseViewController, UITableViewDataSource, UITableViewDelegate {

    private var tableView: UITableView!
    private var sections: [SectionItem] = []

    private var userAvatar: UIImage?
    private var userName: String?

    private let defaultAvatar = UIImage(named: "avatar")

    var viewModel: ProfileViewModel!
    var router: ProfileRouter!

    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI Elements
    private let profileHeaderView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.translatesAutoresizingMaskIntoConstraints = true
        return view
    }()

    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 50
        imageView.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        return imageView
    }()

    private let usernameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 20)
        label.textColor = Theme.Colors.text.dynamicColor()
        label.textAlignment = .center
        return label
    }()

    private let statsView: UIView = {
        let view = UIView()
        view.backgroundColor = Theme.Colors.cardBackground.dynamicColor()
        view.layer.cornerRadius = 10
        view.translatesAutoresizingMaskIntoConstraints = true
        return view
    }()

    private let totalEntriesLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textColor = Theme.Colors.primary.dynamicColor()
        label.textAlignment = .center
        return label
    }()

    private let totalEntriesTitleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = Theme.Colors.secondaryText.dynamicColor()
        label.text = "总日记数"
        label.textAlignment = .center
        return label
    }()

    private let monthEntriesLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textColor = Theme.Colors.primary.dynamicColor()
        label.textAlignment = .center
        return label
    }()

    private let monthEntriesTitleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = Theme.Colors.secondaryText.dynamicColor()
        label.text = "本月日记"
        label.textAlignment = .center
        return label
    }()

    private let streakDaysLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textColor = Theme.Colors.primary.dynamicColor()
        label.textAlignment = .center
        return label
    }()

    private let streakDaysTitleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = Theme.Colors.secondaryText.dynamicColor()
        label.text = "加入天数"
        label.textAlignment = .center
        return label
    }()

    private let loadingView: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.hidesWhenStopped = true
        return indicator
    }()

    // MARK: - 生命周期方法

    override func viewDidLoad() {
        hideNavigationBar = false

        super.viewDidLoad()

        setupUI()
        setupViewModel()
        setupRouter()
        setupTableView()
        setupBindings()
        view.layoutIfNeeded()
    }

    // MARK: - UITableViewDataSource

    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].rows.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let rowItem = sections[indexPath.section].rows[indexPath.row]
        let cell =
            tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath) as? ProfileCell
            ?? UITableViewCell(style: .value1, reuseIdentifier: "Cell")

        if let profileCell = cell as? ProfileCell {
            profileCell.titleLabel.text = rowItem.title
            profileCell.accessoryView = nil
            profileCell.accessoryType = rowItem.accessoryType
            profileCell.selectionStyle = .none

        }
        return cell
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return sections[section].title.isEmpty ? nil : sections[section].title
    }

    // MARK: - UITableViewDelegate

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let rowItem = sections[indexPath.section].rows[indexPath.row]
        rowItem.action?()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        if App.shared.isInGuestMode() {
            updateGuestProfileUI()
        } else {
            self.viewModel.fetchUserProfile()
        }
    }

    // swiftlint:disable:next function_body_length
    private func setupUI() {
        // let background = UIImageView(image: UIImage.background)
        // background.contentMode = .scaleAspectFill
        // profileHeaderView.addSubview(background)
        // background.snp.makeConstraints { make in
        //     make.edges.equalToSuperview()
        // }
        // 添加头像
        profileHeaderView.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(16)
            make.width.height.equalTo(100)
        }

        // 添加用户名
        profileHeaderView.addSubview(usernameLabel)
        usernameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(15)
            make.centerX.equalToSuperview()
            make.width.lessThanOrEqualToSuperview().inset(20).priority(.high)
        }

        // 添加加载指示器
        view.addSubview(loadingView)
        loadingView.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }

    private func setupTableView() {
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .singleLine
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ProfileCell.self, forCellReuseIdentifier: "Cell")
        tableView.showsVerticalScrollIndicator = false
        tableView.setContentInsets()

        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.bottom.equalToSuperview()
        }

        // 设置表格内容
        setupTableViewSections()
    }

    private func setupTableViewSections() {
        var sectionsToAdd: [SectionItem] = []

        // Account section - different for guest vs logged in users
        if App.shared.isInGuestMode() {
            sectionsToAdd.append(
                SectionItem(
                    title: "账户",
                    rows: [
                        RowItem(
                            title: "注册/登录",
                            action: { [weak self] in
                                self?.router.showLoginScreen()
                            })
                    ])
            )
        } else {
            sectionsToAdd.append(
                SectionItem(
                    title: "账户",
                    rows: [
                        RowItem(
                            title: "编辑个人资料",
                            action: { [weak self] in
                                self?.router.navigateToEditProfile()
                            })
                    ])
            )
        }

        // About section - available for all users
        sectionsToAdd.append(
            SectionItem(
                title: "关于",
                rows: [
                    RowItem(
                        title: "隐私政策",
                        action: { [weak self] in
                            self?.router.navigateToPrivacyPolicy()
                        })
                ])
        )

        // Settings section - available for all users
        sectionsToAdd.append(
            SectionItem(
                title: "设置",
                rows: [
                    RowItem(
                        title: "应用设置",
                        action: { [weak self] in
                            self?.router.navigateToSettings()
                        })
                ])
        )

        sections = sectionsToAdd
        tableView.reloadData()
    }

    private func setupBindings() {
        // 监听用户资料变化
        viewModel.$userProfile
            .receive(on: DispatchQueue.main)
            .sink { [weak self] profile in
                guard let profile = profile else { return }
                self?.updateProfileUI(with: profile)
            }
            .store(in: &cancellables)

        // 监听用户头像变化
        viewModel.$userAvatar
            .sink { [weak self] image in
                if let image = image {
                    self?.userAvatar = image
                    self?.updateHeaderView()
                }
            }
            .store(in: &cancellables)

        // 监听用户名变化
        viewModel.$userName
            .sink { [weak self] name in
                self?.userName = name
                self?.updateHeaderView()
            }
            .store(in: &cancellables)

        // 监听登录状态变化
        App.shared.$isLoggedIn
            .receive(on: RunLoop.main)
            .sink { [weak self] _ in
                self?.setupTableViewSections() // Rebuild sections when login status changes
                self?.updateHeaderView()
                self?.popToRoot()
            }
            .store(in: &cancellables)
    }

    private func updateProfileUI(with profile: UserProfile) {
        // 更新用户名
        if let firstName = profile.firstName, let lastName = profile.lastName {
            usernameLabel.text = "\(lastName) \(firstName)"
        } else if let firstName = profile.firstName {
            usernameLabel.text = firstName
        } else if let lastName = profile.lastName {
            usernameLabel.text = lastName
        } else {
            usernameLabel.text = profile.email
        }

        // 更新头像 - 在实际应用中，这里应该加载用户的头像
        // 这里使用默认头像或根据用户名生成的文字头像
        if let picture = profile.picture, !picture.isEmpty {
            avatarImageView.subviews.forEach { $0.removeFromSuperview() }
            avatarImageView.kf.setImage(with: URL(string: picture))
        } else {
            // 使用用户名首字母作为头像
            avatarImageView.image = nil
            avatarImageView.subviews.forEach { $0.removeFromSuperview() }

            let label = UILabel()
            label.text = String((usernameLabel.text?.first ?? "U").uppercased())
            label.font = UIFont.boldSystemFont(ofSize: 40)
            label.textColor = Theme.Colors.primary.dynamicColor()
            label.textAlignment = .center

            avatarImageView.addSubview(label)
            label.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
    }

    private func updateGuestProfileUI() {
        // Set guest user display
        usernameLabel.text = App.shared.userName()

        // Use default guest avatar
        avatarImageView.image = nil
        avatarImageView.subviews.forEach { $0.removeFromSuperview() }

        let label = UILabel()
        label.text = "访"
        label.font = UIFont.boldSystemFont(ofSize: 40)
        label.textColor = Theme.Colors.primary.dynamicColor()
        label.textAlignment = .center

        avatarImageView.addSubview(label)
        label.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func popToRoot() {
        self.navigationController?.popToRootViewController(animated: false)
    }

    private func updateStatsUI(with stats: JournalStats) {
        totalEntriesLabel.text = "\(stats.totalEntries)"
        monthEntriesLabel.text = "\(stats.thisMonthEntries)"
        streakDaysLabel.text = "\(stats.streakDays)"
    }

    // MARK: - 初始化方法

    func setup(viewModel: ProfileViewModel, router: ProfileRouter) {
        self.viewModel = viewModel
        self.router = router
        router.viewController = self
    }

    private func setupViewModel() {
        if viewModel == nil {
            viewModel = ProfileViewModel()
        }
    }

    private func setupRouter() {
        if router == nil {
            router = ProfileRouter(viewController: self)
        }
    }

    // 显示退出确认对话框
    private func showLogoutConfirmation() {
        let alert = UIAlertController(
            title: "退出登录",
            message: "确定要退出登录吗？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(
            UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
                self?.viewModel.logMeOut()
            })

        present(alert, animated: true)
    }

    // 更新头部视图
    private func updateHeaderView() {
        // 重新创建头部视图
        profileHeaderView.frame = CGRect.init(x: 0, y: 0, width: ScreenWidth, height: 160)
        tableView.tableHeaderView = profileHeaderView
    }

    // 已在上面定义了TableView DataSource & Delegate方法

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 55
    }

    @objc func avatarTapped() {
        router.navigateToEditProfile()
    }

    @objc func didUserNameTapped() {
        if App.shared.isInGuestMode() {
            router.showLoginScreen()
        }
        // If user is logged in, do nothing (or could navigate to edit profile)
    }
}
