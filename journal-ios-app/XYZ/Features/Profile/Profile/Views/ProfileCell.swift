//
//  ProfileCell.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/25.
//

import UIKit

class ProfileCell: UITableViewCell {

    var containerView: UIView = {
        let view = UIView()
        return view
    }()

    var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = Colors.textPrimary.dynamicColor()
        return label
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func setupViews() {

        backgroundColor = Colors.cardBackground.dynamicColor()
        layer.cornerRadius = 10
        clipsToBounds = true

        contentView.addSubview(containerView)
        containerView.addSubview(titleLabel)

        containerView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
        }

        titleLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

}
