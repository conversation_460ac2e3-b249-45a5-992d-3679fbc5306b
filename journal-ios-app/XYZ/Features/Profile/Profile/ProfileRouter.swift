import UIKit

class ProfileRouter {
    weak var viewController: UIViewController?

    init(viewController: UIViewController? = nil) {
        self.viewController = viewController
    }

    // MARK: - Navigation Methods
    func navigateToEditProfile() {
        guard let from = viewController else { return }
        let settingsVC = EditProfileConfigurator.configure()
        from.navigationController?.pushViewController(settingsVC, animated: true)
    }

    func showThemeSelector() {
        guard let from = viewController else { return }

        // 在实际应用中，这里应该导航到主题选择器屏幕
        let alert = UIAlertController(
            title: "主题设置",
            message: "选择您喜欢的应用主题",
            preferredStyle: .actionSheet
        )

        alert.addAction(
            UIAlertAction(title: "浅色模式", style: .default) { _ in
                // 设置浅色主题
                ThemeManager.shared.currentThemeMode = .light
            })

        alert.addAction(
            UIAlertAction(title: "深色模式", style: .default) { _ in
                // 设置深色主题
                ThemeManager.shared.currentThemeMode = .dark
            })

        alert.addAction(
            UIAlertAction(title: "跟随系统", style: .default) { _ in
                // 设置跟随系统
                ThemeManager.shared.currentThemeMode = .system
            })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        // 对于iPad，需要设置弹出位置
        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = from.view
            popoverController.sourceRect = CGRect(
                x: from.view.bounds.midX, y: from.view.bounds.midY, width: 0, height: 0)
            popoverController.permittedArrowDirections = []
        }

        from.present(alert, animated: true)
    }

    func navigateToSettings() {
        guard let from = viewController else { return }

        let settingsVC = SettingsConfigurator.configure()
        from.navigationController?.pushViewController(settingsVC, animated: true)
    }

    func showLogoutConfirmation() {
        guard let from = viewController else { return }

        let alert = UIAlertController(
            title: "退出登录",
            message: "确定要退出登录吗？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(
            UIAlertAction(title: "确定", style: .destructive) { [weak from] _ in
                if let profileVC = from as? ProfileViewController {
                    profileVC.viewModel.logMeOut()
                }
            })

        from.present(alert, animated: true)
    }

    func navigateToPrivacyPolicy() {
        guard let from = viewController else { return }

        // 导航到隐私政策页面
        let privacyPolicyVC = PrivacyPolicyConfigurator.configure()
        from.navigationController?.pushViewController(privacyPolicyVC, animated: true)
    }

    func navigateToAbout() {
        guard let from = viewController else { return }
    }

    func showLoginScreen() {
        guard let from = viewController else { return }
        let login = LoginScreenViewController(nibName: nil, bundle: nil)
        let loginNav = NavigationViewController(rootViewController: login)
        loginNav.modalPresentationStyle = .fullScreen
        from.tabBarController?.present(loginNav, animated: true)
    }
}
