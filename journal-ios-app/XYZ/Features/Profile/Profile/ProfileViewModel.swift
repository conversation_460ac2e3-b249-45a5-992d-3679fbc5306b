import Alamofire
import Combine
import Foundation
import UIKit

class ProfileViewModel: ObservableObject {
    // MARK: - Properties
    @Published var userAvatar: UIImage?
    @Published var userName: String = ""
    @Published var userProfile: UserProfile?
//    @Published var journalStats: JournalStats = JournalStats(
//        totalEntries: 0, thisMonthEntries: 0, streakDays: 0)

    @Published var isLoading: Bool = false

    private var cancellables = Set<AnyCancellable>()
    private let userService: UserServiceProtocol

    // MARK: - Initialization
    init(userService: UserServiceProtocol = UserService()) {
        self.userService = userService
        setupBindings()
    }

    private func setupBindings() {
        // Monitor login state changes
        App.shared.$isLoggedIn
            .sink { [weak self] isLoggedIn in
                if isLoggedIn {
                    self?.fetchUserProfile()
                } else {
                    self?.resetUserData()
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Public Methods
    func fetchUserProfile() {
        isLoading = true

        guard (App.shared.instance?.uid) != nil else {
            return
        }
        self.userProfile = App.shared.instance?.user

        Task {
            do {
                let userProfile = try await self.userService.fetchProfile()
                self.userProfile = userProfile
                self.userService.saveProfileInfo(user: userProfile)

//                self.journalStats = try await self.userService.getUserStats()
                self.isLoading = false

            } catch {
                XYLog("\(error)")
                self.isLoading = false
            }
        }
    }

    func updateNotificationSettings(enabled: Bool) {
        Task {
            do {
                let _: Alamofire.Empty = try await APIClient.shared.sendRequestAsync(
                    requestModel: RequestModel(
                        path: "/user/settings/notifications",
                        parameters: ["enabled": enabled],
                        method: .put
                    )
                )
                XYLog("Notification settings updated successfully")
            } catch {
                XYLog("Failed to update notification settings: \(error)")
            }
        }
    }

    func logMeOut() {
        DB.shared.logOut()
        App.shared.isLoggedIn = false
        App.shared.instance = nil
    }

    // MARK: - Private Methods
    private func resetUserData() {
        userName = ""
        userAvatar = nil
        userProfile = nil
//        journalStats = JournalStats(totalEntries: 0, thisMonthEntries: 0, streakDays: 0)
    }
}
