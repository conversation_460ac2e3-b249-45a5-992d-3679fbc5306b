import Combine
import SnapKit
import UIKit

class EditProfileViewController: BaseViewController {

    // MARK: - Properties
    private var viewModel: EditProfileViewModel!
    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .clear
        tableView.separatorColor = Theme.Colors.separator.dynamicColor()
        tableView.register(
            EditProfileTextFieldCell.self,
            forCellReuseIdentifier: EditProfileTextFieldCell.identifier)
        tableView.register(
            EditProfileSelectorCell.self, forCellReuseIdentifier: EditProfileSelectorCell.identifier
        )
        tableView.register(
            EditProfileDateCell.self, forCellReuseIdentifier: EditProfileDateCell.identifier)
        tableView.register(
            EditProfileNumberCell.self, forCellReuseIdentifier: EditProfileNumberCell.identifier)
        return tableView
    }()

    private let saveButton: UIBarButtonItem = {
        let button = UIBarButtonItem(
            title: XYLocalize.XYLocalize("save"), style: .done, target: nil, action: nil)
        return button
    }()

    // MARK: - Data
    private var sections: [EditProfileSection] = []

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupSections()
        setupActions()
        loadUserData()
        setupBindings()
    }

    // MARK: - Setup
    func setup(viewModel: EditProfileViewModel) {
        self.viewModel = viewModel
    }

    private func setupUI() {
        title = XYLocalize.XYLocalize("edit_profile")
        navigationItem.rightBarButtonItem = saveButton

        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupSections() {
        sections = [
            EditProfileSection(rows: [
                EditProfileRow(
                    title: "avatar", type: .avatar, placeholder: nil,
                    value: {
                        return self.viewModel.userProfile?.picture
                    })
            ]),
            EditProfileSection(
                title: nil,
                rows: [
                    EditProfileRow(
                        title: "last_name", type: .textField, placeholder: "请输入姓",
                        value: {
                            return self.viewModel.userProfile?.lastName
                        }),
                    EditProfileRow(
                        title: "first_name", type: .textField, placeholder: "请输入名字",
                        value: {
                            return self.viewModel.userProfile?.firstName
                        }),
                    EditProfileRow(
                        title: "gender", type: .selector,
                        value: {
                            return self.viewModel.getGender()
                        })
                ]
            )
        ]
    }

    private func setupActions() {
        saveButton.target = self
        saveButton.action = #selector(saveButtonTapped)
    }

    private func loadUserData() {
        viewModel.loadUserProfile()
        tableView.reloadData()
    }

    private func setupBindings() {
        viewModel.$updated
            .receive(on: RunLoop.main)
            .sink { [weak self] updated in
                guard let self = self else { return }
                if updated {
                    self.navigationController?.popViewController(animated: true)
                }
            }.store(in: &cancellables)

        viewModel.$isLoading
            .receive(on: RunLoop.main)
            .sink { [weak self] isLoading in
                guard let self = self else { return }
                self.isLoadingParts = isLoading
            }.store(in: &cancellables)

        viewModel.$error
            .receive(on: RunLoop.main)
            .sink { [weak self] error in
                guard let self = self else { return }
                if let error = error {
                    self.error = error
                }
            }.store(in: &cancellables)
    }

    // MARK: - Actions
    @objc private func saveButtonTapped() {
        viewModel.saveUserInfo()
    }

    @objc private func showDatePicker(_ sender: UITapGestureRecognizer) {
        guard let cell = sender.view as? EditProfileDateCell,
            let indexPath = tableView.indexPath(for: cell)
        else {
            return
        }

        let datePicker = UIDatePicker()
        datePicker.datePickerMode = .date
        datePicker.preferredDatePickerStyle = .wheels

        let alert = UIAlertController(
            title: "\n\n\n\n\n\n\n\n\n\n\n", message: nil, preferredStyle: .actionSheet)
        alert.view.addSubview(datePicker)

        datePicker.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(8)
        }

        let doneAction = UIAlertAction(title: "完成", style: .default) { [weak self] _ in
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy/MM/dd"
            let dateString = formatter.string(from: datePicker.date)
            self?.tableView.reloadRows(at: [indexPath], with: .none)
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)

        alert.addAction(doneAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    @objc private func showGenderPicker(_ sender: UITapGestureRecognizer) {
        guard let cell = sender.view as? EditProfileSelectorCell,
            let indexPath = tableView.indexPath(for: cell)
        else { return }

        let alert = UIAlertController(title: "选择性别", message: nil, preferredStyle: .actionSheet)

        let maleAction = UIAlertAction(title: "男", style: .default) { [weak self] _ in
            self?.viewModel.userProfile?.gender = "male"
            self?.tableView.reloadRows(at: [indexPath], with: .none)
        }

        let femaleAction = UIAlertAction(title: "女", style: .default) { [weak self] _ in
            self?.viewModel.userProfile?.gender = "female"
            self?.tableView.reloadRows(at: [indexPath], with: .none)
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)

        alert.addAction(maleAction)
        alert.addAction(femaleAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    override func themeDidChange(interfaceStyle: UIUserInterfaceStyle) {
        super.themeDidChange(interfaceStyle: interfaceStyle)
        tableView.reloadData()
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension EditProfileViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].rows.count
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        if let title = sections[section].title {
            return XYLocalize.XYLocalize(title)
        }
        return nil
    }

    // swiftlint:disable:next function_body_length
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let row = sections[indexPath.section].rows[indexPath.row]
        switch row.type {
        case .avatar:
            let cell =
                tableView.dequeueReusableCell(withIdentifier: EditProfileAvatarCell.identifier)
                as? EditProfileAvatarCell
                ?? EditProfileAvatarCell(
                    style: .default, reuseIdentifier: EditProfileAvatarCell.identifier)
            cell.configure(with: row.value(), selectedImage: viewModel.selectedImage)
            cell.avatarTappedHandler = { [weak self] in
                self?.showAvatarPicker()
            }
            return cell
        case .textField:
            let cell =
            tableView.dequeueReusableCell(
                withIdentifier: EditProfileTextFieldCell.identifier, for: indexPath)
            as! EditProfileTextFieldCell
            cell.configure(
                title: XYLocalize.XYLocalize(row.title), placeholder: row.placeholder ?? "",
                value: row.value())
            cell.textChangedHandler = { [weak self] text in
                guard let self = self else { return }
                if row.title == "first_name" {
                    self.viewModel.userProfile?.firstName = text
                } else if row.title == "last_name" {
                    self.viewModel.userProfile?.lastName = text
                }
            }
            cell.selectionStyle = .none
            return cell
        case .selector:
            let cell =
                tableView.dequeueReusableCell(
                    withIdentifier: EditProfileSelectorCell.identifier, for: indexPath)
                as! EditProfileSelectorCell
            cell.configure(title: XYLocalize.XYLocalize(row.title), value: row.value() ?? "")
            cell.accessoryType = .disclosureIndicator

            // Add tap gesture for gender selector
            if row.title == "gender" {
                let tapGesture = UITapGestureRecognizer(
                    target: self, action: #selector(showGenderPicker(_:)))
                cell.addGestureRecognizer(tapGesture)
            }
            cell.selectionStyle = .none
            return cell

        case .date:
            let cell =
                tableView.dequeueReusableCell(
                    withIdentifier: EditProfileDateCell.identifier, for: indexPath)
                as! EditProfileDateCell
            cell.configure(
                title: XYLocalize.XYLocalize(row.title),
                placeholder: row.placeholder ?? "",
                value: row.value()
            )

            let tapGesture = UITapGestureRecognizer(
                target: self, action: #selector(showDatePicker(_:)))
            cell.addGestureRecognizer(tapGesture)
            cell.selectionStyle = .none

            return cell

        case .number:
            let cell =
                tableView.dequeueReusableCell(
                    withIdentifier: EditProfileNumberCell.identifier, for: indexPath)
                as! EditProfileNumberCell
            cell.configure(
                title: XYLocalize.XYLocalize(row.title), value: row.value() ?? "0",
                unit: row.unit ?? "")
            cell.valueChangedHandler = { [weak self] _ in

            }
            cell.selectionStyle = .none
            return cell
        }
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return section == 0 ? 20 : 10 // 第一个 section 顶部间距更大
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let row = sections[indexPath.section].rows[indexPath.row]
        if row.type == .avatar {
            return 100
        }
        return 55
    }

    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        self.view.endEditing(true)
    }
}

extension EditProfileViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(
        _ picker: UIImagePickerController,
        didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]
    ) {
        picker.dismiss(animated: true)
        var selectedImage: UIImage?
        if let edited = info[.editedImage] as? UIImage {
            selectedImage = edited
        } else if let original = info[.originalImage] as? UIImage {
            selectedImage = original
        }
        guard let image = selectedImage else { return }
        viewModel.selectedImage = image
        tableView.reloadRows(at: [IndexPath(row: 0, section: 0)], with: .none)
    }
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }

    private func showAvatarPicker() {
        let picker = UIImagePickerController()
        picker.sourceType = .photoLibrary
        picker.delegate = self
        picker.allowsEditing = true
        present(picker, animated: true)
    }
}

// MARK: - Supporting Models
struct EditProfileSection {
    var title: String?
    var rows: [EditProfileRow]
}

struct EditProfileRow {
    var title: String
    var type: EditProfileRowType
    var placeholder: String?
    var value: () -> String?
    var unit: String?
}

enum EditProfileRowType {
    case textField
    case selector
    case date
    case number
    case avatar
}

// MARK: - Custom Cells
class EditProfileTextFieldCell: UITableViewCell {
    static let identifier = "EditProfileTextFieldCell"

    var textChangedHandler: ((String?) -> Void)?

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = Theme.Colors.primary.dynamicColor()
        return label
    }()

    private let textField: UITextField = {
        let textField = UITextField()
        textField.textAlignment = .right
        textField.textColor = Theme.Colors.primary.dynamicColor()
        return textField
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = Theme.Colors.cardBackground.dynamicColor()

        contentView.addSubview(titleLabel)
        contentView.addSubview(textField)

        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.equalTo(100)
        }

        textField.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(8)
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }

        textField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
    }

    func configure(title: String, placeholder: String, value: String?) {
        titleLabel.text = title
        textField.placeholder = placeholder
        textField.text = value
    }

    @objc private func textFieldDidChange() {
        textChangedHandler?(textField.text)
    }
}

class EditProfileSelectorCell: UITableViewCell {
    static let identifier = "EditProfileSelectorCell"

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = Theme.Colors.primary.dynamicColor()
        return label
    }()

    private let valueLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .right
        label.textColor = Theme.Colors.primary.dynamicColor()
        return label
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = Theme.Colors.cardBackground.dynamicColor()

        contentView.addSubview(titleLabel)
        contentView.addSubview(valueLabel)

        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.equalTo(100)
        }

        valueLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(8)
            make.trailing.equalToSuperview().offset(-8)
            make.centerY.equalToSuperview()
        }
    }

    func configure(title: String, value: String?) {
        titleLabel.text = title
        valueLabel.text = value
    }
}

class EditProfileDateCell: UITableViewCell {
    static let identifier = "EditProfileDateCell"

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = Theme.Colors.primary.dynamicColor()
        return label
    }()

    private let valueLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .right
        label.textColor = Theme.Colors.primary.dynamicColor()
        return label
    }()

    private let accessoryImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(systemName: "calendar"))
        imageView.tintColor = .gray
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = Theme.Colors.cardBackground.dynamicColor()

        contentView.addSubview(titleLabel)
        contentView.addSubview(valueLabel)
        contentView.addSubview(accessoryImageView)

        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.equalTo(100)
        }

        accessoryImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        valueLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(8)
            make.trailing.equalTo(accessoryImageView.snp.leading).offset(-8)
            make.centerY.equalToSuperview()
        }
    }

    func configure(title: String, placeholder: String, value: String?) {
        titleLabel.text = title
        if let value = value, !value.isEmpty {
            valueLabel.text = value
            valueLabel.textColor = Theme.Colors.primary.dynamicColor()
        } else {
            valueLabel.text = placeholder
            valueLabel.textColor = .placeholderText
        }
    }
}

class EditProfileNumberCell: UITableViewCell {
    static let identifier = "EditProfileNumberCell"

    var valueChangedHandler: ((String) -> Void)?

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = Theme.Colors.primary.dynamicColor()
        return label
    }()

    private let valueLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .right
        label.textColor = Theme.Colors.primary.dynamicColor()
        return label
    }()

    private let stepper: UIStepper = {
        let stepper = UIStepper()
        stepper.minimumValue = 0
        stepper.maximumValue = 100
        stepper.stepValue = 1
        return stepper
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = Theme.Colors.cardBackground.dynamicColor()

        contentView.addSubview(titleLabel)
        contentView.addSubview(valueLabel)
        contentView.addSubview(stepper)

        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.equalTo(100)
        }

        stepper.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(8)
            make.trailing.equalTo(stepper.snp.leading).offset(-8)
            make.centerY.equalToSuperview()
        }

        stepper.addTarget(self, action: #selector(stepperValueChanged), for: .valueChanged)
    }

    func configure(title: String, value: String, unit: String) {
        titleLabel.text = title
        valueLabel.text = "\(value) \(unit)"
        stepper.value = Double(value) ?? 0
    }

    @objc private func stepperValueChanged() {
        let value = Int(stepper.value)
        let unit = valueLabel.text?.components(separatedBy: " ").last ?? ""
        valueLabel.text = "\(value) \(unit)"
        valueChangedHandler?(String(value))
    }
}

class EditProfileAvatarCell: UITableViewCell {
    static let identifier = "EditProfileAvatarCell"
    var avatarTappedHandler: (() -> Void)?
    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 36
        imageView.layer.borderWidth = 1
        imageView.backgroundColor = Theme.Colors.secondary.dynamicColor().withAlphaComponent(0.1)
        return imageView
    }()
    private let cameraIcon: UIImageView = {
        let imageView = UIImageView(image: UIImage(systemName: "camera.fill"))
        imageView.tintColor = Theme.Colors.primary.dynamicColor()
        imageView.contentMode = .scaleAspectFit
        imageView.alpha = 0.8
        return imageView
    }()
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        setupUI()
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    private func setupUI() {
        backgroundColor = Theme.Colors.cardBackground.dynamicColor()

        contentView.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(20)
            make.width.height.equalTo(72)
        }
        avatarImageView.addSubview(cameraIcon)
        cameraIcon.snp.makeConstraints { make in
            make.bottom.right.equalToSuperview().inset(4)
            make.width.height.equalTo(24)
        }
        let tap = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        avatarImageView.isUserInteractionEnabled = true
        avatarImageView.addGestureRecognizer(tap)
    }
    func configure(with urlString: String?, selectedImage: UIImage?) {
        avatarImageView.layer.borderColor = Theme.Colors.separator.dynamicColor().cgColor

        if let selectedImage = selectedImage {
            avatarImageView.image = selectedImage
        } else if let urlString = urlString, let url = URL(string: urlString) {
            avatarImageView.kf.setImage(with: url, placeholder: UIImage(named: "avatar"))
        } else {
            avatarImageView.image = UIImage(named: "avatar")
        }
    }
    @objc private func avatarTapped() {
        avatarTappedHandler?()
    }
}
