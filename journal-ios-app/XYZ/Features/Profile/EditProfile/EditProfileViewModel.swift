import Combine
import UIKit
import Foundation

class EditProfileViewModel: BaseService {
    // MARK: - Properties
    // Replace UserInfo with UserProfile
    @Published var userProfile: UserProfile?
    @Published var updated: Bool = false
    @Published var isLoading = false
    @Published var error: Error?
    @Published var selectedImage: UIImage?

    private let userService: UserServiceProtocol

    // MARK: - Initialization
    init(userService: UserServiceProtocol = UserService()) {
        self.userService = userService
    }

    func loadUserProfile() {
        self.userProfile = App.shared.instance?.user
    }

    // MARK: - Methods
    func saveUserInfo() {

        self.isLoading = true
        // 可根据实际 UI 增加更多字段
        Task { @MainActor in
            do {
                guard var userProfile = self.userProfile else {
                    throw XYError.raw("保存失败，请重试", 0)
                }

                if let image = self.selectedImage {
                    let responses = try await QiniuService.shared.uploadImagesWithProgress([image]) { _ in

                    }
                    if responses.count != 1 {
                        throw XYError.raw("图片上传失败，请重试", 0)
                    }
                    userProfile.picture = responses[0].url
                }

                // loading 提示（可通过 delegate/closure 通知 VC）
                try await self.userService.updateProfile(
                    userProfile: userProfile
                )
                self.saveProfileInfo(user: userProfile)

                self.isLoading = false
                try await Task.sleep(nanoseconds: 1000_000)
                self.updated = true

            } catch {
                self.isLoading = false
                try await Task.sleep(nanoseconds: 1000_000)

                self.updated = false
                self.error = error
            }
        }
    }

    func getGender() -> String {
        switch userProfile?.gender {
        case "male":
            return XYLocalize.XYLocalize("male")
        case "female":
            return XYLocalize.XYLocalize("female")
        default:
            return ""
        }

    }
}
