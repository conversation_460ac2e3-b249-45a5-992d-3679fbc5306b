//
//  NSMutableAttributedString.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/27.
//

import UIKit

extension NSAttributedString {
    func toHTMLString() -> String? {
        let exportOptions: [NSAttributedString.DocumentAttributeKey: Any] = [
            .documentType: NSAttributedString.DocumentType.html,
            .characterEncoding: String.Encoding.utf8.rawValue
        ]
        do {
            let htmlData = try data(
                from: NSRange(location: 0, length: length),
                documentAttributes: exportOptions
            )
            if let htmlString = String(data: htmlData, encoding: .utf8) {
                return htmlString
            }
        } catch {
            XYLog(error)
        }
        return nil
    }

    // reset predefined color according to theme, for example when user save html string when light mode,
    // the basic text color will be #191919, if the user swithed dark mode, the text will be not clear
    // also will not impact user have customized content color, so just replace the predefined color,  #191919,  #ffffff,
    // and keep the customized color
    func resetColorAccordingToTheme() -> NSAttributedString {
        let htmlString = toHTMLString()
        let attributedString = htmlString?.toAttributedString() ?? self
        return NSMutableAttributedString(attributedString: attributedString).setDefaultAttributes()
    }

    func toNSMutableAttributedString() -> NSMutableAttributedString {
        return NSMutableAttributedString(attributedString: self)
    }
}

extension NSMutableAttributedString {

    func setDefaultAttributes() -> NSMutableAttributedString {
        addAttribute(
            .font, value: UIFont.systemFont(ofSize: 16), range: NSRange(location: 0, length: length)
        )
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineHeightMultiple = 1.4
        addAttribute(
            .paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: length))
        return self
    }

    func setTextStyle(
        lineHeight: CGFloat,
        alignment: NSTextAlignment, textColor: UIColor?,
        font: UIFont? = nil ) -> NSMutableAttributedString {
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.lineHeightMultiple = lineHeight
            paragraphStyle.alignment = alignment
            paragraphStyle.lineBreakMode = .byTruncatingTail
            addAttribute(
                .paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: length))
            if let textColor = textColor {
                addAttribute(
                    .foregroundColor, value: textColor, range: NSRange(location: 0, length: length))
            }

            if let font = font {
                addAttribute(
                    .font, value: font, range: NSRange(location: 0, length: length)
                )
            }
            return self
    }
}

extension String {

    func toAttributedString() -> NSAttributedString? {
        // Try to decode as attributed string if possible
        let lightModeColor = "#191919"
        let darkModeColor = "#ffffff"

        var replaced = self

        if ThemeManager.shared.darkOrLight == .dark {
            replaced = self.replacingOccurrences(ofPattern: lightModeColor, withTemplate: darkModeColor)
        } else {
            replaced = self.replacingOccurrences(ofPattern: darkModeColor, withTemplate: lightModeColor)
        }

        if let data = replaced.data(using: .utf8),
            let attributedContent = try? NSAttributedString(
                data: data,
                options: [
                    .documentType: NSAttributedString.DocumentType.html,
                    .characterEncoding: String.Encoding.utf8.rawValue
                ],
                documentAttributes: nil) {
            return attributedContent
        }
        return nil
    }
}
