import UIKit

extension UIColor {

    // MARK: - Dynamic color methods

    /// Creates a dynamic color that changes based on the user interface style
    /// - Parameters:
    ///   - color1: The color to use in light mode
    ///   - color2: The color to use in dark mode
    /// - Returns: A dynamic color that adapts to the user interface style
    public static func dynamicColor(color1: UIColor, color2: UIColor) -> UIColor {
        if #available(iOS 13.0, *) {
            return UIColor { traitCollection in
                switch traitCollection.userInterfaceStyle {
                case .dark:
                    return color2
                default:
                    return color1
                }
            }
        } else {
            // Pre-iOS 13 - just use the light color or respect manual setting
            if Theme.currentThemeMode == .dark {
                return color2
            }
            return color1
        }
    }

    /// Creates a dynamic color using hex strings
    /// - Parameters:
    ///   - color1: The hex string for light mode color
    ///   - color2: The hex string for dark mode color
    /// - Returns: A dynamic color that adapts to the user interface style
    public static func dynamicColorWithHex(color1: String, color2: String) -> UIColor {
        return UIColor.dynamicColor(
            color1: UIColor(hexString: color1)!,
            color2: UIColor(hexString: color2)!
        )
    }

    /// Initializes a UIColor with a hex string
    /// - Parameters:
    ///   - hexString: The hex string representing the color (e.g., "#FF0000" for red)
    ///   - transparency: Optional transparency value (0.0 to 1.0), defaults to 1.0
    /// - Returns: A UIColor if the hex string is valid, nil otherwise
    convenience init?(hexString: String, transparency: CGFloat = 1.0) {
        var hexFormatted = hexString.trimmingCharacters(in: .whitespacesAndNewlines)
        hexFormatted = hexFormatted.replacingOccurrences(of: "#", with: "")

        var rgb: UInt64 = 0

        guard Scanner(string: hexFormatted).scanHexInt64(&rgb) else {
            return nil
        }

        let red: CGFloat
        let green: CGFloat
        let blue: CGFloat

        if hexFormatted.count == 6 {
            red = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
            green = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
            blue = CGFloat(rgb & 0x0000FF) / 255.0
        } else if hexFormatted.count == 8 {
            // Including alpha value in hex
            red = CGFloat((rgb & 0xFF000000) >> 24) / 255.0
            green = CGFloat((rgb & 0x00FF0000) >> 16) / 255.0
            blue = CGFloat((rgb & 0x0000FF00) >> 8) / 255.0
        } else {
            return nil
        }

        self.init(red: red, green: green, blue: blue, alpha: transparency)
    }
}
