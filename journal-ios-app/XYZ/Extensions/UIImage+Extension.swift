//
//  UIImage+Extension.swift

//
//  Created by <PERSON> on 2025/3/14.
//

import UIKit

extension UIImage {
    func withTintColor(_ color: UIColor) -> UIImage {
        // Ensure the image is in template rendering mode
        let templateImage = self.withRenderingMode(.alwaysTemplate)

        // Create a new image with the desired tint color
        UIGraphicsBeginImageContextWithOptions(self.size, false, self.scale)
        color.set()
        templateImage.draw(in: CGRect(origin: .zero, size: self.size))

        // Get the tinted image
        let tintedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return tintedImage ?? self
    }

    // 10MB
    func compressToTargetFileSize(_ targetFileSize: Int = 10 * 1024 * 1024) -> UIImage {
        var compression: CGFloat = 1.0
        var data = self.jpegData(compressionQuality: compression)
        var fileSize = data?.count ?? 0

        while fileSize > targetFileSize && compression > 0.0 {
            compression -= 0.1
            data = self.jpegData(compressionQuality: compression)
            fileSize = data?.count ?? 0
        }

        return UIImage(data: data ?? Data()) ?? self
    }
}
