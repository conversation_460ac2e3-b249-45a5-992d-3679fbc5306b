//
//  UIScrollView+Extension.swift
//  XYZ
//
//  Created by <PERSON> on 2025/5/19.
//

import UIKit

extension UIView {
    func safeBottomInsets() -> CGFloat {
        return Appearance.shared.safeAreaInsets.bottom + 56 + 10
    }
}

extension UIScrollView {
    func setContentInsets() {
        self.contentInset = .init(
            top: 0, left: 0,
            bottom: Appearance.shared.safeAreaInsets.bottom + 56 + 10 , right: 0
        )
    }

    func setBasicContentInsets() {
        self.contentInset = .init(
            top: 0, left: 0,
            bottom: Appearance.shared.safeAreaInsets.bottom + 10, right: 0
        )
    }

    func captureWithFooter(footerText: String) -> UIImage? {
        // Store original frame and offset
        let originalFrame = self.frame
        let originalOffset = self.contentOffset
        let originalBackground = self.backgroundColor

        // Ensure background is not transparent for capture
        self.backgroundColor = .white

        // Calculate total size needed
        let contentSize = self.contentSize
        let footerHeight: CGFloat = 40
        let totalSize = CGSize(width: contentSize.width, height: contentSize.height + footerHeight)

        // Create a container view with proper size
        let containerView = UIView(frame: CGRect(origin: .zero, size: totalSize))
        containerView.backgroundColor = .white

        // First render the scroll view content to an image
        let contentRenderer = UIGraphicsImageRenderer(size: contentSize)
        let contentImage = contentRenderer.image { _ in
            // Move to top before rendering
            self.contentOffset = .zero
            self.layoutIfNeeded()

            // Draw the scroll view content into the context
            self.drawHierarchy(in: CGRect(origin: .zero, size: contentSize), afterScreenUpdates: true)
        }

        // Create an image view with the content image
        let contentImageView = UIImageView(image: contentImage)
        contentImageView.frame = CGRect(origin: .zero, size: contentSize)
        containerView.addSubview(contentImageView)

        // Add footer label
        let label = UILabel(frame: CGRect(x: 0, y: contentSize.height, width: totalSize.width, height: footerHeight))
        label.text = footerText
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .gray
        label.textAlignment = .center
        containerView.addSubview(label)

        // Render the final image
        let renderer = UIGraphicsImageRenderer(size: totalSize)
        let image = renderer.image { context in
            containerView.layer.render(in: context.cgContext)
        }

        // Restore original state
        self.frame = originalFrame
        self.contentOffset = originalOffset
        self.backgroundColor = originalBackground

        return image
    }
}

extension UIStackView {

    func addFooterLabel(footerText: String) -> UILabel {
        let label = UILabel(frame: CGRect(x: 0, y: bounds.height, width: bounds.width - 20, height: 40))
        label.text = footerText
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = .gray
        label.textAlignment = .right
        return label
    }

    func captureStackView(footerText: String) -> UIImage? {
        let boundsWithFooter = CGRect(x: bounds.origin.x, y: bounds.origin.y, width: bounds.width, height: (bounds.height + 40))
        let renderer = UIGraphicsImageRenderer(bounds: boundsWithFooter)
        return renderer.image { context in
            // draw footer label at the bottom of the stack view
            let label = addFooterLabel(footerText: footerText)
            label.layer.render(in: context.cgContext)
            layer.render(in: context.cgContext)
        }
    }
}
