//
//  ViewModel.swift

//
//  Created by <PERSON> on 2025/1/23.
//

import Foundation
import JWTDecode
import Alamofire
import SwifterSwift

protocol BaseService {
    func fetchProfile() async throws -> UserProfile
}

let userProfilePath = "user/profile"

extension BaseService {

    func fetchProfile() async throws -> UserProfile {
        let userProfile: UserProfile = try await APIClient.shared.sendRequestAsync(requestModel: RequestModel(path: userProfilePath, parameters: [:]))
        try await updateUserTimezone()
        return userProfile
    }

    @discardableResult
    func updateProfile(userProfile: UserProfile) async throws -> Empty {
        var parameters: [String: Any]?
        if let data: Data = try? JSONEncoder().encode(userProfile) {
            parameters = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        }
        return try await APIClient.shared.sendRequestAsync(
            requestModel: RequestModel(
                path: userProfilePath,
                parameters: parameters ?? [:], method: .put
            )
        )
    }

    func updateUserTimezone() async throws {
        if let email = App.shared.instance?.user?.email {
            try await self.updateProfile(userProfile: UserProfile(email: email, timezone: getTimeZoneAbbreviation()))
        }
    }

    func saveProfileInfo(user: UserProfile) {
        let uid = user.id ?? 0
        DB.shared.updateUserWithUid(
            uid: Int64(uid), email: user.email,
            firstName: user.firstName,
            lastName: user.lastName, bio: user.bio,
            appleId: user.googleId, googleId: user.googleId,picture: user.picture
        )
        App.shared.instance?.user = user
    }

    func loadCurrentUser() {
        guard let uid = DB.shared.loadLoginUser(), let token = DB.shared.tokenWithUid(uid: uid) else {
            return
        }

        let session = DB.shared.loadUserSession(uid: uid)
        let user = DB.shared.userWithUid(uid: uid)
        App.shared.instance = UserInstance(
            user: user,
            token: token,
            uid: uid,
            isAuthEnabled: session?.biometricAuth ?? false
        )
        PushManager.shared.didUserLogin(uid: uid)

        Task {
            try await updateUserTimezone()
        }
    }

    func parseJWTTokenUid(token: String) -> String? {
        return try? JWTDecode.decode(jwt: token).identifier
    }

    // when auth token is retrieved
    func didObtainAuthToken(token: String, overrideLocal: Bool = true) {

        guard let uidStr = parseJWTTokenUid(token: token),
            let uid = Int64(uidStr) else { return }

        if overrideLocal {
            App.shared.instance = UserInstance(
                token: Token(token: token, type: "Bearer"), uid: uid
            )
            DB.shared.setLoginUser(uid: uid)
        } else {
            App.shared.instance?.token = Token(token: token, type: "Bearer")
        }

        PushManager.shared.didUserLogin(uid: uid)
        DB.shared.updateTokenWithUid(uid: uid, token: token)
    }

    func logOut() {
        PushManager.shared.didUserLogout()
        DB.shared.logOut()
        App.shared.instance = nil
    }
}
