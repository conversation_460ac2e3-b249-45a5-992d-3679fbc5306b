//
//  Foundation.swift

//
//  Created by <PERSON> on 2025/1/21.
//

import UIKit

func getCurrentUserInterfaceStyle() -> UIUserInterfaceStyle {
    if #available(iOS 13.0, *) {
        return UITraitCollection.current.userInterfaceStyle
    } else {
        return .light // Default to light mode for older iOS versions
    }
}

func getTimeZoneAbbreviation() -> Float64 {
    let secondsGMT = TimeZone.current.secondsFromGMT()
    return Float64(secondsGMT)/3600.0
}
