import Foundation
import Combine
import Alamofire

protocol UserServiceProtocol: BaseService {
    func getUserStats() async throws -> JournalStats
    func updateUserName(firstName: String, lastName: String) async throws -> Bool
    func deleteAccount() async throws -> Alamofire.Empty
}

class UserService: UserServiceProtocol {
    private let statsPath = "/journal/stats"
    private let userPath = "/user/profile"
    private let deleteAccountPath = "/user/account"

    func getUserStats() async throws -> JournalStats {
        return try await APIClient.shared.sendRequestAsync(
            requestModel: RequestModel(
                path: statsPath,
                method: .get
            )
        )
    }

    @discardableResult
    func updateUserName(firstName: String, lastName: String) async throws -> Bool {
        return try await APIClient.shared.sendRequestAsync(
            requestModel: RequestModel(
                path: userPath,
                parameters: [
                    "firstName": firstName,
                    "lastName": lastName
                ],
                method: .put
            )
        )
    }

    @discardableResult
    func deleteAccount() async throws -> Alamofire.Empty {
      return try await APIClient.shared.sendRequestAsync(
            requestModel: RequestModel(
                path: deleteAccountPath,
                parameters: [:],
                method: .delete
            )
        )
    }
}
