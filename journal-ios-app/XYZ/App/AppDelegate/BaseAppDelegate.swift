//
//  BaseAppDelegate.swift

//
//  Created by <PERSON> on 2025/1/12.
//

import GoogleSignIn
import GoogleMobileAds
import RevenueCat
import UIKit

class BaseAppDelegate: UIResponder, UIApplicationDelegate, BaseService, PurchasesDelegate {

    var window: UIWindow?

    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
    ) -> Bool {

        DB.shared.setup()

        loadCurrentUser()

        // setUpRevenueCat()
        // checkUserPremium()

        // Initialize AdMob
        AdMobManager.shared.initializeAdMob()

        return true
    }

    func application(
        _ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]
    ) -> Bool {

        var handled: Bool
        handled = GIDSignIn.sharedInstance.handle(url)
        if handled {
            return true
        }

        return false
    }

    func purchaseProducts() {
        Purchases.shared.getOfferings { (offerings, error) in
            guard let offering = offerings?.current else {
                XYLog("No current offering available")
                return
            }

            // Purchase the first package in the current offering
            Purchases.shared.purchase(package: offering.availablePackages[0]) { (_, customerInfo, error, userCancelled) in
                if userCancelled {
                    XYLog("Purchase was cancelled")
                    return
                }

                if let error = error {
                    XYLog("Purchase error: \(error.localizedDescription)")
                    return
                }

                if customerInfo?.entitlements["Pro"]?.isActive == true {
                    XYLog("Purchase successful! User is now Pro")
                }
            }
        }
    }

    func setUpRevenueCat() {
        Purchases.logLevel = .debug
        Purchases.configure(withAPIKey: JSONFiles.revenueCatKey)
        Purchases.shared.delegate = self
    }

    func checkUserPremium() {
        Purchases.shared.getCustomerInfo { (customerInfo, _) in
            let premium = customerInfo?.entitlements["Pro"]?.isActive ?? false
            XYLog(premium)
        }
    }

    func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
        // Handle subscription changes
        let isPro = customerInfo.entitlements["Pro"]?.isActive == true

        if isPro {
            XYLog("User has active Pro subscription")
        } else {
            XYLog("User does not have active Pro subscription")
        }

        // Handle renewals
        if customerInfo.entitlements["Pro"]?.willRenew == true {
            XYLog("Pro subscription will renew")
        }

        // Handle refunds
        if customerInfo.entitlements["Pro"]?.isActive == false {
            XYLog("Pro subscription is no longer active - possibly due to refund")
            // Handle refund logic here
        }
    }

}
