//
//  SceneDelegate.swift

//
//  Created by <PERSON> on 2025/1/7.
//

import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(
        _ scene: UIScene, willConnectTo session: UISceneSession,
        options connectionOptions: UIScene.ConnectionOptions
    ) {
        guard let windowScene = (scene as? UIWindowScene) else { return }

        let initialViewController = TabBarController(nibName: nil, bundle: nil)

        window = UIWindow(windowScene: windowScene)
        window?.rootViewController = initialViewController
        window?.makeKeyAndVisible()

        Appearance.shared.configureApperance(safeAreaInsets: window?.safeAreaInsets ?? .zero)

        _ = ThemeManager.shared

        // Handle URL if app was launched from widget
        if let urlContext = connectionOptions.urlContexts.first {
            handleURL(urlContext.url)
        }
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
    }

    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
    }

    // MARK: - URL Handling

    func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
        guard let url = URLContexts.first?.url else { return }
        handleURL(url)
    }

    private func handleURL(_ url: URL) {
        guard url.scheme == "journal" else { return }

        switch url.host {
        case "new-entry":
            handleNewEntryURL(url)
        default:
            break
        }
    }

    private func handleNewEntryURL(_ url: URL) {
        // Extract prompt from URL parameters
        let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
        let prompt = components?.queryItems?.first(where: { $0.name == "prompt" })?.value

        // Navigate to new journal entry screen
        guard let tabBarController = window?.rootViewController as? TabBarController else {
            print("Failed to get TabBarController")
            return
        }

        // Switch to journal tab (assuming it's the first tab)
        tabBarController.selectedIndex = 0

        // Present new journal entry screen with delay to ensure UI is ready
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            guard let navigationController = tabBarController.selectedViewController as? UINavigationController else {
                print("Failed to get NavigationController")
                return
            }

            // Dismiss any existing modals first
            navigationController.dismiss(animated: false) {
                let journalNewVC = JournalNewViewController()

                // Pre-fill with prompt if available
                if let promptText = prompt {
                    journalNewVC.prefilledPrompt = promptText
                }

                let navController = UINavigationController(rootViewController: journalNewVC)
                navController.modalPresentationStyle = .fullScreen
                navigationController.present(navController, animated: true)

                // Update widget data to track that user opened from widget
                WidgetDataManager.shared.updateWritingStreak()
            }
        }
    }

}
