//
//  UserProfile.swift

//
//  Created by <PERSON> on 2025/3/28.
//

import Foundation

struct UserProfile: Codable {
    let id: Int64?
    let email: String
    var firstName: String?
    var lastName: String?
    let bio: String?
    let createdAt: String?
    let updatedAt: String?
    let appleId: String?
    let googleId: String?
    let timezone: Float64?
    var gender: String?
    var picture: String?

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case firstName
        case lastName
        case bio
        case createdAt
        case updatedAt
        case appleId
        case googleId
        case timezone
        case gender
        case picture
    }

    init(
        id: Int64? = nil, email: String = "",
        firstName: String? = nil, lastName: String? = nil,
        bio: String? = nil, createdAt: String? = nil,
        updatedAt: String? = nil, appleId: String? = nil,
        googleId: String? = nil, timezone: Float64? = nil,
        gender: String? = nil,
        picture: String? = nil
    ) {
        self.id = id
        self.email = email
        self.firstName = firstName
        self.lastName = lastName
        self.bio = bio
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.appleId = appleId
        self.googleId = googleId
        self.timezone = timezone
        self.gender = gender
        self.picture = picture
    }

    // Computed property to get full name
    var name: String {
        if let firstName = firstName, let lastName = lastName {
            return "\(firstName) \(lastName)"
        } else if let firstName = firstName {
            return firstName
        } else if let lastName = lastName {
            return lastName
        } else {
            return ""
        }
    }
}
