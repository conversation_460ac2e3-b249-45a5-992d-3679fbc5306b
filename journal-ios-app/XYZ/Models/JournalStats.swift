//
//  JournalStats.swift
//  XYZ
//
//  Created by <PERSON> on 2025/4/28.
//

import Foundation

// 日记统计模型
struct JournalStats: Codable {
    var totalEntries: Int
    var thisMonthEntries: Int
    var streakDays: Int

    enum CodingKeys: String, CodingKey {
        case totalEntries = "total_journals"
        case thisMonthEntries = "this_month_journals"
        case streakDays = "user_joined_days"
    }
}
