import Foundation

// Define the content types that can appear in a journal
enum ContentItemType: String, Codable {
    case text
    case image
    case video
    case audio
}

enum WidthMode: String {
    case half
    case full

    var multiplier: CGFloat {
        switch self {
        case .half:
            return 0.7
        case .full:
            return 1.0
        }
    }
}

// A single content item in a journal
struct ContentItem: Identifiable, Codable, Equatable {
    var id: String
    var type: ContentItemType
    var content: String  // HTML text or URL string
    var metadata: [String: String]?  // For additional info like dimensions, duration, etc.

    static func == (lhs: ContentItem, rhs: ContentItem) -> Bool {
        return lhs.id == rhs.id &&
               lhs.type == rhs.type &&
               lhs.content == rhs.content &&
               lhs.metadata == rhs.metadata
    }

    var size: CGSize {
        let widthString = metadata?["width"]
        let heightString = metadata?["height"]
        let width = widthString.flatMap { Double($0) }.map { CGFloat($0) } ?? 0
        let height = heightString.flatMap { Double($0) }.map { CGFloat($0) } ?? 0
        return CGSize(width: width, height: height)
    }

    var mode: WidthMode {
        let mode = metadata?["mode"]
        return mode == "half" ? .half: .full
    }

}

struct JournalEntry: Identifiable, Codable, Equatable {
    var id: String
    var title: ContentItem
    var contentSnippet: String
    var contentItems: [ContentItem]  // Mixed content items in sequence

    // Legacy support
    var content: NSAttributedString {
        // Combine all text items
        let textItems = contentItems.filter { $0.type == .text }
        return textItems.compactMap { $0.content.toAttributedString() }
            .reduce(into: NSMutableAttributedString()) { result, next in
                result.append(next)
            }
    }

    // Legacy support
    var images: [String] {
        return contentItems.filter { $0.type == .image }.map { $0.content }
    }

    // Legacy support
    var videos: [String] {
        return contentItems.filter { $0.type == .video }.map { $0.content }
    }

    // Legacy support
    var audios: [String] {
        return contentItems.filter { $0.type == .audio }.map { $0.content }
    }

    var moodEmoji: String
    var weather: String
    var tags: [String]
    var location: String
    var date: Date
    var createdAt: Date
    var updatedAt: Date
    var fontSize: Int
    var lineHeightMultiple: CGFloat
    var textAlignment: Int = 0  // 0 = left, 1 = center, 2 = right, 3 = justified
    var textColor: String = "#191919"  // Default text color

    init(
        id: String = UUID().uuidString,
        title: ContentItem,
        contentItems: [ContentItem],
        contentSnippet: String,
        moodEmoji: String,
        weather: String,
        tags: [String],
        location: String,
        fontSize: Int,
        lineHeightMultiple: CGFloat,
        textAlignment: Int = 0,
        textColor: String = "#191919",
        date: Date = Date(),
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.title = title
        self.contentItems = contentItems
        self.contentSnippet = contentSnippet
        self.moodEmoji = moodEmoji
        self.weather = weather
        self.tags = tags
        self.location = location
        self.fontSize = fontSize
        self.date = date
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.lineHeightMultiple = lineHeightMultiple
        self.textAlignment = textAlignment
        self.textColor = textColor
    }

    static func == (lhs: JournalEntry, rhs: JournalEntry) -> Bool {
        // compare all properties
        return lhs.id == rhs.id && lhs.title == rhs.title && lhs.contentItems == rhs.contentItems && lhs.contentSnippet == rhs.contentSnippet && lhs.moodEmoji == rhs.moodEmoji && lhs.weather == rhs.weather && lhs.tags == rhs.tags && lhs.location == rhs.location && lhs.fontSize == rhs.fontSize && lhs.lineHeightMultiple == rhs.lineHeightMultiple && lhs.textAlignment == rhs.textAlignment && lhs.textColor == rhs.textColor && lhs.date == rhs.date
    }
}
