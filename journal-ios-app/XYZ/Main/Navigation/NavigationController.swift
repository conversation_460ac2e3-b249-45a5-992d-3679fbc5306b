//
//  XYNavigationController.swift

//
//  Created by <PERSON> on 2025/1/13.
//

import UIKit

class NavigationViewController: UINavigationController, UIGestureRecognizerDelegate,
    UINavigationControllerDelegate {

    override func viewDidLoad() {
        super.viewDidLoad()

        self.view.backgroundColor = Theme.Colors.pageBg.dynamicColor()

        self.navigationBar.setBackgroundImage(nil, for: UIBarMetrics.default)
        self.navigationBar.isTranslucent = false
        self.navigationBar.barTintColor = Theme.Colors.pageBg.dynamicColor()
        self.navigationBar.tintColor = Theme.Colors.primary.dynamicColor()

        let font = UIFont.systemFont(ofSize: 18, weight: UIFont.Weight.semibold)
        let color = Theme.Colors.primary.dynamicColor()
        let titleAttributes = [
            NSAttributedString.Key.font: font,
            NSAttributedString.Key.foregroundColor: color
        ]
        self.navigationBar.titleTextAttributes = titleAttributes
        self.navigationBar.shadowImage = UIImage()
        self.interactivePopGestureRecognizer?.delegate = self

        if #available(iOS 15, *) {
            let app = UINavigationBarAppearance.init()
            app.configureWithOpaqueBackground()  // 重置背景和阴影颜色
            app.titleTextAttributes = titleAttributes
            app.backgroundColor = Theme.Colors.pageBg.dynamicColor()  // 设置导航栏背景色
            app.shadowColor = .clear  // 设置导航栏下边界分割线透明
            app.shadowImage = UIImage()  // 设置导航栏下边界分割线透明
            navigationBar.scrollEdgeAppearance = app  // 带scroll滑动的页面
            navigationBar.standardAppearance = app  // 常规页面
        }

        delegate = self
    }

    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }

    override var childForStatusBarStyle: UIViewController? {
        return self.topViewController
    }

    func navigationController(
        _ navigationController: UINavigationController, willShow viewController: UIViewController,
        animated: Bool
    ) {
        if let base = viewController as? BaseViewController {
            if base.navigationController?.isNavigationBarHidden != base.hideNavigationBar {
                base.navigationController?.setNavigationBarHidden(
                    base.hideNavigationBar, animated: animated)
            }
        }
    }
}

class TierNavigationController: NavigationViewController {

    override func navigationController(_ navigationController: UINavigationController, willShow viewController: UIViewController, animated: Bool) {
        super.navigationController(navigationController, willShow: viewController, animated: animated)

        // if viewController is the first viewController, hide tabBar floating bar
        guard let tabBarController = self.tabBarController as? TabBarController else {
            return
        }

        if viewController == self.viewControllers.first {
            // do nothing
        } else {
            tabBarController.hideFloatingTabBar()
        }
    }

    func navigationController(_ navigationController: UINavigationController, didShow viewController: UIViewController, animated: Bool) {

        guard let tabBarController = self.tabBarController as? TabBarController else {
            return
        }

        if viewController == self.viewControllers.first {
            tabBarController.showFloatingTabBar()
        } else {
//            tabBarController.hideFloatingTabBar()
        }
    }
}
