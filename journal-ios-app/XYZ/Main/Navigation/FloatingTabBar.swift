import SnapKit
import UIKit

protocol FloatingTabBarDelegate: AnyObject {
    func floatingTabBar(_ tabBar: FloatingTabBar, didSelect index: Int)
    func floatingTabBarDidTapCreateButton(_ tabBar: FloatingTabBar)
}

class FloatingTabBar: UIView {
    weak var delegate: FloatingTabBarDelegate?
    var selectedIndex: Int = 0 {
        didSet { updateSelection() }
    }

    private let stackView = UIStackView()
    private var buttons: [UIButton] = []
    private let icons = [
        UIImage(named: "home"),
        UIImage(named: "profile")
    ]

    // Add create journal button
    private let createButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "plus"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor(red: 0.2, green: 0.6, blue: 1.0, alpha: 1.0)
        button.layer.cornerRadius = 28
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 4)
        button.layer.shadowRadius = 8
        button.layer.shadowOpacity = 0.3
        return button
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .clear

        stackView.axis = .horizontal
        stackView.distribution = .equalSpacing
        stackView.alignment = .fill
        stackView.spacing = 0
        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(30)
            make.bottom.equalToSuperview()
            make.height.equalTo(56)
        }

        for (index, icon) in icons.enumerated() {
            let btn = UIButton(type: .system)
            btn.tintColor = Colors.secondary.dynamicColor()
            btn.setImage(icon, for: .normal)
            btn.tag = index
            btn.addTarget(self, action: #selector(tabTapped(_:)), for: .touchUpInside)
            buttons.append(btn)
            stackView.addArrangedSubview(btn)

            btn.snp.makeConstraints { make in
                make.width.height.equalTo(56)
            }
            addCreateButtonGradient(button: btn)
        }

        updateSelection()

        // Add create button on top of the tab bar
        addSubview(createButton)
        createButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(self.snp.top)
            make.width.height.equalTo(56)
        }
        // Add action to create button
        createButton.addTarget(self, action: #selector(createButtonTapped), for: .touchUpInside)
    }

    private func addCreateButtonGradient(button: UIButton) {
        // Add gradient background to create button
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = CGRect(x: 0, y: 0, width: 56, height: 56)
        gradientLayer.cornerRadius = 28
        gradientLayer.colors = [
            UIColor(red: 1, green: 1, blue: 1, alpha: 0.3).cgColor,
            UIColor(red: 1, green: 1, blue: 1, alpha: 0.3).cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        button.layer.insertSublayer(gradientLayer, at: 0)
    }

    private func updateSelection() {
        for (index, btn) in buttons.enumerated() {
            if index == selectedIndex {
                btn.tintColor = Colors.primary.dynamicColor()
                btn.layer.cornerRadius = 16
            } else {
                btn.tintColor = Colors.secondary.dynamicColor()
                btn.backgroundColor = .clear
                btn.layer.cornerRadius = 0
            }
        }
    }

    @objc private func tabTapped(_ sender: UIButton) {
        selectedIndex = sender.tag
        delegate?.floatingTabBar(self, didSelect: sender.tag)
    }

    @objc private func createButtonTapped() {
        delegate?.floatingTabBarDidTapCreateButton(self)
    }
}
