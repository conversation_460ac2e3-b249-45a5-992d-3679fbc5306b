//
//  TabBarController.swift

//
//  Created by <PERSON> on 2025/1/12.
//

import Combine
import SnapKit
import UIKit

class XYTabViewModel: BaseService {}

class TabBarController: UITabBarController {

    var viewModel: XYTabViewModel!
    private var isCheckingLogin = false

    private var cancellables = Set<AnyCancellable>()
    private var floatingTabBar: FloatingTabBar!

    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        setupTabBar()
        setupViewControllers()
        setupFloatingTabBar()
        listenToLoginStatus()

        self.viewModel = XYTabViewModel()
    }

    func setupTabBar() {
        if #available(iOS 18.0, *) {
            setTabBarHidden(true, animated: false)
        } else {
            tabBar.isHidden = true  // Hide system tab bar
        }
    }

    private func setupViewControllers() {
        // 首页 - 日记主页
        let homeVC = JournalHomeViewController()
        homeVC.router = JournalHomeRouter(viewController: homeVC)
        homeVC.viewModel = JournalHomeViewModel()
        homeVC.title = "首页"
        let homeNavController = TierNavigationController(rootViewController: homeVC)

        // 日记列表
        let listVC = JournalListViewController()
        listVC.router = JournalListRouter(viewController: listVC)
        listVC.viewModel = JournalListViewModel()
        listVC.title = "日记列表"
        let listNavController = TierNavigationController(rootViewController: listVC)
        listNavController.tabBarItem = UITabBarItem(
            title: nil,
            image: UIImage.init(named: "track"),
            selectedImage: UIImage.init(named: "track")
        )

        // 日历视图
        let calendarVC = JournalCalendarViewController()
        calendarVC.router = JournalCalendarRouter(viewController: calendarVC)
        calendarVC.viewModel = JournalCalendarViewModel()
        calendarVC.title = "日记日历"
        let calendarNavController = TierNavigationController(rootViewController: calendarVC)

        // 个人资料
        let profileVC = ProfileViewController()  // Placeholder for profile screen
        profileVC.title = "个人资料"
        let profileNavController = TierNavigationController(rootViewController: profileVC)

        viewControllers = [
            homeNavController,
            profileNavController
        ]
    }

    private func setupFloatingTabBar() {
        floatingTabBar = FloatingTabBar()
        view.addSubview(floatingTabBar)
        floatingTabBar.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-10)
            make.height.equalTo(56)
        }
        floatingTabBar.delegate = self
        floatingTabBar.selectedIndex = self.selectedIndex
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        view.bringSubviewToFront(floatingTabBar)
    }

    func listenToLoginStatus() {
        App.shared.$isLoggedIn
            .receive(on: RunLoop.main)
            .delay(for: 0.1, scheduler: RunLoop.main)
            .removeDuplicates() // Only trigger when the value actually changes
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.checkLoginStatus()
            }.store(in: &cancellables)
    }

    func checkLoginStatus() {
        // Prevent multiple simultaneous checks
        guard !isCheckingLogin else { return }
        isCheckingLogin = true

        // If user is already logged in, check for pattern protection
        if let uid = App.shared.instance?.uid, uid > 0 {
            // Check if pattern protection is enabled
            PatternLockProtectionManager.shared.checkPatternProtectionOnAppLaunch(from: self) { [weak self] in
                guard let self = self else { return }
                self.isCheckingLogin = false
            }
            return
        }

        if self.selectedIndex != 0 {
            if let currentNavigation = self.viewControllers?[self.selectedIndex] as? UINavigationController {
                currentNavigation.popToRootViewController(animated: false)
            }
            self.selectedIndex = 0
        }

        // Allow users to access the app without login (guest mode)
        // No longer force login screen presentation
        isCheckingLogin = false
    }

    private func isLoginScreenPresented() -> Bool {
        // Check if login is being presented
        if let presented = self.presentedViewController {
            // Check if it's a navigation controller with login as root
            if let nav = presented as? NavigationViewController,
                nav.topViewController is WelcomePageViewController {
                return true
            }

            // Check if it's directly a login view controller
            if presented is WelcomePageViewController {
                return true
            }

            // Check if it's a pattern login view controller
            if presented is PatternLockLoginViewController {
                return true
            }
        }

        return false
    }

    private func presentLoginScreen() {
        let loginViewController = WelcomePageViewController(nibName: nil, bundle: nil)
        let loginNav = NavigationViewController(rootViewController: loginViewController)
        loginNav.modalPresentationStyle = .fullScreen

        // Use the PresentationManager for safe presentation with critical priority
        self.presentSafely(
            loginNav,
            animated: true,
            priority: .critical
        ) { [weak self] in
            self?.isCheckingLogin = false
        }
    }
}

// MARK: - FloatingTabBarDelegate
extension TabBarController: FloatingTabBarDelegate {
    func floatingTabBarDidTapCreateButton(_ tabBar: FloatingTabBar) {
        JournalHomeRouter(viewController: self).navigateToJournalNew()
    }

    func floatingTabBar(_ tabBar: FloatingTabBar, didSelect index: Int) {
        self.selectedIndex = index
    }
}

extension TabBarController {
    // hide floating tab bar with animation
    func hideFloatingTabBar() {
        UIView.animate(withDuration: 0.3) {
            self.floatingTabBar.snp.updateConstraints { make in
                make.bottom.equalTo(self.view.safeAreaLayoutGuide.snp.bottom).offset(60)
            }
            self.floatingTabBar.alpha = 0
            self.view.layoutIfNeeded()
        } completion: { _ in
            self.floatingTabBar.isHidden = true
        }
    }

    // hide floating tab bar with animation
    func showFloatingTabBar() {
        UIView.animate(withDuration: 0.3) {
            self.floatingTabBar.isHidden = false
            self.floatingTabBar.snp.updateConstraints { make in
                make.bottom.equalTo(self.view.safeAreaLayoutGuide.snp.bottom).offset(-10)
            }
            self.floatingTabBar.alpha = 1
            self.view.layoutIfNeeded()
        } completion: { _ in

        }
    }
}
